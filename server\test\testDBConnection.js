const mongoose = require('mongoose');
require('dotenv').config();

async function testDBConnection() {
  try {
    console.log('测试数据库连接...');
    console.log('连接字符串:', process.env.MONGODB_URI);
    
    // 连接数据库
    await mongoose.connect(process.env.MONGODB_URI, {
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      maxPoolSize: 10,
      minPoolSize: 5,
      maxIdleTimeMS: 30000,
      waitQueueTimeoutMS: 10000
    });
    
    console.log('✅ 数据库连接成功');
    console.log('连接状态:', mongoose.connection.readyState);
    
    // 导入模型
    const DailyStockData = require('../src/models/DailyStockData');
    
    console.log('测试简单查询...');
    
    // 测试简单查询
    const count = await DailyStockData.countDocuments();
    console.log('✅ 数据总数:', count);
    
    // 测试带条件的查询
    console.log('测试带条件查询...');
    const result = await DailyStockData.find({
      peRatioTTM: { $gte: 10, $lte: 30 },
      totalMarketCap: { $gt: 100 }
    }).limit(5).maxTimeMS(30000);
    
    console.log('✅ 筛选结果数量:', result.length);
    if (result.length > 0) {
      console.log('样本数据:', {
        stockCode: result[0].stockCode,
        peRatioTTM: result[0].peRatioTTM,
        totalMarketCap: result[0].totalMarketCap
      });
    }
    
    await mongoose.disconnect();
    console.log('✅ 测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
    process.exit(1);
  }
}

testDBConnection();
