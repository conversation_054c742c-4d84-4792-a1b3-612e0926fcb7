// 基础股票筛选页面
const filterConfig = require('../../../config/filterCondition');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 筛选条件配置
    filterConfig: filterConfig,
    
    // 当前选中的筛选条件组
    activeGroup: 'dailyConditions',
    
    // 用户输入的筛选条件
    filterConditions: {
      dailyConditions: {},
      financialConditions: {},
      basicInfoConditions: {}
    },
    
    // 目标日期
    targetDate: '',
    
    // 日期选择控制（开发者配置：true-允许用户选择日期，false-锁定为昨天）
    enableDateSelection: true, // 控制用户是否可以选择日期
    
    // 弹窗相关
    showModal: false,
    currentField: null,
    currentGroup: null,
    currentFieldConfig: null,
    
    // 临时输入值（用于弹窗中的输入）
    tempValue: null,
    
    // 临时多选状态对象（用于界面高亮显示）
    tempMultiSelectStatus: {},
    
    // 已选指标弹窗
    showSelectedModal: false,
    
    // 已选指标数量
    selectedCount: 0,
    
    // 已选条件列表（用于已选指标弹窗）
    selectedConditionsList: [],
    
    // 我的筛选条件相关
    showMyConditionsModal: false,
    myConditionsList: [],
    
    // 保存条件相关
    showSaveModal: false,
    conditionName: '',
    
    // 重命名条件相关
    showRenameModal: false,
    newConditionName: '',
    currentRenameCondition: null,
    
    // 预处理的分类数据
    conditionClassifications: {},

    // 智能筛选相关
    smartFilterInput: '',
    parseLoading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 设置默认日期为昨天
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const dateStr = this.formatDate(yesterday);
    
    // 初始化分类数据
    const conditionClassifications = this.initConditionClassifications();
    
    this.setData({
      targetDate: dateStr,
      conditionClassifications: conditionClassifications
    });
    
    // 计算已选指标数量
    this.updateSelectedCount();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时重新计算已选指标数量
    this.updateSelectedCount();
  },

  /**
   * 切换筛选条件组
   */
  switchGroup(e) {
    const groupKey = e.currentTarget.dataset.group;
    this.setData({
      activeGroup: groupKey
    });
  },

  /**
   * 点击筛选字段，打开设置弹窗
   */
  openFieldModal(e) {
    const { group, field } = e.currentTarget.dataset;
    const fieldConfig = this.data.filterConfig[group][field];
    const currentValue = this.data.filterConditions[group][field];
    
    // 准备临时值
    let tempValue;
    let tempMultiSelectStatus = {};
    
    if (fieldConfig.type === 'range') {
      tempValue = currentValue || { min: '', max: '' };
    } else if (fieldConfig.type === 'multiSelect') {
      tempValue = currentValue || [];
      // 初始化多选状态对象
      if (fieldConfig.options) {
        fieldConfig.options.forEach(option => {
          tempMultiSelectStatus[option] = tempValue.includes(option);
        });
      }
    } else {
      tempValue = currentValue || false;
    }
    
    this.setData({
      showModal: true,
      currentField: field,
      currentGroup: group,
      currentFieldConfig: fieldConfig,
      tempValue: tempValue,
      tempMultiSelectStatus: tempMultiSelectStatus
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  },

  /**
   * 关闭设置弹窗
   */
  closeModal() {
    this.setData({
      showModal: false,
      currentField: null,
      currentGroup: null,
      currentFieldConfig: null,
      tempValue: null,
      tempMultiSelectStatus: {}
    });
  },

  /**
   * 处理范围输入 - 最小值
   */
  onTempRangeMinInput(e) {
    const value = e.detail.value;
    this.setData({
      'tempValue.min': value
    });
  },

  /**
   * 处理范围输入 - 最大值
   */
  onTempRangeMaxInput(e) {
    const value = e.detail.value;
    this.setData({
      'tempValue.max': value
    });
  },

  /**
   * 处理布尔值切换
   */
  onTempBooleanToggle(e) {
    const checked = e.detail.value;
    this.setData({
      tempValue: checked
    });
  },

  /**
   * 处理多选切换
   */
  onTempMultiSelectToggle(e) {
    const { value } = e.currentTarget.dataset;
    const currentList = this.data.tempValue || [];
    
    let newList;
    if (currentList.includes(value)) {
      // 如果已选中，则取消选中
      newList = currentList.filter(item => item !== value);
    } else {
      // 如果未选中，则添加到选中列表
      newList = [...currentList, value];
    }
    
    // 创建临时状态对象用于界面显示
    const tempStatus = {};
    const currentFieldConfig = this.data.currentFieldConfig;
    if (currentFieldConfig && currentFieldConfig.options) {
      currentFieldConfig.options.forEach(option => {
        tempStatus[option] = newList.includes(option);
      });
    }
    
    this.setData({
      tempValue: newList,
      tempMultiSelectStatus: tempStatus
    });
  },

  /**
   * 确认设置筛选条件
   */
  confirmFieldSetting() {
    const { currentGroup, currentField, tempValue, currentFieldConfig } = this.data;
    
    // 验证数据
    if (currentFieldConfig.type === 'range') {
      const { min, max } = tempValue;
      if (min !== '' && max !== '' && parseFloat(min) > parseFloat(max)) {
        wx.showToast({
          title: '最小值不能大于最大值',
          icon: 'none'
        });
        return;
      }
      
      // 如果都为空，则删除该条件
      if (min === '' && max === '') {
        const updatePath = `filterConditions.${currentGroup}.${currentField}`;
        const newConditions = { ...this.data.filterConditions };
        delete newConditions[currentGroup][currentField];
        this.setData({
          filterConditions: newConditions
        });
      } else {
        const updatePath = `filterConditions.${currentGroup}.${currentField}`;
        this.setData({
          [updatePath]: {
            min: min === '' ? null : parseFloat(min),
            max: max === '' ? null : parseFloat(max)
          }
        });
      }
    } else if (currentFieldConfig.type === 'multiSelect') {
      if (tempValue.length === 0) {
        // 删除该条件
        const newConditions = { ...this.data.filterConditions };
        delete newConditions[currentGroup][currentField];
        this.setData({
          filterConditions: newConditions
        });
      } else {
        const updatePath = `filterConditions.${currentGroup}.${currentField}`;
        this.setData({
          [updatePath]: tempValue
        });
      }
    } else {
      // 布尔值
      if (tempValue) {
        const updatePath = `filterConditions.${currentGroup}.${currentField}`;
        this.setData({
          [updatePath]: tempValue
        });
      } else {
        // 删除该条件
        const newConditions = { ...this.data.filterConditions };
        delete newConditions[currentGroup][currentField];
        this.setData({
          filterConditions: newConditions
        });
      }
    }
    
    this.closeModal();
    this.updateSelectedCount();
  },

  /**
   * 显示已选指标
   */
  showSelectedConditions() {
    this.setData({
      showSelectedModal: true
    });
  },

  /**
   * 关闭已选指标弹窗
   */
  closeSelectedModal() {
    this.setData({
      showSelectedModal: false
    });
  },

  /**
   * 删除单个已选条件
   */
  removeSelectedCondition(e) {
    const { group, field } = e.currentTarget.dataset;
    const newConditions = { ...this.data.filterConditions };
    delete newConditions[group][field];
    
    this.setData({
      filterConditions: newConditions
    });
    
    this.updateSelectedCount();
  },

  /**
   * 编辑已选条件
   */
  editSelectedCondition(e) {
    const { group, field } = e.currentTarget.dataset;
    
    // 关闭已选指标弹窗
    this.setData({
      showSelectedModal: false
    });
    
    // 切换到对应的标签页
    this.setData({
      activeGroup: group
    });
    
    // 打开编辑弹窗
    const fieldConfig = this.data.filterConfig[group][field];
    const currentValue = this.data.filterConditions[group][field];
    
    // 准备临时值
    let tempValue;
    let tempMultiSelectStatus = {};
    
    if (fieldConfig.type === 'range') {
      tempValue = currentValue || { min: '', max: '' };
    } else if (fieldConfig.type === 'multiSelect') {
      tempValue = currentValue || [];
      // 初始化多选状态对象
      if (fieldConfig.options) {
        fieldConfig.options.forEach(option => {
          tempMultiSelectStatus[option] = tempValue.includes(option);
        });
      }
    } else {
      tempValue = currentValue || false;
    }
    
    this.setData({
      showModal: true,
      currentField: field,
      currentGroup: group,
      currentFieldConfig: fieldConfig,
      tempValue: tempValue,
      tempMultiSelectStatus: tempMultiSelectStatus
    });
  },

  /**
   * 处理日期选择
   */
  onDateChange(e) {
    // 只有当允许日期选择时才处理日期变更
    if (this.data.enableDateSelection) {
      this.setData({
        targetDate: e.detail.value
      });
    }
  },

  /**
   * 切换日期选择功能（开发者配置用）
   */
  toggleDateSelection() {
    const newEnableState = !this.data.enableDateSelection;
    
    // 如果禁用日期选择，将日期重置为昨天
    if (!newEnableState) {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const dateStr = this.formatDate(yesterday);
      
      this.setData({
        enableDateSelection: newEnableState,
        targetDate: dateStr
      });
    } else {
      this.setData({
        enableDateSelection: newEnableState
      });
    }
  },

  /**
   * 重置筛选条件
   */
  resetConditions() {
    this.setData({
      filterConditions: {
        dailyConditions: {},
        financialConditions: {},
        basicInfoConditions: {}
      },
      selectedCount: 0
    });
    
    wx.showToast({
      title: '已重置条件',
      icon: 'success',
      duration: 1500
    });
  },

  /**
   * 更新已选指标数量
   */
  updateSelectedCount() {
    const conditions = this.data.filterConditions;
    let count = 0;
    
    for (let groupKey in conditions) {
      count += Object.keys(conditions[groupKey]).length;
    }
    
    // 更新已选条件列表
    const selectedList = this.getSelectedConditionsList();
    
    this.setData({
      selectedCount: count,
      selectedConditionsList: selectedList
    });
  },

  /**
   * 开始查询
   */
  async startQuery() {
    // 检查是否设置了筛选条件
    const hasConditions = this.checkHasConditions();
    
    if (!hasConditions) {
      wx.showToast({
        title: '请设置筛选条件',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '查询中...'
    });

    try {
      // 引入API
      const filterAPI = require('../../../api/filter');
      
      // 准备查询参数
      const queryParams = {
        filterConditions: this.data.filterConditions,
        targetDate: this.data.targetDate
      };
      
      console.log('筛选条件:', queryParams);
      
      // 调用API
      const result = await filterAPI.baseFilterStocks(queryParams.filterConditions, queryParams.targetDate);
      
      wx.hideLoading();
      
      console.log('API响应结果:', result);
      
      if (result && result.data && result.data.stockCodes && result.data.stockCodes.length > 0) {
        // 跳转到结果页面，传递结果数据
        wx.navigateTo({
          url: `/pages/filter/filterResult/filterResult?results=${encodeURIComponent(JSON.stringify(result.data.stockCodes))}&conditions=${encodeURIComponent(JSON.stringify(queryParams.filterConditions))}&targetDate=${encodeURIComponent(this.data.targetDate)}`
        });
      } else {
        wx.showToast({
          title: '未查询到结果',
          icon: 'none',
          duration: 2000
        });
      }
      
    } catch (error) {
      wx.hideLoading();
      console.error('查询失败:', error);
      wx.showToast({
        title: '查询失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 检查是否有筛选条件
   */
  checkHasConditions() {
    const conditions = this.data.filterConditions;
    
    for (let groupKey in conditions) {
      const group = conditions[groupKey];
      if (Object.keys(group).length > 0) {
        return true;
      }
    }
    
    return false;
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 检查字段是否已设置
   */
  isFieldSet(group, field) {
    return this.data.filterConditions[group] && this.data.filterConditions[group][field] !== undefined;
  },

  /**
   * 获取字段显示值
   */
  getFieldDisplayValue(group, field) {
    const value = this.data.filterConditions[group][field];
    const config = this.data.filterConfig[group][field];
    
    if (!value) return '';
    
    if (config.type === 'range') {
      const { min, max } = value;
      if (min !== null && max !== null) {
        return `${min} - ${max}`;
      } else if (min !== null) {
        return `≥ ${min}`;
      } else if (max !== null) {
        return `≤ ${max}`;
      }
    } else if (config.type === 'multiSelect') {
      // return `已选${value.length}项`;
      return value.join(', ');
    } else if (config.type === 'boolean') {
      return value ? '是' : '否';
    }
    
    return '';
  },

  /**
   * 获取已选条件列表（用于已选指标弹窗）
   */
  getSelectedConditionsList() {
    const conditions = this.data.filterConditions;
    const config = this.data.filterConfig;
    const result = [];
    
    for (let groupKey in conditions) {
      const group = conditions[groupKey];
      const groupConfig = this.data.filterConfig.conditionGroups.find(g => g.key === groupKey);
      
      for (let fieldKey in group) {
        const fieldConfig = config[groupKey][fieldKey];
        const value = group[fieldKey];
        
        result.push({
          groupKey,
          groupLabel: groupConfig.label,
          fieldKey,
          fieldLabel: fieldConfig.label,
          value,
          displayValue: this.getFieldDisplayValue(groupKey, fieldKey)
        });
      }
    }
    
    return result;
  },

  /**
   * 初始化筛选条件分类数据
   */
  initConditionClassifications() {
    const classifications = filterConfig.conditionClassification;
    const result = {};
    
    for (let groupKey in classifications) {
      result[groupKey] = [];
      const groupClassifications = classifications[groupKey];
      
      for (let categoryKey in groupClassifications) {
        result[groupKey].push({
          ...groupClassifications[categoryKey],
          categoryKey
        });
      }
    }
    
    return result;
  },

  /**
   * 获取筛选条件分类信息
   */
  getConditionClassifications(groupKey) {
    const classifications = this.data.filterConfig.conditionClassification;
    if (!classifications || !classifications[groupKey]) {
      return [];
    }
    
    const groupClassifications = classifications[groupKey];
    const result = [];
    
    for (let categoryKey in groupClassifications) {
      result.push({
        ...groupClassifications[categoryKey],
        categoryKey
      });
    }
    
    return result;
  },

  /**
   * 显示我的筛选条件
   */
  async showMyConditions() {
    // 检查登录状态
    const app = getApp();
    const isLoggedIn = await app.checkLoginStatus({
      updateUserInfo: false
    });
    
    if (!isLoggedIn) {
      wx.showModal({
        title: '登录提示',
        content: '查看我的筛选条件需要先登录，请选择操作',
        cancelText: '稍后登录',
        confirmText: '立即登录',
        success: (res) => {
          if (res.confirm) {
            // 跳转到登录页面
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
          // 如果选择稍后登录，什么都不做
        }
      });
      return;
    }

    wx.showLoading({
      title: '加载中...'
    });

    try {
      const filterAPI = require('../../../api/filter');
      const result = await filterAPI.getUserFilterConditions();
      
      wx.hideLoading();
      
      if (result && result.success && result.data) {
        // 处理数据，计算每个条件的指标数量和格式化时间
        const formattedList = result.data.map(item => {
          let conditionCount = 0;
          const conditions = item.filterConditions;
          
          // 计算条件数量
          if (conditions.dailyConditions) {
            conditionCount += Object.keys(conditions.dailyConditions).length;
          }
          if (conditions.financialConditions) {
            conditionCount += Object.keys(conditions.financialConditions).length;
          }
          if (conditions.basicInfoConditions) {
            conditionCount += Object.keys(conditions.basicInfoConditions).length;
          }
          
          return {
            ...item,
            conditionCount,
            createdAt: this.formatDateTime(item.createdAt)
          };
        });
        
        this.setData({
          myConditionsList: formattedList,
          showMyConditionsModal: true
        });
      } else {
        this.setData({
          myConditionsList: [],
          showMyConditionsModal: true
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('获取筛选条件失败:', error);
      wx.showToast({
        title: '获取条件失败',
        icon: 'none'
      });
    }
  },

  /**
   * 关闭我的筛选条件弹窗
   */
  closeMyConditionsModal() {
    this.setData({
      showMyConditionsModal: false
    });
  },

  /**
   * 保存筛选条件
   */
  async saveConditions() {
    // 检查是否有筛选条件
    if (!this.checkHasConditions()) {
      wx.showToast({
        title: '请先设置筛选条件',
        icon: 'none'
      });
      return;
    }

    // 检查登录状态
    const app = getApp();
    const isLoggedIn = await app.checkLoginStatus({
      updateUserInfo: false
    });
    
    if (!isLoggedIn) {
      wx.showModal({
        title: '登录提示',
        content: '保存筛选条件需要先登录，请选择操作',
        cancelText: '稍后登录',
        confirmText: '立即登录',
        success: (res) => {
          if (res.confirm) {
            // 跳转到登录页面
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
          // 如果选择稍后登录，什么都不做
        }
      });
      return;
    }

    // 关闭已选指标弹窗，显示保存条件弹窗
    this.setData({
      showSelectedModal: false,
      showSaveModal: true,
      conditionName: ''
    });
  },

  /**
   * 关闭保存条件弹窗
   */
  closeSaveModal() {
    this.setData({
      showSaveModal: false,
      conditionName: ''
    });
  },

  /**
   * 处理条件名称输入
   */
  onConditionNameInput(e) {
    this.setData({
      conditionName: e.detail.value
    });
  },

  /**
   * 确认保存条件
   */
  async confirmSaveCondition() {
    const conditionName = this.data.conditionName.trim();
    
    if (!conditionName) {
      wx.showToast({
        title: '请输入条件名称',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '保存中...'
    });

    try {
      const filterAPI = require('../../../api/filter');
      const result = await filterAPI.saveFilterCondition(conditionName, this.data.filterConditions);
      
      wx.hideLoading();
      
      if (result && result.success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
        
        this.setData({
          showSaveModal: false,
          conditionName: ''
        });
      } else {
        wx.showToast({
          title: result.message || '保存失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('保存筛选条件失败:', error);
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 导入筛选条件
   */
  importCondition(e) {
    const condition = e.currentTarget.dataset.condition;
    
    wx.showModal({
      title: '确认导入',
      content: '导入此筛选条件将覆盖当前已选的筛选指标，是否继续？',
      success: (res) => {
        if (res.confirm) {
          // 导入筛选条件
          const importedConditions = condition.filterConditions;
          
          this.setData({
            filterConditions: {
              dailyConditions: importedConditions.dailyConditions || {},
              financialConditions: importedConditions.financialConditions || {},
              basicInfoConditions: importedConditions.basicInfoConditions || {}
            },
            showMyConditionsModal: false
          });
          
          // 更新已选指标数量
          this.updateSelectedCount();
          
          wx.showToast({
            title: '导入成功',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 使用条件查询结果
   */
  async queryWithCondition(e) {
    const condition = e.currentTarget.dataset.condition;
    
    wx.showLoading({
      title: '查询中...'
    });

    try {
      const filterAPI = require('../../../api/filter');
      const result = await filterAPI.baseFilterStocks(condition.filterConditions, this.data.targetDate);
      
      wx.hideLoading();
      
      if (result && result.data && result.data.stockCodes && result.data.stockCodes.length > 0) {
        // 关闭弹窗并跳转到结果页面
        this.setData({
          showMyConditionsModal: false
        });
        
        wx.navigateTo({
          url: `/pages/filter/filterResult/filterResult?results=${encodeURIComponent(JSON.stringify(result.data.stockCodes))}&conditions=${encodeURIComponent(JSON.stringify(condition.filterConditions))}&targetDate=${encodeURIComponent(this.data.targetDate)}`
        });
      } else {
        wx.showToast({
          title: '未查询到结果',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('查询失败:', error);
      wx.showToast({
        title: '查询失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 重命名筛选条件
   */
  renameCondition(e) {
    const condition = e.currentTarget.dataset.condition;
    
    this.setData({
      showRenameModal: true,
      newConditionName: condition.name,
      currentRenameCondition: condition
    });
  },

  /**
   * 关闭重命名弹窗
   */
  closeRenameModal() {
    this.setData({
      showRenameModal: false,
      newConditionName: '',
      currentRenameCondition: null
    });
  },

  /**
   * 处理新条件名称输入
   */
  onNewConditionNameInput(e) {
    this.setData({
      newConditionName: e.detail.value
    });
  },

  /**
   * 确认重命名条件
   */
  async confirmRenameCondition() {
    const newName = this.data.newConditionName.trim();
    const condition = this.data.currentRenameCondition;
    
    if (!newName) {
      wx.showToast({
        title: '请输入新名称',
        icon: 'none'
      });
      return;
    }

    if (newName === condition.name) {
      this.closeRenameModal();
      return;
    }

    wx.showLoading({
      title: '重命名中...'
    });

    try {
      const filterAPI = require('../../../api/filter');
      const result = await filterAPI.renameFilterCondition(condition.id, newName);
      
      wx.hideLoading();
      
      if (result && result.success) {
        wx.showToast({
          title: '重命名成功',
          icon: 'success'
        });
        
        // 更新本地数据
        const updatedList = this.data.myConditionsList.map(item => {
          if (item.id === condition.id) {
            return { ...item, name: newName };
          }
          return item;
        });
        
        this.setData({
          myConditionsList: updatedList,
          showRenameModal: false,
          newConditionName: '',
          currentRenameCondition: null
        });
      } else {
        wx.showToast({
          title: result.message || '重命名失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('重命名失败:', error);
      wx.showToast({
        title: '重命名失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 删除筛选条件
   */
  deleteCondition(e) {
    const condition = e.currentTarget.dataset.condition;
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除筛选条件"${condition.name}"吗？此操作不可撤销。`,
      confirmColor: '#E74C3C',
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...'
          });

          try {
            const filterAPI = require('../../../api/filter');
            const result = await filterAPI.deleteFilterCondition(condition.id);
            
            wx.hideLoading();
            
            if (result && result.success) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
              
              // 更新本地数据
              const updatedList = this.data.myConditionsList.filter(item => item.id !== condition.id);
              this.setData({
                myConditionsList: updatedList
              });
            } else {
              wx.showToast({
                title: result.message || '删除失败',
                icon: 'none'
              });
            }
          } catch (error) {
            wx.hideLoading();
            console.error('删除失败:', error);
            wx.showToast({
              title: '删除失败，请重试',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 格式化日期时间
   */
  formatDateTime(dateTimeStr) {
    const date = new Date(dateTimeStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  /**
   * 处理智能筛选输入
   */
  onSmartFilterInput(e) {
    this.setData({
      smartFilterInput: e.detail.value
    });
  },

  /**
   * 使用示例文本
   */
  useExample(e) {
    const example = e.currentTarget.dataset.example;
    this.setData({
      smartFilterInput: example
    });
  },

  /**
   * 解析智能筛选条件
   */
  async parseSmartFilter(e) {
    const inputText = this.data.smartFilterInput.trim();
    
    if (!inputText) {
      wx.showToast({
        title: '请输入筛选条件',
        icon: 'none'
      });
      return;
    }

    // 检查是否在加载中
    if (this.data.parseLoading) {
      console.log('正在解析中，请稍等...');
      return;
    }

    console.log('开始解析智能筛选条件:', inputText);

    this.setData({
      parseLoading: true
    });

    try {
      const filterAPI = require('../../../api/filter');
      const result = await filterAPI.parseFilterCondition(inputText);
      
      this.setData({
        parseLoading: false
      });
      
      console.log('智能解析结果:', result);
      
      if (result && result.success && result.data && result.data.filterConditions) {
        // 成功解析，询问用户是否应用这些条件
        const parsedConditions = result.data.filterConditions;
        const conditionCount = this.countParsedConditions(parsedConditions);
        
        wx.showModal({
          title: '解析成功',
          content: `AI成功解析出 ${conditionCount} 个筛选条件，是否应用到已选指标中？应用后将覆盖当前已选的筛选条件。`,
          confirmText: '应用',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              this.applyParsedConditions(parsedConditions);
            }
          }
        });
      } else {
        wx.showModal({
          title: '解析失败',
          content: result.message || 'AI暂时无法理解您的描述，请尝试换一种表达方式，或使用手动选择筛选条件。',
          showCancel: false,
          confirmText: '知道了'
        });
      }
      
    } catch (error) {
      this.setData({
        parseLoading: false
      });
      
      console.error('智能解析失败:', error);
      wx.showModal({
        title: '解析失败',
        content: '网络异常或服务暂时不可用，请稍后重试。',
        showCancel: false,
        confirmText: '知道了'
      });
    }
  },

  /**
   * 计算解析出的条件数量
   */
  countParsedConditions(parsedConditions) {
    let count = 0;
    
    if (parsedConditions.dailyConditions) {
      count += Object.keys(parsedConditions.dailyConditions).length;
    }
    if (parsedConditions.financialConditions) {
      count += Object.keys(parsedConditions.financialConditions).length;
    }
    if (parsedConditions.basicInfoConditions) {
      count += Object.keys(parsedConditions.basicInfoConditions).length;
    }
    
    return count;
  },

  /**
   * 应用解析出的筛选条件
   */
  applyParsedConditions(parsedConditions) {
    // 合并解析出的条件到当前筛选条件中
    const newFilterConditions = {
      dailyConditions: {
        ...this.data.filterConditions.dailyConditions,
        ...(parsedConditions.dailyConditions || {})
      },
      financialConditions: {
        ...this.data.filterConditions.financialConditions,
        ...(parsedConditions.financialConditions || {})
      },
      basicInfoConditions: {
        ...this.data.filterConditions.basicInfoConditions,
        ...(parsedConditions.basicInfoConditions || {})
      }
    };
    
    this.setData({
      filterConditions: newFilterConditions,
      smartFilterInput: '' // 清空输入框
    });
    
    // 更新已选指标数量
    this.updateSelectedCount();
    
    wx.showToast({
      title: '条件已应用',
      icon: 'success',
      duration: 2000
    });
    
    // 如果当前不在对应的条件组，自动切换到第一个有条件的组
    this.autoSwitchToActiveGroup(parsedConditions);
  },

  /**
   * 自动切换到有新增条件的组
   */
  autoSwitchToActiveGroup(parsedConditions) {
    let targetGroup = this.data.activeGroup;
    
    // 优先显示有新增条件的组
    if (parsedConditions.dailyConditions && Object.keys(parsedConditions.dailyConditions).length > 0) {
      targetGroup = 'dailyConditions';
    } else if (parsedConditions.financialConditions && Object.keys(parsedConditions.financialConditions).length > 0) {
      targetGroup = 'financialConditions';
    } else if (parsedConditions.basicInfoConditions && Object.keys(parsedConditions.basicInfoConditions).length > 0) {
      targetGroup = 'basicInfoConditions';
    }
    
    if (targetGroup !== this.data.activeGroup) {
      this.setData({
        activeGroup: targetGroup
      });
    }
  },

  // ...existing code...
});
