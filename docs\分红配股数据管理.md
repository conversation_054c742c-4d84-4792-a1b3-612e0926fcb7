# 分红配股数据管理

## 概述

本模块用于管理企业的分红配股数据，包括数据模型定义、CSV数据导入和数据查询功能。

## 文件结构

```
server/
├── models/
│   └── DividendData.js          # 分红配股数据模型
├── scripts/
│   └── importDividendData.js    # 数据导入脚本
└── test/
    └── testDividendData.js      # 测试脚本
```

## 数据模型

### DividendData.js

分红配股数据模型包含以下主要字段：

#### 基本信息
- `stockCode`: 证券代码
- `reportPeriod`: 报告期
- `performanceDisclosureDate`: 业绩披露日期

#### 送转股份信息
- `totalStockTransferRatio`: 送转总比例
- `stockBonusRatio`: 送股比例
- `stockTransferRatio`: 转股比例

#### 现金分红信息
- `cashDividendRatio`: 现金分红比例
- `cashDividendDescription`: 现金分红比例描述
- `dividendYield`: 股息率

#### 每股指标
- `earningsPerShare`: 每股收益
- `bookValuePerShare`: 每股净资产
- `reserveFundPerShare`: 每股公积金
- `undistributedProfitPerShare`: 每股未分配利润

#### 财务指标
- `netProfitGrowthYoY`: 净利润同比增长
- `totalShareCapital`: 总股本

#### 重要日期
- `proposalAnnouncementDate`: 预案公告日
- `equityRegistrationDate`: 股权登记日
- `exDividendDate`: 除权除息日

#### 状态信息
- `implementationStatus`: 方案进度
- `latestAnnouncementDate`: 最新公告日期

### 中英文字段映射

模型提供了完整的中英文字段映射功能：

```javascript
// 获取字段映射
const mapping = DividendData.getFieldMapping();

// 获取中文字段名
const chineseName = DividendData.getChineseFieldName('stockCode'); // "证券代码"

// 获取英文字段名
const englishName = DividendData.getEnglishFieldName('证券代码'); // "stockCode"
```

## 数据导入

### 使用方法

1. **直接运行导入脚本**：
```powershell
cd server
node scripts/importDividendData.js
```

2. **在代码中调用**：
```javascript
const { importDividendData } = require('./scripts/importDividendData');

importDividendData()
  .then(result => console.log('导入成功:', result))
  .catch(error => console.error('导入失败:', error));
```

### 导入功能特性

1. **BOM检测和清理**：自动检测CSV文件中的BOM并移除
2. **股票代码格式化**：确保股票代码为6位数字格式（如000001不会变成1）
3. **数据验证**：验证必要字段的完整性
4. **重复数据处理**：自动检测并更新重复数据
5. **错误日志**：将导入错误保存到日志文件
6. **进度显示**：每1000条数据显示一次进度
7. **数据统计**：导入完成后显示详细统计信息

### 导入结果

导入完成后会显示：
- 总处理条数
- 新增条数
- 重复/更新条数
- 错误条数
- 数据库总条数
- 按年份的数据统计

## 数据查询

### 静态方法

```javascript
// 根据股票代码查找分红数据
const records = await DividendData.findByStockCode('600000');

// 获取最新的分红数据
const latest = await DividendData.findLatestByStockCode('600000');

// 根据报告期查找
const periodData = await DividendData.findByReportPeriod('2020-01-01', '2023-12-31');

// 查找高股息率排行榜
const topDividend = await DividendData.findTopDividendYield(2023, 10);

// 查找高送转股票
const topTransfer = await DividendData.findTopStockTransfer(2023, 10);
```

### 实例方法

```javascript
const dividend = await DividendData.findOne({stockCode: '600000'});

// 获取分红类型
const type = dividend.getDividendType(); // "现金分红+送转股"

// 获取格式化的分红描述
const description = dividend.getFormattedDividendDescription();

// 获取格式化的股息率
const yieldStr = dividend.getFormattedDividendYield(); // "3.25%"

// 获取分红摘要
const summary = dividend.getDividendSummary();
```

## 测试

运行测试脚本验证功能：

```powershell
cd server
node test/testDividendData.js
```

测试包括：
1. 数据总量统计
2. 特定股票分红记录查询
3. 高股息率股票排行
4. 高送转股票排行
5. 按年份统计
6. 字段映射测试

## 错误处理

### 导入错误日志

导入过程中的错误会保存到 `server/logs/import_dividend_errors.json`，包含：
- 错误行号
- 原始数据
- 错误信息

### 数据验证

- 证券代码和报告期为必填字段
- 日期字段会自动解析，无效日期设为null
- 数值字段会自动转换，无效数值设为null
- 字符串字段会自动去除首尾空格

## 数据库索引

为提高查询性能，模型创建了以下索引：

1. **复合唯一索引**：`{stockCode: 1, reportPeriod: 1}` - 防止重复数据
2. **排序索引**：`{stockCode: 1, reportPeriod: -1}` - 按股票和时间排序
3. **时间索引**：`{reportPeriod: -1}` - 按时间查询
4. **日期索引**：`{exDividendDate: -1}` - 按除权除息日查询
5. **分红指标索引**：现金分红比例、股息率等

## 注意事项

1. **数据库连接**：确保MongoDB服务已启动，连接字符串为 `mongodb://localhost:27017/investment_ai`
2. **CSV文件路径**：导入脚本会自动查找 `data_gp/分红配股数据汇总.csv`
3. **股票代码格式**：系统会自动将数字股票代码补齐为6位（如1 -> 000001）
4. **重复数据**：相同股票代码和报告期的记录会被更新而不是创建新记录
5. **内存使用**：大文件导入时注意内存使用情况

## 使用示例

### 导入数据
```powershell
cd d:\investment-AI\server
node scripts/importDividendData.js
```

### 测试查询
```powershell
cd d:\investment-AI\server
node test/testDividendData.js
```

### 在应用中使用
```javascript
const DividendData = require('./models/DividendData');

// 查询某股票的分红历史
const dividendHistory = await DividendData.findByStockCode('600036');

// 查询高股息股票
const highDividendStocks = await DividendData.findTopDividendYield(2023, 20);
```
