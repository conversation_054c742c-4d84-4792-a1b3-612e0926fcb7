# 股票信息展示功能说明

## 功能概述
用户在筛选结果页面点击证券代码时，可以跳转到股票详情页面查看该股票的详细信息。

## 页面结构

### 1. 页面布局
- **顶部固定区域**: 显示证券代码和证券简称
- **基本信息卡片**: 固定显示股票的基本信息（证券代码、简称、板块、行业、上市时间）
- **数据类型切换**: 通过标签页切换不同类型的数据
- **数据展示区域**: 动态显示选中标签页的数据内容
- **底部操作栏**: 返回和刷新功能

### 2. 数据类型支持
根据API返回的数据动态生成标签页，支持的数据类型包括：
- **日行情** (dailyData): 显示当日股价、涨跌幅、成交量等
- **财务数据** (financialData): 显示财务指标
- **技术指标** (technicalData): 显示技术分析指标
- **基本面** (fundamentalData): 显示基本面数据
- **市场数据** (marketData): 显示市场相关数据

### 3. 特殊处理
- **涨跌数据**: 自动添加颜色标识（红涨绿跌）
- **数值格式化**: 大数值自动转换为万、亿单位
- **日期格式化**: 自动格式化日期显示
- **动态标题**: 根据数据类型显示不同的副标题信息

## 文件结构
```
pages/query/stockInfo/
├── stockInfo.wxml    # 页面结构
├── stockInfo.js      # 页面逻辑
├── stockInfo.wxss    # 页面样式
└── stockInfo.json    # 页面配置
```

## 使用流程
1. 用户在筛选结果页面查看股票列表
2. 点击任意股票的证券代码（带下划线的蓝色文字）
3. 跳转到股票详情页面
4. 查看基本信息和各类数据
5. 可以切换不同的数据类型标签页
6. 支持下拉刷新和返回操作

## 技术特点
- **响应式设计**: 适配不同屏幕尺寸
- **动态数据处理**: 根据API返回的字段动态生成界面
- **用户体验优化**: 加载状态、空状态、错误处理
- **统一样式**: 使用全局样式变量保持一致性
