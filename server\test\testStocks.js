const mongoose = require('mongoose');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const Stock = require('../models/Stock');

/**
 * 连接数据库
 */
async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    process.exit(1);
  }
}

/**
 * 测试数据查询功能
 */
async function testQueries() {
  try {
    console.log('\n🔍 开始测试数据查询功能...\n');

    // 1. 总数统计
    const totalCount = await Stock.countDocuments();
    console.log(`📊 数据库中共有 ${totalCount} 只股票`);

    // 2. 查找特定股票
    console.log('\n🔎 查找平安银行 (000001):');
    const pingAn = await Stock.findByCode('000001');
    if (pingAn) {
      console.log(`  ${pingAn.toDisplayString()}`);
      console.log(`  上市时间: ${pingAn.listingDate.toLocaleDateString('zh-CN')}`);
      console.log(`  上市年份: ${pingAn.listingYear}`);
    } else {
      console.log('  未找到该股票');
    }

    // 3. 查找银行业股票
    console.log('\n🏦 银行业股票 (前5只):');
    const bankStocks = await Stock.findByIndustry('银行').limit(5);
    bankStocks.forEach(stock => {
      console.log(`  ${stock.toDisplayString()}`);
    });

    // 4. 查找主板股票统计
    console.log('\n📈 主板股票统计:');
    const mainBoardCount = await Stock.countDocuments({ boardType: '主板' });
    console.log(`  主板股票数量: ${mainBoardCount}`);

    // 5. 按板块统计
    console.log('\n📊 各板块股票数量:');
    const boardStats = await Stock.aggregate([
      { $group: { _id: '$boardType', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    boardStats.forEach(stat => {
      console.log(`  ${stat._id}: ${stat.count} 只`);
    });

    // 6. 按行业统计（前10名）
    console.log('\n🏭 前10大行业股票数量:');
    const industryStats = await Stock.aggregate([
      { $group: { _id: '$industry', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);
    industryStats.forEach(stat => {
      console.log(`  ${stat._id}: ${stat.count} 只`);
    });

    // 7. 最近上市的股票
    console.log('\n🆕 最近上市的10只股票:');
    const recentStocks = await Stock.find()
      .sort({ listingDate: -1 })
      .limit(10);
    recentStocks.forEach(stock => {
      console.log(`  ${stock.stockCode} ${stock.stockName} - ${stock.listingDate.toLocaleDateString('zh-CN')}`);
    });

    // 8. 最早上市的股票
    console.log('\n🏛️ 最早上市的10只股票:');
    const oldestStocks = await Stock.find()
      .sort({ listingDate: 1 })
      .limit(10);
    oldestStocks.forEach(stock => {
      console.log(`  ${stock.stockCode} ${stock.stockName} - ${stock.listingDate.toLocaleDateString('zh-CN')}`);
    });

    // 9. 按年份统计上市数量
    console.log('\n📅 近5年上市股票数量:');
    const currentYear = new Date().getFullYear();
    for (let year = currentYear - 4; year <= currentYear; year++) {
      const yearCount = await Stock.countDocuments({
        listingDate: {
          $gte: new Date(year, 0, 1),
          $lt: new Date(year + 1, 0, 1)
        }
      });
      console.log(`  ${year}年: ${yearCount} 只`);
    }

    // 10. 搜索包含特定关键词的股票
    console.log('\n🔍 搜索包含"科技"的股票 (前5只):');
    const techStocks = await Stock.find({
      $or: [
        { stockName: { $regex: '科技', $options: 'i' } },
        { industry: { $regex: '科技', $options: 'i' } }
      ]
    }).limit(5);
    techStocks.forEach(stock => {
      console.log(`  ${stock.toDisplayString()}`);
    });

  } catch (error) {
    console.error('❌ 查询测试失败:', error);
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('🧪 开始测试沪深上市公司数据...');
    
    await connectDB();
    await testQueries();
    
    console.log('\n✅ 所有测试完成!');
    
  } catch (error) {
    console.error('\n❌ 测试过程中发生错误:', error);
  } finally {
    await mongoose.connection.close();
    console.log('📴 数据库连接已关闭');
  }
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testQueries };
