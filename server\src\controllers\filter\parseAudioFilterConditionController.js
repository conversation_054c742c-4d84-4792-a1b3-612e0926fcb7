const { parseAudioFilterCondition } = require('../../services/filter/parseAudioFilterConditionService');

/**
 * 解析语音输入的筛选条件
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
const parseAudioFilterConditionController = async (req, res) => {
  try {
    const startTime = Date.now();
    
    // 从请求体中获取音频数据
    const { audioData, format = 'wav' } = req.body;
    
    console.log(`开始处理语音筛选条件解析请求，音频格式: ${format}`);
    
    // 调用服务解析语音筛选条件
    const result = await parseAudioFilterCondition(audioData, format);
    
    const processingTime = Date.now() - startTime;
    
    console.log(`语音筛选条件解析完成，耗时: ${processingTime}ms`);
    
    // 返回成功响应
    res.status(200).json({
      success: true,
      message: '语音筛选条件解析成功',
      data: {
        recognizedText: result.recognizedText,
        filterConditions: result.filterConditions,
        processingTime: processingTime
      }
    });
    
  } catch (error) {
    console.error('解析语音筛选条件失败:', error);
    
    // 返回错误响应
    let statusCode = 500;
    let errorMessage = '服务器内部错误';
    
    if (error.message.includes('不支持的音频格式') || error.message.includes('无效的音频数据格式')) {
      statusCode = 400;
      errorMessage = error.message;
    } else if (error.message.includes('语音识别结果为空')) {
      statusCode = 400;
      errorMessage = '无法识别语音内容，请重新录音';
    } else if (error.message.includes('科大讯飞API错误')) {
      statusCode = 502;
      errorMessage = '语音识别服务暂时不可用，请稍后重试';
    } else if (error.message.includes('解析筛选条件失败')) {
      statusCode = 400;
      errorMessage = '无法理解语音内容，请用更清晰的表达';
    }
    
    res.status(statusCode).json({
      success: false,
      message: errorMessage,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  parseAudioFilterConditionController
};
