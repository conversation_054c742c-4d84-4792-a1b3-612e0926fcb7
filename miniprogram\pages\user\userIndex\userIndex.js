// pages/user/userIndex/userIndex.js
const auth = require('../../../api/auth');
const storage = require('../../../utils/storage');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户登录状态
    isLoggedIn: false,
    
    // 用户信息
    userInfo: {
      avatarUrl: '',
      nickName: ''
    },
    
    // 页面加载状态
    loading: false,
    
    // 防重复检查标记
    _checkingLoginStatus: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this._hasLoadCompleted = false;
    this.checkLoginStatus().finally(() => {
      this._hasLoadCompleted = true;
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时检查登录状态
    // 但避免与onLoad重复，添加短暂延迟
    if (!this._hasLoadCompleted) {
      // 如果onLoad还没完成，等待一下
      setTimeout(() => {
        this.checkLoginStatus();
      }, 100);
    } else {
      this.checkLoginStatus();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshUserData();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '言策AI量化投资',
      desc: '智能投资，让财富增值更简单',
      path: '/pages/user/userIndex/userIndex'
    };
  },

  /**
   * 检查用户登录状态
   */
  async checkLoginStatus() {
    // 防重复检查
    if (this.data._checkingLoginStatus) {
      console.log('登录状态检查正在进行中，跳过重复检查');
      return;
    }

    try {
      this.setData({ _checkingLoginStatus: true });
      
      const app = getApp();
      
      if (!app) {
        console.error('无法获取应用实例');
        this.setData({ isLoggedIn: false });
        return;
      }

      // 先检查本地是否有完整的用户数据，避免不必要的网络请求
      const localToken = storage.getToken();
      const localUserInfo = storage.getUserInfo();
      
      if (localToken && localUserInfo && app.globalData.isLoggedIn) {
        console.log('使用本地缓存的用户信息');
        this.setData({
          isLoggedIn: true,
          userInfo: {
            avatarUrl: localUserInfo.avatar || '',
            nickName: localUserInfo.nickname || '用户'
          }
        });
        return;
      }

      // 使用全局的登录状态检查方法
      const isLoggedIn = await app.checkLoginStatus({
        showToast: false,
        redirectToLogin: false,
        onSuccess: (userInfo) => {
          console.log('登录状态检查成功:', userInfo);
          // 成功回调：更新页面数据
          this.setData({
            isLoggedIn: true,
            userInfo: {
              avatarUrl: userInfo.avatar || '',
              nickName: userInfo.nickname || '用户'
            }
          });
        },
        onFail: (error) => {
          // 失败回调：更新页面状态
          console.log('登录状态检查失败:', error);
          this.setData({ isLoggedIn: false });
        }
      });

      // 如果全局方法返回false，确保页面状态正确
      if (!isLoggedIn) {
        this.setData({ isLoggedIn: false });
      }
    } catch (error) {
      console.error('检查登录状态异常:', error);
      this.setData({ isLoggedIn: false });
    } finally {
      this.setData({ _checkingLoginStatus: false });
    }
  },

  /**
   * 刷新用户数据
   */
  async refreshUserData() {
    try {
      wx.showNavigationBarLoading();
      await this.checkLoginStatus();
    } catch (error) {
      console.error('刷新用户数据失败:', error);
      wx.showToast({
        title: '刷新失败',
        icon: 'error',
        duration: 2000
      });
    } finally {
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 跳转到登录页面
   */
  navigateToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  /**
   * 处理退出登录
   */
  async handleLogout() {
    try {
      const confirm = await new Promise((resolve) => {
        wx.showModal({
          title: '确认退出',
          content: '确定要退出登录吗？',
          success: (res) => resolve(res.confirm),
          fail: () => resolve(false)
        });
      });

      if (!confirm) return;

      this.setData({ loading: true });

      // 调用后端退出登录接口
      try {
        await auth.logout();
      } catch (error) {
        console.warn('后端退出登录失败:', error);
      }

      // 清除本地存储的token和全局状态
      const app = getApp();
      if (app && app.clearUserLoginStatus) {
        app.clearUserLoginStatus();
      } else {
        wx.removeStorageSync('token');
        wx.removeStorageSync('access_token');
        wx.removeStorageSync('user_info');
        wx.removeStorageSync('userInfo');
      }
      
      // 更新页面状态
      this.setData({
        isLoggedIn: false,
        userInfo: {
          avatarUrl: '',
          nickName: ''
        }
      });

      wx.showToast({
        title: '已退出登录',
        icon: 'success',
        duration: 2000
      });
    } catch (error) {
      console.error('退出登录失败:', error);
      wx.showToast({
        title: '退出登录失败',
        icon: 'error',
        duration: 2000
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 导航到投资组合页面
   */
  navigateToPortfolio() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip();
      return;
    }
    
    wx.navigateTo({
      url: '/pages/portfolio/portfolioIndex/portfolioIndex'
    });
  },

  /**
   * 导航到AI策略页面
   */
  navigateToStrategy() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip();
      return;
    }
    
    wx.navigateTo({
      url: '/pages/strategy/strategyIndex/strategyIndex'
    });
  },

  /**
   * 导航到投资分析页面
   */
  navigateToAnalysis() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip();
      return;
    }
    
    wx.navigateTo({
      url: '/pages/analysis/analysisIndex/analysisIndex'
    });
  },

  /**
   * 导航到设置页面
   */
  navigateToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settingsIndex/settingsIndex'
    });
  },

  /**
   * 导航到帮助页面
   */
  navigateToHelp() {
    wx.navigateTo({
      url: '/pages/help/helpIndex/helpIndex'
    });
  },

  /**
   * 导航到关于页面
   */
  navigateToAbout() {
    wx.navigateTo({
      url: '/pages/about/aboutIndex/aboutIndex'
    });
  },

  /**
   * 显示登录提示并跳转到登录页
   */
  showLoginTip() {
    wx.showModal({
      title: '需要登录',
      content: '请先登录后再使用此功能',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/login/login'
          });
        }
      }
    });
  }
});
