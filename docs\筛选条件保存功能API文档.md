# 筛选条件保存功能 API 文档

## 概述
用户可以保存、查询、删除和重命名自定义的股票筛选条件。

## API 列表

### 1. 保存筛选条件
**POST** `/api/filter/save-condition`

**请求参数：**
```json
{
  "name": "我的筛选条件1",
  "filterConditions": {
    "dailyConditions": {
      "peRatioDynamicRange": { "min": 10, "max": 30 },
      "totalMarketCapRange": { "min": 1000000000, "max": null }
    },
    "financialConditions": {
      "netProfitGrowthRateRange": { "min": 0.1, "max": null }
    },
    "basicInfoConditions": {
      "board": ["沪市主板"],
      "industry": ["软件开发"]
    },
    "targetDate": "2024-12-01"
  }
}
```

**响应结果：**
```json
{
  "success": true,
  "message": "筛选条件保存成功",
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "name": "我的筛选条件1",
    "createdAt": "2024-12-01T10:00:00.000Z",
    "updatedAt": "2024-12-01T10:00:00.000Z"
  }
}
```

### 2. 获取用户所有保存的筛选条件列表（包含详情）
**GET** `/api/filter/conditions`

**响应结果：**
```json
{
  "success": true,
  "message": "获取筛选条件列表成功",
  "data": [
    {
      "id": "507f1f77bcf86cd799439011",
      "name": "我的筛选条件1",
      "filterConditions": {
        "dailyConditions": {
          "peRatioDynamicRange": { "min": 10, "max": 30 }
        },
        "basicInfoConditions": {
          "board": ["沪市主板"]
        },
        "targetDate": "2024-12-01"
      },
      "createdAt": "2024-12-01T10:00:00.000Z",
      "updatedAt": "2024-12-01T10:00:00.000Z"
    },
    {
      "id": "507f1f77bcf86cd799439012",
      "name": "价值投资筛选",
      "filterConditions": {
        "financialConditions": {
          "netProfitGrowthRateRange": { "min": 0.1, "max": null }
        }
      },
      "createdAt": "2024-11-30T15:30:00.000Z",
      "updatedAt": "2024-11-30T15:30:00.000Z"
    }
  ]
}
```

### 3. 删除筛选条件
**DELETE** `/api/filter/conditions/:id`

**路径参数：**
- `id`: 筛选条件ID (MongoDB ObjectId 格式)

**响应结果：**
```json
{
  "success": true,
  "message": "筛选条件删除成功",
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "name": "我的筛选条件1"
  }
}
```

### 4. 重命名筛选条件
**PATCH** `/api/filter/conditions/:id`

**路径参数：**
- `id`: 筛选条件ID (MongoDB ObjectId 格式)

**请求参数：**
```json
{
  "name": "新的筛选条件名称"
}
```

**响应结果：**
```json
{
  "success": true,
  "message": "筛选条件重命名成功",
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "name": "新的筛选条件名称",
    "updatedAt": "2024-12-01T11:00:00.000Z"
  }
}
```

## 错误响应示例

### 参数验证失败
```json
{
  "success": false,
  "message": "请求参数验证失败",
  "errors": [
    {
      "field": "name",
      "message": "筛选条件名称不能为空",
      "value": ""
    }
  ]
}
```

### 业务逻辑错误
```json
{
  "success": false,
  "message": "该筛选条件名称已存在，请使用其他名称"
}
```

## 注意事项

1. 所有API都需要用户认证（需要在请求中包含用户身份信息）
2. 筛选条件名称在同一用户下必须唯一
3. 筛选条件名称长度限制为1-50个字符
4. 筛选条件ID必须是有效的MongoDB ObjectId格式
5. 用户只能访问自己保存的筛选条件
