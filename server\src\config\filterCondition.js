// 股票筛选条件配置文件
// 自动生成于: 2025/9/13
// 数据来源: 言策AI量化平台

module.exports = {
  // 当日数据筛选条件
  dailyConditions: {
    // 市盈率相关
    peRatioDynamicRange: {
      label: '市盈率(动)',
      unit: '倍',
      type: 'range',
      description: '动态市盈率范围'
    },
    peRatioStaticRange: {
      label: '市盈率(静)', 
      unit: '倍',
      type: 'range',
      description: '静态市盈率范围'
    },
    peRatioTTMRange: {
      label: '市盈率(TTM)',
      unit: '倍', 
      type: 'range',
      description: 'TTM市盈率范围'
    },
    
    // 市净率
    pbRatioRange: {
      label: '市净率',
      unit: '倍',
      type: 'range', 
      description: '市净率范围'
    },
    
    // 市值相关
    totalMarketCapRange: {
      label: '总市值',
      unit: '亿元',
      type: 'range',
      description: '总市值范围'
    },
    circulatingMarketCapRange: {
      label: '流通市值',
      unit: '亿元', 
      type: 'range',
      description: '流通市值范围'
    },
    
    // 涨停跌停条件
    isLimitUp: {
      label: '涨停',
      type: 'boolean',
      description: '是否涨停'
    },
    isLimitDown: {
      label: '跌停',
      type: 'boolean', 
      description: '是否跌停'
    },
    touchedLimitUp: {
      label: '触及涨停',
      type: 'boolean',
      description: '是否触及涨停价'
    },
    touchedLimitDown: {
      label: '触及跌停',
      type: 'boolean',
      description: '是否触及跌停价'
    },
    
    // 技术指标
    turnoverRateRange: {
      label: '换手率',
      unit: '%',
      type: 'range',
      description: '换手率范围'
    },
    changePercentRange: {
      label: '涨跌幅',
      unit: '%',
      type: 'range', 
      description: '涨跌幅范围'
    },
    amplitudeRange: {
      label: '振幅',
      unit: '%',
      type: 'range',
      description: '振幅范围'
    },
    ytdGainRange: {
      label: '今年以来涨幅', 
      unit: '%',
      type: 'range',
      description: '今年以来涨跌幅范围'
    },
    sixtyDayChangeRange: {
      label: '60日涨跌幅',
      unit: '%',
      type: 'range',
      description: '60日涨跌幅范围'
    },
    dividendYieldTTMRange: {
      label: '股息率(TTM)',
      unit: '%',
      type: 'range',
      description: '股息率(TTM)范围'
    }
  },

  // 财务数据筛选条件
  financialConditions: {
    // 增长率指标
    netProfitGrowthRateRange: {
      label: '净利润同比增长率',
      unit: '%',
      type: 'range',
      description: '净利润同比增长率范围'
    },
    nonRecurringNetProfitGrowthRateRange: {
      label: '扣非净利润同比增长率',
      unit: '%', 
      type: 'range',
      description: '扣除非经常性损益净利润同比增长率范围'
    },
    totalRevenueGrowthRateRange: {
      label: '营业总收入同比增长率',
      unit: '%',
      type: 'range',
      description: '营业总收入同比增长率范围'
    },
    
    // 盈利能力指标
    netProfitMarginRange: {
      label: '销售净利率',
      unit: '%',
      type: 'range', 
      description: '销售净利率范围'
    },
    grossProfitMarginRange: {
      label: '销售毛利率',
      unit: '%',
      type: 'range',
      description: '销售毛利率范围'
    },
    returnOnEquityRange: {
      label: '净资产收益率',
      unit: '%',
      type: 'range',
      description: '净资产收益率范围'
    },
    dilutedReturnOnEquityRange: {
      label: '净资产收益率-摊薄',
      unit: '%', 
      type: 'range',
      description: '摊薄净资产收益率范围'
    },
    
    // 偿债能力指标
    currentRatioRange: {
      label: '流动比率',
      unit: '',
      type: 'range',
      description: '流动比率范围'
    },
    quickRatioRange: {
      label: '速动比率',
      unit: '',
      type: 'range',
      description: '速动比率范围'
    },
    equityRatioRange: {
      label: '产权比率',
      unit: '',
      type: 'range',
      description: '产权比率范围'
    },
    debtToAssetRatioRange: {
      label: '资产负债率',
      unit: '%',
      type: 'range',
      description: '资产负债率范围'
    },
    
    // 营运能力指标
    inventoryTurnoverRatioRange: {
      label: '存货周转率', 
      unit: '次',
      type: 'range',
      description: '存货周转率范围'
    },
    inventoryTurnoverDaysRange: {
      label: '存货周转天数',
      unit: '天',
      type: 'range',
      description: '存货周转天数范围'
    },
    accountsReceivableTurnoverDaysRange: {
      label: '应收账款周转天数',
      unit: '天',
      type: 'range',
      description: '应收账款周转天数范围'
    }
  },

  // 基本信息筛选条件
  basicInfoConditions: {
    board: {
      label: '板块',
      type: 'multiSelect',
      description: '股票所属板块',
      options: ['沪市主板', '深市主板', '创业板', '科创板']
    },
    industry: {
      label: '行业',
      type: 'multiSelect', 
      description: '股票所属行业',
      options: ['银行', '证券', '保险', '软件开发', '计算机应用', '医药制造']
    },
    listedYearsRange: {
      label: '已上市年份',
      unit: '年',
      type: 'range',
      description: '已上市年份范围'
    }
  },

  // 筛选条件分组配置
  conditionGroups: [
    {
      key: 'dailyConditions',
      label: '当日数据',
      icon: 'chart-line',
      description: '基于当日股价和技术指标的筛选条件'
    },
    {
      key: 'financialConditions', 
      label: '财务数据',
      icon: 'calculator',
      description: '基于年度财务报表的筛选条件'
    },
    {
      key: 'basicInfoConditions',
      label: '基本信息',
      icon: 'info-circle', 
      description: '基于股票基本信息的筛选条件'
    }
  ],

  // 新增：筛选条件分类映射
  conditionClassification: {
    // 当日数据分类
    dailyConditions: {
      fundamentalIndicators: {
        label: '基本面指标',
        description: '估值和市值相关指标',
        conditions: [
          'peRatioDynamicRange',
          'peRatioStaticRange', 
          'peRatioTTMRange',
          'pbRatioRange',
          'dividendYieldTTMRange',
          'totalMarketCapRange',
          'circulatingMarketCapRange'
        ]
      },
      marketIndicators: {
        label: '行情指标',
        description: '价格变动和交易活跃度指标',
        conditions: [
          'changePercentRange',
          'amplitudeRange',
          'ytdGainRange',
          'sixtyDayChangeRange',
          'turnoverRateRange',
          'isLimitUp',
          'isLimitDown',
          'touchedLimitUp',
          'touchedLimitDown'
        ]
      }
    },
    
    // 财务数据分类
    financialConditions: {
      growthIndicators: {
        label: '成长性指标',
        description: '反映公司成长能力的指标',
        conditions: [
          'netProfitGrowthRateRange',
          'nonRecurringNetProfitGrowthRateRange',
          'totalRevenueGrowthRateRange'
        ]
      },
      profitabilityIndicators: {
        label: '盈利能力指标',
        description: '反映公司盈利能力的指标',
        conditions: [
          'netProfitMarginRange',
          'grossProfitMarginRange',
          'returnOnEquityRange',
          'dilutedReturnOnEquityRange'
        ]
      },
      solvencyIndicators: {
        label: '偿债能力指标',
        description: '反映公司偿债能力的指标',
        conditions: [
          'currentRatioRange',
          'quickRatioRange',
          'equityRatioRange',
          'debtToAssetRatioRange'
        ]
      },
      operatingIndicators: {
        label: '营运能力指标',
        description: '反映公司营运效率的指标',
        conditions: [
          'inventoryTurnoverRatioRange',
          'inventoryTurnoverDaysRange',
          'accountsReceivableTurnoverDaysRange'
        ]
      }
    },
    
    // 基本信息分类
    basicInfoConditions: {
      companyBasicInfo: {
        label: '公司基本信息',
        description: '公司上市信息和分类信息',
        conditions: [
          'board',
          'industry',
          'listedYearsRange'
        ]
      }
    }
  }
};
