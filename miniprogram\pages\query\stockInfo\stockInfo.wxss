/* 股票信息页面样式 */
@import '../../../styles/base.wxss';

.stock-info-page {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  display: flex;
  flex-direction: column;
}

/* ==================== 页面头部 ==================== */
.page-header {
  background: var(--gradient-primary);
  padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);
  color: var(--text-inverse);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stock-code {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
  letter-spacing: 2rpx;
}

.stock-name {
  font-size: var(--font-size-title);
  font-weight: var(--font-weight-bold);
  opacity: 0.95;
}

/* ==================== 基本信息卡片 ==================== */
.basic-info-card {
  background-color: var(--bg-primary);
  margin: var(--spacing-base);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-light);
  border: 1rpx solid var(--border-light);
}

.card-header {
  padding: var(--spacing-base) var(--spacing-lg);
  border-bottom: 1rpx solid var(--border-lighter);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-base) var(--radius-base) 0 0;
}

.card-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
  display: block;
}

.info-grid {
  padding: var(--spacing-base);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-base);
}

.info-item {
  display: flex;
  flex-direction: column;
  padding: var(--spacing-sm);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-sm);
  border: 1rpx solid var(--border-lighter);
}

.info-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.info-value {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

/* ==================== 数据类型切换 ==================== */
.data-tabs {
  background-color: var(--bg-primary);
  margin: 0 var(--spacing-base) var(--spacing-base);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-light);
  border: 1rpx solid var(--border-light);
}

.tabs-scroll {
  white-space: nowrap;
}

.tab-list {
  display: flex;
  padding: var(--spacing-xs);
}

.tab-item {
  position: relative;
  padding: var(--spacing-base) var(--spacing-lg);
  margin-right: var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
  white-space: nowrap;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 160rpx;
}

.tab-item.active {
  background-color: var(--primary-color);
  color: var(--text-inverse);
}

.tab-item:not(.active) {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
}

.tab-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 4rpx;
  background-color: var(--accent-color);
  border-radius: 2rpx;
}

/* ==================== 数据内容区域 ==================== */
.data-content {
  flex: 1;
  padding-bottom: 160rpx; /* 为底部操作栏留出空间 */
}

.data-card {
  background-color: var(--bg-primary);
  margin: 0 var(--spacing-base) var(--spacing-base);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-light);
  border: 1rpx solid var(--border-light);
}

.data-grid {
  padding: var(--spacing-base);
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-sm);
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-base);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-sm);
  border: 1rpx solid var(--border-lighter);
  transition: all 0.2s ease;
}

.data-item:hover {
  border-color: var(--border-base);
}

.data-label {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  flex: 1;
}

.data-value {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

/* 涨跌颜色样式 */
.data-value.rise {
  color: var(--color-rise) !important;
  font-weight: var(--font-weight-bold);
}

.data-value.fall {
  color: var(--color-fall) !important;
  font-weight: var(--font-weight-bold);
}

/* ==================== 空状态 ==================== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  min-height: 400rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: var(--spacing-lg);
  opacity: 0.6;
}

.empty-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-desc {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: center;
  line-height: 1.6;
}

/* ==================== 加载状态 ==================== */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  min-height: 400rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-lighter);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-lg);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
}

/* ==================== 底部操作栏 ==================== */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--bg-primary);
  padding: var(--spacing-base) var(--spacing-lg);
  border-top: 1rpx solid var(--border-light);
  display: flex;
  gap: var(--spacing-base);
  box-shadow: var(--shadow-top);
  z-index: 100;
}

.action-button {
  flex: 1;
  height: 80rpx;
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.action-button.primary {
  background: var(--gradient-primary);
  color: var(--text-inverse);
}

.action-button.primary:hover {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-base);
}

.action-button.secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1rpx solid var(--border-base);
}

.action-button.secondary:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--border-darker);
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 750rpx) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .tab-item {
    min-width: 140rpx;
    padding: var(--spacing-sm) var(--spacing-base);
  }
  
  .data-label {
    font-size: var(--font-size-sm);
  }
  
  .data-value {
    font-size: var(--font-size-sm);
    max-width: 50%;
  }
}
