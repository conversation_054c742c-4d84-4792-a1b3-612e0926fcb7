const User = require('../../models/User');
const { generateToken } = require('../../utils/jwt');
const wechatAPI = require('../../utils/wechat');

/**
 * 微信小程序登录
 * @param {String} code 微信授权码
 * @returns {Object} 包含用户和令牌的对象
 */
const wxLogin = async (code) => {
  try {
    console.log('开始处理微信登录');

    // 通过code获取openid
    const wxData = await wechatAPI.code2Session(code);
    const { openid, unionid } = wxData;

    console.log('获取到微信openid:', openid.substring(0, 8) + '...');

    // 查找用户是否存在
    let user = await User.findOne({ openId: openid }).select('+refreshToken');

    let isNewUser = false;

    // 如果用户不存在，创建新用户
    if (!user) {
      console.log('创建微信新用户:', openid.substring(0, 8) + '...');
      
      // 生成用户昵称：言策用户 + openid后4位
      const openidSuffix = openid.slice(-4);
      const nickname = `言策用户${openidSuffix}`;

      user = await User.create({
        nickname: nickname,
        openId: openid,
        unionId: unionid,
        avatar: '/assets/images/default-avatar.png',
        gender: 'secret',
        age: 18,
        occupation: '',
        region: '',
        lastLoginAt: new Date()
      });

      isNewUser = true;
      console.log('微信新用户创建成功:', user._id);
    } else {
      // 用户存在，检查账号状态
      if (!user.isActive) {
        throw { statusCode: 403, message: '您的账号已被禁用' };
      }

      // 更新unionid（如果有变化）
      if (unionid && user.unionId !== unionid) {
        await User.findByIdAndUpdate(user._id, { unionId: unionid });
      }

      // 更新最后登录时间
      await User.findByIdAndUpdate(user._id, {
        lastLoginAt: new Date()
      });

      console.log('微信用户登录成功:', user._id);
    }

    // 生成JWT令牌
    const token = generateToken(user._id);
    
    // 更新用户的refreshToken
    await User.findByIdAndUpdate(user._id, { refreshToken: token.refreshToken });

    // 准备返回的用户数据
    const userData = {
      id: user._id,
      nickname: user.nickname,
      avatar: user.avatar,
      phone: user.phone || '', // 微信用户可能没有手机号
      gender: user.gender,
      age: user.age,
      occupation: user.occupation,
      region: user.region,
      lastLoginAt: new Date()
    };

    return { 
      user: userData, 
      token,
      isNewUser
    };

  } catch (error) {
    console.error("微信登录错误:", error);
    
    if (error.statusCode) {
      throw error;
    }
    
    // 如果是微信API错误，保持原始错误信息
    if (error.message && (
      error.message.includes('无效的code') || 
      error.message.includes('微信') ||
      error.message.includes('API调用过于频繁')
    )) {
      throw { 
        statusCode: 400, 
        message: error.message
      };
    }
    
    throw { 
      statusCode: 500, 
      message: '微信登录失败，请稍后再试',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    };
  }
};

module.exports = { wxLogin };
