const Joi = require('joi');

// 解析筛选条件的验证规则
const parseFilterConditionValidator = {
  body: Joi.object({
    userInput: Joi.string()
      .trim()
      .min(1)
      .max(500)
      .required()
      .messages({
        'string.empty': '用户输入不能为空',
        'string.min': '用户输入不能为空',
        'string.max': '用户输入不能超过500个字符',
        'any.required': '用户输入不能为空'
      })
  }).required()
};

/**
 * 验证解析筛选条件请求的中间件
 * @param {Object} req Express请求对象
 * @param {Object} res Express响应对象
 * @param {Function} next Express下一个中间件
 */
const validateParseFilterConditionRequest = (req, res, next) => {
  const { error, value } = parseFilterConditionValidator.body.validate(req.body, {
    abortEarly: false,
    allowUnknown: false
  });

  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));

    return res.status(400).json({
      success: false,
      message: '请求参数验证失败',
      errors
    });
  }

  req.validatedBody = value;
  next();
};

module.exports = {
  validateParseFilterConditionRequest
};