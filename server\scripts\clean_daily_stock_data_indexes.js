const mongoose = require('mongoose');

const MONGO_URI = 'mongodb://localhost:27017/VoiceQuant'; // 修改为你的数据库连接地址
const COLLECTION_NAME = 'daily_stock_data';

async function cleanupIndexes() {
  await mongoose.connect(MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true });
  const collection = mongoose.connection.collection(COLLECTION_NAME);

  // 获取所有索引
  const indexes = await collection.indexes();
  let uniqueIndexes = indexes.filter(idx => idx.unique);

  console.log('当前唯一性索引:');
  uniqueIndexes.forEach(idx => console.log(idx));

  // 只保留 stockCode 唯一性索引
  for (const idx of uniqueIndexes) {
    if (!(idx.key.stockCode === 1 && Object.keys(idx.key).length === 1)) {
      console.log(`删除索引: ${idx.name}`);
      await collection.dropIndex(idx.name);
    }
  }

  console.log('清理完成。');
  await mongoose.disconnect();
}

cleanupIndexes().catch(err => {
  console.error('发生错误:', err);
});