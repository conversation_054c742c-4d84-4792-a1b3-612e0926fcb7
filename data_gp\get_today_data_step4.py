import akshare as ak
import pandas as pd
from datetime import datetime

def get_all_stock_data():
    """
    获取四个板块的股票数据并整合
    """
    print("开始获取股票数据...")
    
    # 初始化一个空的DataFrame来存储所有数据
    all_data = pd.DataFrame()
    
    # 定义板块信息
    board_info = [
        {"name": "沪市A股", "func": ak.stock_sh_a_spot_em},
        {"name": "深市A股", "func": ak.stock_sz_a_spot_em},
    ]
    
    # 逐个获取各板块数据
    for board in board_info:
        try:
            print(f"正在获取{board['name']}数据...")
            
            # 调用对应的API
            df = board['func']()
            
            if not df.empty:
                # 添加板块字段
                df['板块'] = board['name']
                
                # 删除序号字段
                if '序号' in df.columns:
                    df = df.drop('序号', axis=1)
                
                # 重命名字段
                df = df.rename(columns={
                    '代码': '证券代码',
                    '名称': '证券简称'
                })
                
                # 调整字段顺序，将板块放在证券简称和最新价之间
                cols = df.columns.tolist()
                
                # 找到证券简称和最新价的位置
                name_idx = cols.index('证券简称')
                price_idx = cols.index('最新价')
                
                # 移除板块字段从原位置
                cols.remove('板块')
                
                # 在证券简称后插入板块字段
                cols.insert(name_idx + 1, '板块')
                
                # 重新排列DataFrame
                df = df[cols]
                
                # 添加到总数据中
                all_data = pd.concat([all_data, df], ignore_index=True)
                
                print(f"{board['name']}数据获取成功，共{len(df)}条记录")
                
            else:
                print(f"警告：{board['name']}数据为空")
                
        except Exception as e:
            print(f"获取{board['name']}数据时出错：{str(e)}")
            continue
    
    return all_data

def save_to_csv(data, filename="当日股票数据.csv"):
    """
    保存数据到CSV文件
    """
    try:
        # 保存到CSV文件
        data.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"数据已成功保存到 {filename}")
        print(f"总共保存了 {len(data)} 条股票数据")
        
        # 显示各板块的数据统计
        if '板块' in data.columns:
            board_counts = data['板块'].value_counts()
            print("\n各板块数据统计：")
            for board, count in board_counts.items():
                print(f"  {board}: {count} 条")
                
    except Exception as e:
        print(f"保存文件时出错：{str(e)}")

def main():
    """
    主函数
    """
    print("=" * 50)
    print("股票数据整合脚本")
    print(f"执行时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # 获取所有股票数据
    stock_data = get_all_stock_data()
    
    if not stock_data.empty:
        # 保存到CSV文件
        save_to_csv(stock_data)
        
        # 显示前几行数据作为预览
        print("\n数据预览（前5行）：")
        print(stock_data.head())
        
        # 显示字段信息
        print(f"\n字段列表：{list(stock_data.columns)}")
        
    else:
        print("错误：未能获取到任何股票数据")
    
    print("\n脚本执行完成！")

if __name__ == "__main__":
    main()
