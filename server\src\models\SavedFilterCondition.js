const mongoose = require('mongoose');

const SavedFilterConditionSchema = new mongoose.Schema(
  {
    // 用户ID
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, '用户ID不能为空'],
      index: true
    },
    
    // 筛选条件名称
    name: {
      type: String,
      required: [true, '筛选条件名称不能为空'],
      trim: true,
      maxlength: [50, '筛选条件名称不能超过50个字符']
    },
    
    // 筛选条件内容
    filterConditions: {
      // 当日数据筛选条件
      dailyConditions: {
        type: Object,
        default: {}
      },
      
      // 财务数据筛选条件
      financialConditions: {
        type: Object,
        default: {}
      },
      
      // 基本信息筛选条件
      basicInfoConditions: {
        type: Object,
        default: {}
      }
    },
    
    // 创建时间
    createdAt: {
      type: Date,
      default: Date.now
    },
    
    // 更新时间
    updatedAt: {
      type: Date,
      default: Date.now
    }
  },
  {
    timestamps: true,
    versionKey: false
  }
);

// 创建复合索引，确保同一用户下的筛选条件名称唯一
SavedFilterConditionSchema.index({ userId: 1, name: 1 }, { unique: true });

// 静态方法：根据用户ID获取所有保存的筛选条件
SavedFilterConditionSchema.statics.findByUserId = function(userId) {
  return this.find({ userId }).select('_id name createdAt updatedAt').sort({ updatedAt: -1 });
};

// 静态方法：根据ID和用户ID获取特定的筛选条件
SavedFilterConditionSchema.statics.findByIdAndUserId = function(id, userId) {
  return this.findOne({ _id: id, userId });
};

// 实例方法：更新时间戳
SavedFilterConditionSchema.methods.updateTimestamp = function() {
  this.updatedAt = new Date();
  return this.save();
};

module.exports = mongoose.model('SavedFilterCondition', SavedFilterConditionSchema);
