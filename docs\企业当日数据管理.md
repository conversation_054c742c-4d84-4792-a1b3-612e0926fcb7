# 企业当日数据管理

## 概述

这个模块用于管理和存储企业的当日股票交易数据，包括价格、成交量、技术指标等实时信息。

## 数据模型

### DailyStockData 模型

位置：`server/models/DailyStockData.js`

#### 主要字段分类

1. **基本信息**
   - 证券代码 (stockCode)
   - 时间戳 (timestamp)

2. **价格信息**
   - 现价 (currentPrice)
   - 涨跌额 (priceChange)
   - 涨幅 (priceChangePercent)
   - 昨收 (previousClose)
   - 今开 (openPrice)
   - 最高 (highPrice)
   - 最低 (lowPrice)

3. **交易信息**
   - 成交量 (volume)
   - 成交额 (turnover)
   - 换手率 (turnoverRate)
   - 振幅 (amplitude)
   - 涨停价 (limitUp)
   - 跌停价 (limitDown)
   - 均价 (averagePrice)

4. **52周信息**
   - 52周最高 (week52High)
   - 52周最低 (week52Low)
   - 今年以来涨幅 (ytdGain)

5. **估值指标**
   - 市盈率(动) (peRatioDynamic)
   - 市盈率(静) (peRatioStatic)
   - 市盈率(TTM) (peRatioTTM)
   - 市净率 (pbRatio)

6. **每股指标**
   - 每股收益 (earningsPerShare)
   - 每股净资产 (bookValuePerShare)
   - 股息(TTM) (dividendTTM)
   - 股息率(TTM) (dividendYieldTTM)

7. **市值信息**
   - 总市值 (totalMarketCap)
   - 流通市值 (circulatingMarketCap)

8. **技术指标**
   - 量比 (volumeRatio)
   - 涨速 (priceVelocity)
   - 5分钟涨跌 (fiveMinuteChange)
   - 60日涨跌幅 (sixtyDayChange)

#### 字段映射

模型提供了中英文字段映射功能：

```javascript
// 获取所有字段映射
const mapping = DailyStockData.getFieldMapping();

// 获取中文字段名
const chineseName = DailyStockData.getChineseFieldName('currentPrice'); // "现价"

// 获取英文字段名
const englishName = DailyStockData.getEnglishFieldName('现价'); // "currentPrice"
```

#### 数据库索引

- 复合唯一索引：`{ stockCode: 1, timestamp: 1 }`
- 查询索引：`{ stockCode: 1, timestamp: -1 }`
- 时间索引：`{ timestamp: -1 }`
- 价格索引：`{ currentPrice: 1 }`
- 成交量索引：`{ volume: -1 }`
- 成交额索引：`{ turnover: -1 }`
- 市值索引：`{ totalMarketCap: -1 }`

## 静态方法

### 查询方法

```javascript
// 根据股票代码查询
const data = await DailyStockData.findByStockCode('600000', {
  startDate: '2025-09-01',
  endDate: '2025-09-04'
});

// 获取最新数据
const latest = await DailyStockData.findLatestByStockCode('600000');

// 根据时间范围查询
const rangeData = await DailyStockData.findByDateRange('2025-09-01', '2025-09-04', {
  stockCodes: ['600000', '000001'],
  limit: 100
});

// 批量查询最新数据
const multipleLatest = await DailyStockData.findLatestByStockCodes(['600000', '000001']);

// 涨幅排行榜
const topGainers = await DailyStockData.findTopGainers('2025-09-04', 10);

// 跌幅排行榜
const topLosers = await DailyStockData.findTopLosers('2025-09-04', 10);

// 成交额排行榜
const topVolume = await DailyStockData.findTopVolume('2025-09-04', 10);
```

## 实例方法

### 格式化显示

```javascript
const stock = await DailyStockData.findLatestByStockCode('600000');

// 基本显示
console.log(stock.toDisplayString()); // "600000 - 2025-09-04 13.77"

// 价格变动
console.log(stock.getFormattedPriceChange()); // "+0.02 (+0.15%)"

// 成交额格式化
console.log(stock.getFormattedTurnover()); // "13.82亿"

// 市值格式化
console.log(stock.getFormattedMarketCap()); // "4200.97亿"

// 趋势状态
console.log(stock.getTrendStatus()); // "up" | "down" | "flat"

// 技术指标摘要
const summary = stock.getTechnicalSummary();
/*
{
  priceChange: "+0.02 (+0.15%)",
  turnover: "13.82亿",
  marketCap: "4200.97亿",
  pe: "8.75",
  pb: "0.61",
  turnoverRate: "0.33%",
  amplitude: "2.54%"
}
*/

// 异常数据检查
console.log(stock.isAnomalousData()); // true | false
```

## 数据导入

### 导入脚本

位置：`server/scripts/importDailyStockData.js`

#### 运行导入

```powershell
# 进入服务器目录
cd d:\investment-AI\server

# 安装依赖（如果还没安装）
npm install

# 运行导入脚本
node scripts/importDailyStockData.js
```

#### 导入配置

脚本中的配置选项：

```javascript
const IMPORT_CONFIG = {
  batchSize: 1000,        // 批量处理大小
  skipErrors: true,       // 是否跳过错误数据
  updateExisting: true,   // 是否更新已存在的数据
  validateData: true      // 是否验证数据
};
```

#### 功能特性

1. **BOM检测和处理**：自动检测并移除CSV文件的BOM标记
2. **数据验证**：验证股票代码格式、价格逻辑等
3. **批量处理**：分批处理大量数据，避免内存溢出
4. **错误处理**：详细的错误记录和报告
5. **进度显示**：实时显示导入进度
6. **重复数据处理**：使用upsert操作处理重复数据
7. **股票代码保护**：确保不丢失股票代码的前导零

#### 错误报告

导入完成后会生成详细的错误报告：
- 控制台显示摘要信息
- 详细错误保存到 `server/logs/import_errors.json`

## 测试

### 运行测试

```powershell
# 测试模型功能
node test/testDailyStockData.js
```

测试内容包括：
- 数据库连接
- 字段映射
- 数据创建和更新
- 查询方法
- 实例方法
- 数据清理

## 环境配置

### 数据库连接

默认连接：`mongodb://localhost:27017/investment_ai`

通过环境变量自定义：
```bash
export MONGODB_URI="mongodb://your-host:port/your-database"
```

### 文件路径

CSV文件路径：`data_gp/企业当日数据_全字段_雪球为主.csv`

## API使用示例

### 基本CRUD操作

```javascript
const DailyStockData = require('./models/DailyStockData');

// 创建数据
const stockData = new DailyStockData({
  stockCode: '600000',
  timestamp: new Date(),
  currentPrice: 13.77,
  // ... 其他字段
});
await stockData.save();

// 查询数据
const data = await DailyStockData.findByStockCode('600000');

// 更新数据
await DailyStockData.updateOne(
  { stockCode: '600000', timestamp: someDate },
  { $set: { currentPrice: 13.80 } }
);

// 删除数据
await DailyStockData.deleteMany({ stockCode: '600000' });
```

### 高级查询

```javascript
// 分页查询
const data = await DailyStockData.find({ stockCode: '600000' })
  .sort({ timestamp: -1 })
  .limit(10)
  .skip(0);

// 聚合查询
const avgPrice = await DailyStockData.aggregate([
  { $match: { stockCode: '600000' } },
  { $group: { _id: null, avgPrice: { $avg: '$currentPrice' } } }
]);

// 范围查询
const recentData = await DailyStockData.find({
  stockCode: '600000',
  timestamp: {
    $gte: new Date('2025-09-01'),
    $lte: new Date('2025-09-04')
  }
});
```

## 注意事项

1. **股票代码格式**：确保股票代码保持6位数字格式，包括前导零
2. **时间戳处理**：所有时间都转换为Date对象存储
3. **数值处理**：空值或无效数值存储为null
4. **唯一性约束**：同一股票同一时间点只能有一条记录
5. **性能考虑**：大量数据查询时注意使用适当的索引和分页
6. **错误处理**：生产环境中建议启用skipErrors选项
