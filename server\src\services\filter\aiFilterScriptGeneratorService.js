const { generateTextWithDeepSeek } = require('../../utils/deepseekApi');
const DailyStockData = require('../../models/DailyStockData');
const FinancialData = require('../../models/FinancialData');
const StockBasicInfo = require('../../models/StockBasicInfo');

/**
 * AI智能生成筛选脚本服务
 * 通过两个Agent实现：第一个Agent解析用户逻辑，第二个Agent生成可执行脚本
 * @param {string} userInput 用户输入的筛选需求描述
 * @returns {Promise<Object>} 包含子逻辑和完整脚本的结果
 */
const generateFilterScript = async (userInput) => {
  try {
    console.log('开始AI智能生成筛选脚本...');
    console.log('用户输入:', userInput);

    // 第一个Agent：解析用户逻辑，分拆成子逻辑
    const subLogics = await parseUserLogicToSubLogics(userInput);
    console.log('解析出的子逻辑数量:', subLogics.length);

    // 第二个Agent：根据子逻辑生成可执行脚本
    const executableScripts = await generateExecutableScripts(subLogics);
    console.log('生成的脚本数量:', executableScripts.length);

    // 返回结果
    return {
      success: true,
      userInput,
      subLogics,
      executableScripts,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error('AI智能生成筛选脚本失败:', error);
    throw new Error(`AI智能生成筛选脚本失败: ${error.message}`);
  }
};

/**
 * 第一个Agent：解析用户逻辑，将整个筛选逻辑分拆成几个子逻辑
 * @param {string} userInput 用户输入
 * @returns {Promise<Array>} 子逻辑数组
 */
const parseUserLogicToSubLogics = async (userInput) => {
  try {
    // 构建第一个Agent的系统提示词
    const systemPrompt = buildLogicParserSystemPrompt();
    
    // 构建用户提示词
    const userPrompt = buildLogicParserUserPrompt(userInput);
    
    // 调用DeepSeek API
    const response = await generateTextWithDeepSeek(userPrompt, systemPrompt, {
      temperature: 0.2,
      maxTokens: 2000
    });
    
    // 解析AI返回的子逻辑
    const subLogics = parseSubLogicsResponse(response);
    
    return subLogics;
  } catch (error) {
    throw new Error(`解析用户逻辑失败: ${error.message}`);
  }
};

/**
 * 第二个Agent：根据子逻辑生成可执行脚本
 * @param {Array} subLogics 子逻辑数组
 * @returns {Promise<Array>} 可执行脚本数组
 */
const generateExecutableScripts = async (subLogics) => {
  try {
    const scripts = [];
    
    // 为每个子逻辑生成对应的可执行脚本
    for (let i = 0; i < subLogics.length; i++) {
      const subLogic = subLogics[i];
      console.log(`正在为子逻辑 ${i + 1} 生成脚本:`, subLogic.description);
      
      const script = await generateSingleExecutableScript(subLogic, i + 1);
      scripts.push(script);
    }
    
    return scripts;
  } catch (error) {
    throw new Error(`生成可执行脚本失败: ${error.message}`);
  }
};

/**
 * 为单个子逻辑生成可执行脚本
 * @param {Object} subLogic 子逻辑对象
 * @param {number} index 子逻辑索引
 * @returns {Promise<Object>} 可执行脚本对象
 */
const generateSingleExecutableScript = async (subLogic, index) => {
  try {
    // 构建第二个Agent的系统提示词
    const systemPrompt = buildScriptGeneratorSystemPrompt();
    
    // 构建用户提示词
    const userPrompt = buildScriptGeneratorUserPrompt(subLogic);
    
    // 调用DeepSeek API
    const response = await generateTextWithDeepSeek(userPrompt, systemPrompt, {
      temperature: 0.1,
      maxTokens: 3000
    });
    
    // 解析AI返回的脚本代码
    const scriptCode = parseScriptResponse(response);
    
    return {
      subLogicIndex: index,
      subLogicDescription: subLogic.description,
      subLogicType: subLogic.type,
      subLogicConditions: subLogic.conditions,
      scriptCode: scriptCode,
      generatedAt: new Date().toISOString()
    };
  } catch (error) {
    throw new Error(`为子逻辑 ${index} 生成脚本失败: ${error.message}`);
  }
};

/**
 * 构建第一个Agent的系统提示词
 * @returns {string} 系统提示词
 */
const buildLogicParserSystemPrompt = () => {
  // 获取数据模型字段信息
  const dailyFields = getDailyStockDataFieldsInfo();
  const financialFields = getFinancialDataFieldsInfo();
  const basicInfoFields = getStockBasicInfoFieldsInfo();

  return `你是一个专业的股票筛选逻辑分析专家。你的任务是将用户的复杂筛选需求分解成多个简单的子逻辑。

**可用的数据表和字段：**

**1. DailyStockData (当日股票数据表)：**
${dailyFields}

**2. FinancialData (财务数据表)：**
${financialFields}

**3. StockBasicInfo (股票基本信息表)：**
${basicInfoFields}

**你的任务：**
1. 分析用户的筛选需求
2. 将复杂逻辑分解成多个独立的子逻辑
3. 每个子逻辑应该是一个独立的判断条件
4. 识别子逻辑之间的关系（AND、OR等）

**输出格式要求：**
返回JSON数组，每个元素包含：
{
  "description": "子逻辑的中文描述",
  "type": "filter|calculation|ranking|comparison",
  "dataSource": "DailyStockData|FinancialData|StockBasicInfo|multiple",
  "conditions": {
    "field": "字段名",
    "operator": "操作符(>, <, >=, <=, =, !=, in, between)",
    "value": "值或值数组",
    "logic": "AND|OR"
  },
  "priority": 1-10,
  "dependency": "依赖的其他子逻辑索引，如果没有则为null"
}

**重要规则：**
1. 每个子逻辑必须是独立可执行的
2. 优先级高的子逻辑先执行
3. 如果有依赖关系，明确标注dependency
4. 尽量将复杂条件拆分成简单条件
5. 确保返回有效的JSON格式`;
};

/**
 * 构建第一个Agent的用户提示词
 * @param {string} userInput 用户输入
 * @returns {string} 用户提示词
 */
const buildLogicParserUserPrompt = (userInput) => {
  return `请将以下用户的股票筛选需求分解成多个子逻辑：

用户需求："${userInput}"

请仔细分析这个需求，将其分解成多个独立的子逻辑，每个子逻辑应该是一个简单的判断条件。
考虑数据来源、筛选条件、计算逻辑等方面。

只返回JSON数组，不要任何额外说明。`;
};

/**
 * 构建第二个Agent的系统提示词
 * @returns {string} 系统提示词
 */
const buildScriptGeneratorSystemPrompt = () => {
  return `你是一个专业的MongoDB查询脚本生成专家。你的任务是根据给定的子逻辑生成完整的、可执行的JavaScript代码。

**可用的数据模型：**
- DailyStockData: 当日股票数据
- FinancialData: 财务数据
- StockBasicInfo: 股票基本信息

**你需要生成的代码特点：**
1. 完整的可执行JavaScript函数
2. 使用Mongoose进行数据库查询
3. 包含错误处理
4. 返回符合条件的股票列表
5. 代码简洁高效，只包含核心逻辑

**代码模板结构：**
\`\`\`javascript
const executeSubLogic = async () => {
  try {
    // 核心查询逻辑
    const result = await Model.find(conditions);
    return result;
  } catch (error) {
    throw new Error(\`查询失败: \${error.message}\`);
  }
};
\`\`\`

**重要要求：**
1. 只生成核心的查询执行代码
2. 不要添加额外的功能或装饰
3. 确保代码可以直接运行
4. 使用正确的MongoDB查询语法
5. 包含基本的错误处理
6. 只返回JavaScript代码，不要任何解释`;
};

/**
 * 构建第二个Agent的用户提示词
 * @param {Object} subLogic 子逻辑对象
 * @returns {string} 用户提示词
 */
const buildScriptGeneratorUserPrompt = (subLogic) => {
  return `请为以下子逻辑生成完整的可执行JavaScript代码：

子逻辑描述：${subLogic.description}
逻辑类型：${subLogic.type}
数据源：${subLogic.dataSource}
筛选条件：${JSON.stringify(subLogic.conditions, null, 2)}

请生成一个完整的JavaScript函数，能够执行这个子逻辑并返回符合条件的股票数据。

只返回JavaScript代码，不要任何额外说明或markdown标记。`;
};

/**
 * 解析子逻辑响应
 * @param {string} response AI返回的响应
 * @returns {Array} 子逻辑数组
 */
const parseSubLogicsResponse = (response) => {
  try {
    console.log('AI返回的子逻辑:', response);

    // 去除可能的Markdown代码块标记
    let cleanResponse = response.trim();
    if (cleanResponse.startsWith('```json')) {
      cleanResponse = cleanResponse.replace(/^```json\s*/, '');
    }
    if (cleanResponse.startsWith('```')) {
      cleanResponse = cleanResponse.replace(/^```\s*/, '');
    }
    if (cleanResponse.endsWith('```')) {
      cleanResponse = cleanResponse.replace(/\s*```$/, '');
    }

    // 解析JSON
    const parsed = JSON.parse(cleanResponse);

    // 验证是否为数组
    if (!Array.isArray(parsed)) {
      throw new Error('返回的子逻辑必须是数组格式');
    }

    // 验证每个子逻辑的格式
    parsed.forEach((subLogic, index) => {
      if (!subLogic.description || !subLogic.type || !subLogic.dataSource) {
        throw new Error(`子逻辑 ${index + 1} 缺少必要字段`);
      }
    });

    return parsed;
  } catch (error) {
    throw new Error(`无法解析AI返回的子逻辑: ${error.message}。返回内容: ${response}`);
  }
};

/**
 * 解析脚本响应
 * @param {string} response AI返回的响应
 * @returns {string} 脚本代码
 */
const parseScriptResponse = (response) => {
  try {
    console.log('AI返回的脚本代码:', response);

    // 去除可能的Markdown代码块标记
    let cleanResponse = response.trim();
    if (cleanResponse.startsWith('```javascript')) {
      cleanResponse = cleanResponse.replace(/^```javascript\s*/, '');
    }
    if (cleanResponse.startsWith('```js')) {
      cleanResponse = cleanResponse.replace(/^```js\s*/, '');
    }
    if (cleanResponse.startsWith('```')) {
      cleanResponse = cleanResponse.replace(/^```\s*/, '');
    }
    if (cleanResponse.endsWith('```')) {
      cleanResponse = cleanResponse.replace(/\s*```$/, '');
    }

    // 验证代码是否包含基本的函数结构
    if (!cleanResponse.includes('async') || !cleanResponse.includes('await')) {
      console.warn('生成的代码可能不包含异步函数结构');
    }

    return cleanResponse;
  } catch (error) {
    throw new Error(`无法解析AI返回的脚本代码: ${error.message}。返回内容: ${response}`);
  }
};

/**
 * 获取当日股票数据字段信息
 * @returns {string} 字段信息字符串
 */
const getDailyStockDataFieldsInfo = () => {
  return `- stockCode: 证券代码 (String)
- closePrice: 收盘价 (Number)
- priceChange: 涨跌额 (Number)
- changePercent: 涨跌幅% (Number)
- openPrice: 今开 (Number)
- highPrice: 最高 (Number)
- lowPrice: 最低 (Number)
- volume: 成交量 (Number)
- turnover: 成交额 (Number)
- turnoverRate: 换手率% (Number)
- amplitude: 振幅% (Number)
- peRatioTTM: 市盈率(TTM) (Number)
- pbRatio: 市净率 (Number)
- totalMarketCap: 总市值(亿元) (Number)
- circulatingMarketCap: 流通市值(亿元) (Number)
- volumeRatio: 量比 (Number)
- ytdGain: 今年以来涨幅% (Number)`;
};

/**
 * 获取财务数据字段信息
 * @returns {string} 字段信息字符串
 */
const getFinancialDataFieldsInfo = () => {
  return `- stockCode: 证券代码 (String)
- fiscalYear: 财政年度 (Number)
- data.keyMetrics.netProfit: 净利润(亿元) (Number)
- data.keyMetrics.netProfitGrowthRate: 净利润同比增长率% (Number)
- data.keyMetrics.totalRevenue: 营业总收入(亿元) (Number)
- data.keyMetrics.totalRevenueGrowthRate: 营业总收入同比增长率% (Number)
- data.keyMetrics.netProfitMargin: 销售净利率% (Number)
- data.keyMetrics.grossProfitMargin: 销售毛利率% (Number)
- data.keyMetrics.basicEarningsPerShare: 基本每股收益 (Number)
- data.keyMetrics.returnOnEquity: 净资产收益率% (Number)
- data.keyMetrics.currentRatio: 流动比率 (Number)
- data.keyMetrics.debtToAssetRatio: 资产负债率% (Number)`;
};

/**
 * 获取股票基本信息字段信息
 * @returns {string} 字段信息字符串
 */
const getStockBasicInfoFieldsInfo = () => {
  return `- stockCode: 证券代码 (String)
- stockName: 证券简称 (String)
- board: 板块 (String) [沪市主板, 深市主板, 创业板, 科创板]
- industry: 行业 (String)
- listingDate: 上市时间 (Date)`;
};

module.exports = {
  generateFilterScript
};
