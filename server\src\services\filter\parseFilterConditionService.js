const axios = require('axios');
const filterConditionConfig = require('../../config/filterCondition');

// 从环境变量读取配置
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY;
const DEEPSEEK_API_BASE = process.env.DEEPSEEK_API_BASE || 'https://api.deepseek.com';
const DEEPSEEK_MODEL = process.env.DEEPSEEK_MODEL || 'deepseek-chat';

// DeepSeek API 默认配置
const DEFAULT_AI_CONFIG = {
  temperature: 0.1,
  maxTokens: 2000,
  timeout: 30000
};

/**
 * 解析用户输入的自然语言筛选条件
 * @param {string} userInput 用户输入的自然语言描述
 * @returns {Promise<Object>} 解析后的筛选条件对象
 */
const parseFilterCondition = async (userInput) => {
  try {
    // 构建系统提示词
    const systemPrompt = buildSystemPrompt();
    
    // 构建用户提示词
    const userPrompt = buildUserPrompt(userInput);
    
    // 调用 DeepSeek API
    const response = await callDeepSeekAPI(userPrompt, systemPrompt);
    
    // 解析返回的JSON
    const filterConditions = parseResponse(response);
    
    // 验证解析结果
    validateFilterConditions(filterConditions);
    
    return filterConditions;
  } catch (error) {
    throw new Error(`解析筛选条件失败: ${error.message}`);
  }
};

/**
 * 构建系统提示词
 * @returns {string} 系统提示词
 */
const buildSystemPrompt = () => {
  // 获取可用的筛选字段配置
  const availableFields = getAvailableFields();
  
  // 格式化当日数据字段信息
  const dailyFieldsInfo = availableFields.dailyConditions.map(field => {
    const unitStr = field.unit ? ` (单位: ${field.unit})` : '';
    return `- ${field.key}: ${field.label}${unitStr} [类型: ${field.type}]`;
  }).join('\n');
  
  // 格式化财务数据字段信息
  const financialFieldsInfo = availableFields.financialConditions.map(field => {
    const unitStr = field.unit ? ` (单位: ${field.unit})` : '';
    return `- ${field.key}: ${field.label}${unitStr} [类型: ${field.type}]`;
  }).join('\n');
  
  // 格式化基本信息字段信息
  const basicInfoFieldsInfo = availableFields.basicInfoConditions.map(field => {
    const optionsStr = field.options ? ` [可选值: ${field.options.join(', ')}]` : '';
    return `- ${field.key}: ${field.label} [类型: ${field.type}]${optionsStr}`;
  }).join('\n');
  
  return `你是一个专业的股票筛选条件解析助手。用户会用自然语言描述股票筛选需求，你需要将其转换为标准的筛选条件JSON格式。

**可用的筛选字段：**

**当日数据筛选条件 (dailyConditions)：**
${dailyFieldsInfo}

**财务数据筛选条件 (financialConditions)：**
${financialFieldsInfo}

**基本信息筛选条件 (basicInfoConditions)：**
${basicInfoFieldsInfo}

**输出格式要求：**
1. 必须返回有效的JSON格式
2. 使用以下结构：
{
  "dailyConditions": {},
  "financialConditions": {},
  "basicInfoConditions": {}
}

**字段值格式说明：**
- range类型字段（数值范围）：{"min": 数值或null, "max": 数值或null}
- multiSelect类型字段（多选）：["选项1", "选项2"] (只能从上述可选值中选择)
- boolean类型字段（布尔值）：true 或 false

**重要规则：**
1. 只使用上述列出的字段名，不要创造新的字段
2. 严格按照字段类型设置值格式
3. multiSelect字段的值必须从提供的可选值列表中选择
4. 不要添加任何注释或说明文字
5. 确保返回的是有效的JSON格式
6. 如果某个维度没有相关条件，该对象为空 {}
7. **特别注意：如果用户的问题与股票筛选完全无关（如问天气、聊天等），请返回所有三个筛选条件都为空的JSON结构**`;
};

/**
 * 获取可用的筛选字段信息
 * @returns {Object} 字段信息
 */
const getAvailableFields = () => {
  const { dailyConditions, financialConditions, basicInfoConditions } = filterConditionConfig;
  
  return {
    dailyConditions: Object.keys(dailyConditions).map(key => ({
      key,
      label: dailyConditions[key].label,
      unit: dailyConditions[key].unit || '',
      type: dailyConditions[key].type
    })),
    financialConditions: Object.keys(financialConditions).map(key => ({
      key,
      label: financialConditions[key].label,
      unit: financialConditions[key].unit || '',
      type: financialConditions[key].type
    })),
    basicInfoConditions: Object.keys(basicInfoConditions).map(key => ({
      key,
      label: basicInfoConditions[key].label,
      type: basicInfoConditions[key].type,
      options: basicInfoConditions[key].options || null
    }))
  };
};

/**
 * 构建用户提示词
 * @param {string} userInput 用户输入
 * @returns {string} 用户提示词
 */
const buildUserPrompt = (userInput) => {
  return `请将以下自然语言描述转换为标准的股票筛选条件JSON格式：

"${userInput}"

请仔细分析用户需求，识别涉及的筛选维度和具体条件，然后返回对应的JSON格式筛选条件。只返回JSON，不要任何额外说明。`;
};

/**
 * 调用 DeepSeek API
 * @param {string} userPrompt 用户提示词
 * @param {string} systemPrompt 系统提示词
 * @returns {Promise<string>} API响应内容
 */
const callDeepSeekAPI = async (userPrompt, systemPrompt) => {
  try {
    // 确保endpoint没有结尾的斜杠
    const baseEndpoint = DEEPSEEK_API_BASE.endsWith('/') 
      ? DEEPSEEK_API_BASE.slice(0, -1) 
      : DEEPSEEK_API_BASE;
    
    const url = `${baseEndpoint}/v1/chat/completions`;
    
    console.log(`调用DeepSeek API解析筛选条件: ${url}`);
    
    // 构建消息数组
    const messages = [
      { role: "system", content: systemPrompt },
      { role: "user", content: userPrompt }
    ];
    
    const response = await axios({
      url: url,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      data: {
        model: DEEPSEEK_MODEL,
        messages: messages,
        temperature: DEFAULT_AI_CONFIG.temperature,
        max_tokens: DEFAULT_AI_CONFIG.maxTokens,
        stream: false
      },
      timeout: DEFAULT_AI_CONFIG.timeout,
    });
    
    if (response.status === 200) {
      return response.data.choices[0].message.content;
    } else {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.error('DeepSeek API调用失败:', error);
    let errorMessage = 'DeepSeek AI解析失败，请稍后重试';
    
    if (error.response) {
      errorMessage += `(状态码: ${error.response.status})`;
    } else if (error.request) {
      errorMessage += `(请求错误: ${error.message})`;
    } else {
      errorMessage += `(错误: ${error.message})`;
    }
    
    throw new Error(errorMessage);
  }
};

/**
 * 解析API响应
 * @param {string} response API响应内容
 * @returns {Object} 解析后的筛选条件
 */
const parseResponse = (response) => {
  try {
    // 去除可能的Markdown代码块标记
    let cleanResponse = response.trim();
    if (cleanResponse.startsWith('```json')) {
      cleanResponse = cleanResponse.replace(/^```json\s*/, '');
    }
    if (cleanResponse.startsWith('```')) {
      cleanResponse = cleanResponse.replace(/^```\s*/, '');
    }
    if (cleanResponse.endsWith('```')) {
      cleanResponse = cleanResponse.replace(/\s*```$/, '');
    }
    
    // 解析JSON
    const parsed = JSON.parse(cleanResponse);
    
    // 确保返回的结构符合预期
    return {
      dailyConditions: parsed.dailyConditions || {},
      financialConditions: parsed.financialConditions || {},
      basicInfoConditions: parsed.basicInfoConditions || {}
    };
  } catch (error) {
    throw new Error(`无法解析AI返回的JSON: ${error.message}。返回内容: ${response}`);
  }
};

/**
 * 验证筛选条件格式
 * @param {Object} filterConditions 筛选条件对象
 */
const validateFilterConditions = (filterConditions) => {
  const { dailyConditions, financialConditions, basicInfoConditions } = filterConditions;
  
  // 验证基本结构
  if (typeof dailyConditions !== 'object' || Array.isArray(dailyConditions)) {
    throw new Error('dailyConditions 必须是对象');
  }
  if (typeof financialConditions !== 'object' || Array.isArray(financialConditions)) {
    throw new Error('financialConditions 必须是对象');
  }
  if (typeof basicInfoConditions !== 'object' || Array.isArray(basicInfoConditions)) {
    throw new Error('basicInfoConditions 必须是对象');
  }
  
  // 验证字段是否存在于配置中
  validateFields(dailyConditions, filterConditionConfig.dailyConditions, 'dailyConditions');
  validateFields(financialConditions, filterConditionConfig.financialConditions, 'financialConditions');
  validateFields(basicInfoConditions, filterConditionConfig.basicInfoConditions, 'basicInfoConditions');
};

/**
 * 验证字段是否有效
 * @param {Object} conditions 条件对象
 * @param {Object} configFields 配置中的字段
 * @param {string} groupName 组名
 */
const validateFields = (conditions, configFields, groupName) => {
  for (const fieldName in conditions) {
    if (!configFields[fieldName]) {
      throw new Error(`未知的筛选字段: ${groupName}.${fieldName}`);
    }
  }
};

module.exports = {
  parseFilterCondition
};
