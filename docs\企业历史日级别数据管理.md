# 企业历史日级别数据管理

## 概述

本模块用于管理企业的历史日级别股票交易数据，包括数据模型定义、CSV数据导入和相关查询功能。

## 文件结构

```
server/
├── models/
│   └── HistoricalDailyData.js          # 历史日级别数据模型
├── scripts/
│   └── importHistoricalDailyData.js    # 数据导入脚本
└── test/
    └── testHistoricalDailyData.js      # 测试文件
```

## 数据模型 (HistoricalDailyData)

### 字段说明

| 中文字段 | 英文字段 | 类型 | 必需 | 说明 |
|---------|---------|------|------|------|
| 证券代码 | stockCode | String | ✓ | 6位股票代码 |
| 日期 | date | Date | ✓ | 交易日期 |
| 开盘 | openPrice | Number | ✓ | 开盘价 |
| 收盘 | closePrice | Number | ✓ | 收盘价 |
| 最高 | highPrice | Number | ✓ | 最高价 |
| 最低 | lowPrice | Number | ✓ | 最低价 |
| 成交量 | volume | Number | ✓ | 成交量 |
| 成交额 | turnover | Number | ✓ | 成交额 |
| 振幅 | amplitude | Number |  | 振幅(%) |
| 涨跌幅 | priceChangePercent | Number |  | 涨跌幅(%) |
| 涨跌额 | priceChange | Number |  | 涨跌额 |
| 换手率 | turnoverRate | Number |  | 换手率(%) |

### 索引设计

- **唯一复合索引**: `{ stockCode: 1, date: 1 }` - 确保同一股票同一日期的唯一性
- **查询索引**: `{ stockCode: 1, date: -1 }` - 按股票代码和日期排序查询
- **时间索引**: `{ date: -1 }` - 按日期排序查询
- **成交量索引**: `{ volume: -1 }` - 成交量排序
- **成交额索引**: `{ turnover: -1 }` - 成交额排序
- **涨跌幅索引**: `{ priceChangePercent: -1 }` - 涨跌幅排序

## 数据导入功能

### CSV文件格式要求

CSV文件应包含以下列（可以是中文或英文列名）：

```csv
证券代码,日期,开盘,收盘,最高,最低,成交量,成交额,振幅,涨跌幅,涨跌额,换手率
600000,2005-01-04,-3.85,-3.87,-3.85,-3.88,38089,26134943.0,-0.78,-0.78,-0.03,0.42
```

### 导入脚本特性

1. **BOM处理**: 自动清除UTF-8 BOM字符
2. **数据验证**: 完整的数据格式和逻辑验证
3. **批量处理**: 采用批量插入提高性能 (默认批量大小: 1000)
4. **重复处理**: 自动跳过重复记录
5. **错误日志**: 详细的错误记录和统计
6. **进度显示**: 实时显示导入进度

### 数据验证规则

1. **必需字段验证**: 证券代码、日期、价格字段、成交量、成交额不能为空
2. **数据类型验证**: 数值字段必须是有效数字
3. **逻辑验证**: 
   - 价格必须大于0
   - 最高价 ≥ 最低价
   - 开盘价和收盘价必须在最高最低价范围内
   - 成交量和成交额不能为负数
4. **证券代码格式**: 自动补齐为6位数字格式

## 使用方法

### 1. 导入数据

```bash
# 直接运行导入脚本
cd server/scripts
node importHistoricalDailyData.js
```

或者在代码中使用：

```javascript
const HistoricalDataImporter = require('./scripts/importHistoricalDailyData');

async function importData() {
  const importer = new HistoricalDataImporter();
  
  await importer.connectDB();
  const result = await importer.importFromCSV('path/to/your/file.csv');
  await importer.disconnectDB();
  
  console.log('导入结果:', result);
}
```

### 2. 查询数据

```javascript
const HistoricalDailyData = require('./src/models/HistoricalDailyData');

// 查询特定股票的历史数据
const stockData = await HistoricalDailyData.findByStockCode('600000', {
  startDate: '2023-01-01',
  endDate: '2023-12-31'
});

// 获取最新数据
const latestData = await HistoricalDailyData.findLatestByStockCode('600000');

// 查询日期范围内的数据
const rangeData = await HistoricalDailyData.findByDateRange('2023-01-01', '2023-01-31');

// 获取涨幅排行榜
const topGainers = await HistoricalDailyData.findTopGainersByDate('2023-12-25', 10);

// 计算收益率
const returns = await HistoricalDailyData.calculateReturns('600000', '2023-01-01', '2023-12-31');
```

### 3. 字段映射

```javascript
// 获取字段映射
const mapping = HistoricalDailyData.getFieldMapping();

// 中英文转换
const englishName = HistoricalDailyData.getEnglishFieldName('证券代码'); // 'stockCode'
const chineseName = HistoricalDailyData.getChineseFieldName('stockCode'); // '证券代码'
```

### 4. 数据验证

```javascript
// 检查数据有效性
const record = await HistoricalDailyData.findOne({stockCode: '600000'});
const isValid = record.isValidData();

// 获取技术摘要
const summary = record.getTechnicalSummary();
```

## 测试

运行测试以验证功能：

```bash
cd server/test
node testHistoricalDailyData.js
```

## 日志文件

导入过程中的错误会记录在 `server/logs/import_historical_errors.json` 文件中，包含：

- 导入时间
- 统计信息（总数、成功、错误、重复）
- 详细错误列表

## 性能优化建议

1. **批量大小调整**: 根据内存情况调整 `batchSize` 参数
2. **索引优化**: 根据查询模式添加适当的索引
3. **数据分片**: 对于海量数据，考虑按日期或股票代码分片
4. **内存管理**: 大文件导入时注意内存使用情况

## 注意事项

1. 确保MongoDB服务正在运行
2. 检查数据库连接配置
3. 大文件导入可能需要较长时间，请耐心等待
4. 重复导入时，已存在的记录会被自动跳过
5. 确保CSV文件编码为UTF-8格式
