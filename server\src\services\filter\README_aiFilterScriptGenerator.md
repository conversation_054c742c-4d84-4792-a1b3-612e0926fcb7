# AI智能生成筛选脚本服务

## 功能概述

这个服务实现了您要求的AI智能生成筛选脚本功能，通过两个Agent来处理用户的股票筛选需求：

1. **第一个Agent（逻辑解析器）**：将用户的复杂筛选逻辑分拆成多个简单的子逻辑
2. **第二个Agent（脚本生成器）**：根据每个子逻辑生成完整的可执行JavaScript代码

## 核心特性

- ✅ 双Agent架构，提高准确性和可维护性
- ✅ 支持并行处理子逻辑，节省生成时间
- ✅ 生成可直接运行的JavaScript代码
- ✅ 支持三种数据源：当日数据、财务数据、基本信息
- ✅ 智能识别复杂逻辑并合理分解

## 使用方法

### 基本用法

```javascript
const { generateFilterScript } = require('./aiFilterScriptGeneratorService');

// 调用API
const result = await generateFilterScript("找出市盈率在10-20倍之间，市值超过100亿的股票");

console.log(result);
```

### 返回结果结构

```javascript
{
  "success": true,
  "userInput": "用户原始输入",
  "subLogics": [
    {
      "description": "子逻辑的中文描述",
      "type": "filter|calculation|ranking|comparison",
      "dataSource": "DailyStockData|FinancialData|StockBasicInfo|multiple",
      "conditions": {
        "field": "字段名",
        "operator": "操作符",
        "value": "值",
        "logic": "AND|OR"
      },
      "priority": 1,
      "dependency": null
    }
  ],
  "executableScripts": [
    {
      "subLogicIndex": 1,
      "subLogicDescription": "子逻辑描述",
      "subLogicType": "filter",
      "subLogicConditions": {...},
      "scriptCode": "完整的JavaScript代码",
      "generatedAt": "2024-12-20T10:30:00.000Z"
    }
  ],
  "timestamp": "2024-12-20T10:30:00.000Z"
}
```

## 支持的数据字段

### 当日股票数据 (DailyStockData)
- 价格相关：收盘价、涨跌幅、开盘价、最高价、最低价
- 交易相关：成交量、成交额、换手率、振幅
- 估值相关：市盈率TTM、市净率、总市值、流通市值
- 技术指标：量比、今年以来涨幅

### 财务数据 (FinancialData)
- 盈利能力：净利润、净利润增长率、销售净利率、毛利率
- 每股指标：基本每股收益、净资产收益率
- 偿债能力：流动比率、资产负债率
- 营收相关：营业总收入、营业总收入增长率

### 基本信息 (StockBasicInfo)
- 证券代码、证券简称
- 板块：沪市主板、深市主板、创业板、科创板
- 行业分类、上市时间

## 使用示例

### 示例1：简单筛选
```javascript
const result = await generateFilterScript("找出市盈率小于30倍的股票");
```

### 示例2：复合条件筛选
```javascript
const result = await generateFilterScript("筛选净利润增长率超过20%，ROE大于15%的科技股");
```

### 示例3：技术面和基本面结合
```javascript
const result = await generateFilterScript("找出今年涨幅超过50%，换手率在5%-15%之间，属于新能源行业的创业板股票");
```

## 测试方法

运行测试文件：
```javascript
const { testGenerateFilterScript } = require('./testAiFilterScriptGenerator');
await testGenerateFilterScript();
```

## 注意事项

1. **API密钥配置**：确保在环境变量中正确配置了 `DEEPSEEK_API_KEY`
2. **数据库连接**：确保MongoDB数据库连接正常
3. **错误处理**：服务包含完整的错误处理机制
4. **性能优化**：子逻辑可以并行处理，提高响应速度
5. **代码安全**：生成的脚本代码经过基本验证，但建议在沙箱环境中执行

## 扩展说明

这个服务设计为模块化架构，可以轻松扩展：
- 添加新的数据源支持
- 增加更多的逻辑类型
- 优化脚本生成算法
- 集成更多的AI模型

## 技术架构

```
用户输入 → 第一个Agent(逻辑解析) → 子逻辑数组 → 第二个Agent(脚本生成) → 可执行脚本数组
```

这种双Agent设计确保了：
- 逻辑分解的准确性
- 脚本生成的可靠性
- 系统的可维护性和扩展性
