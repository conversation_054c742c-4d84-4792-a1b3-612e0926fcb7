/* 筛选结果页面样式 */
@import '../../../styles/base.wxss';

.result-page {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  display: flex;
  flex-direction: column;
}

/* ==================== 页面标题 ==================== */
.page-header {
  background: var(--gradient-primary);
  padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);
  color: var(--text-inverse);
}

.page-title {
  font-size: var(--font-size-title);
  font-weight: var(--font-weight-bold);
  display: block;
  margin-bottom: var(--spacing-xs);
}

.page-subtitle {
  font-size: var(--font-size-sm);
  opacity: 0.9;
  display: block;
}

/* ==================== 筛选条件回顾 ==================== */
.conditions-summary {
  background-color: var(--bg-primary);
  margin: var(--spacing-base);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-light);
  border: 1rpx solid var(--border-light);
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-base) var(--spacing-lg);
  border-bottom: 1rpx solid var(--border-lighter);
}

.summary-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.summary-toggle {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  padding: var(--spacing-xs) var(--spacing-sm);
  border: none;
  background: none;
  font-weight: var(--font-weight-medium);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.summary-toggle::after {
  content: '▶';
  font-size: 20rpx;
  transition: transform 0.2s ease;
}

.summary-toggle.expanded::after {
  transform: rotate(90deg);
}


.conditions-content {
  padding: var(--spacing-lg) var(--spacing-lg);
  overflow-x: auto;
}

.condition-item {
  display: flex;
  margin-bottom: var(--spacing-base);
  align-items: flex-start;
  flex-wrap: nowrap;
}

.condition-item:last-child {
  margin-bottom: 0;
}

.condition-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  min-width: 200rpx;
  max-width: 300rpx;
  flex-shrink: 0;
  white-space: nowrap;
  overflow: visible;
  padding-right: var(--spacing-base);
  line-height: 1.4;
}

.condition-value {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  flex: 1;
  word-break: break-word;
  line-height: 1.4;
  min-width: 0;
}

/* ==================== 结果统计 ==================== */
.result-stats {
  display: flex;
  margin: var(--spacing-base);
  margin-top: 0;
  gap: var(--spacing-base);
}

.stats-item {
  flex: 1;
  background-color: var(--bg-primary);
  border-radius: var(--radius-base);
  padding: var(--spacing-lg);
  text-align: center;
  box-shadow: var(--shadow-light);
  border: 1rpx solid var(--border-light);
}

.stats-number {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.stats-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  display: block;
}

/* ==================== 结果容器 ==================== */
.result-container {
  flex: 1;
  margin: 0 var(--spacing-base) var(--spacing-base);
  background-color: var(--bg-primary);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-light);
  border: 1rpx solid var(--border-light);
  overflow: hidden;
}

.table-header {
  padding: var(--spacing-lg);
  border-bottom: 1rpx solid var(--border-lighter);
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

/* ==================== 数据表格 ==================== */
.table-scroll {
  height: auto;
  max-height: 60vh;
}

.data-table {
  display: table;
  width: 100%;
  min-width: 800rpx;
}

.table-header-row {
  display: table-row;
  background-color: var(--bg-tertiary);
}

.table-row {
  display: table-row;
  transition: var(--transition-base);
}

.table-row:nth-child(even) {
  background-color: var(--bg-secondary);
}

.table-row:nth-child(odd) {
  background-color: var(--bg-primary);
}

.table-cell {
  display: table-cell;
  padding: var(--spacing-base) var(--spacing-sm);
  border-bottom: 1rpx solid var(--border-lighter);
  vertical-align: middle;
  min-width: 120rpx;
  max-width: 200rpx;
}

.header-cell {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  background-color: var(--bg-tertiary);
  border-bottom: 2rpx solid var(--border-base);
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-cell {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

.cell-content {
  display: block;
  word-break: break-all;
  word-wrap: break-word;
  line-height: 1.4;
}

/* 特殊字段样式 */
.table-cell:first-child .cell-content {
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
}

/* ==================== 空状态 ==================== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxl) var(--spacing-lg);
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: var(--spacing-lg);
  opacity: 0.6;
}

.empty-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  display: block;
}

.empty-desc {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  display: block;
}

.empty-btn {
  background: var(--gradient-primary);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-base);
  padding: var(--spacing-base) var(--spacing-xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

.empty-btn::after {
  border: none;
}

/* ==================== 底部操作栏 ==================== */
.bottom-actions {
  display: flex;
  gap: var(--spacing-base);
  padding: var(--spacing-lg);
  background-color: var(--bg-primary);
  border-top: 1rpx solid var(--border-light);
  box-shadow: var(--shadow-reverse);
}

.action-button {
  flex: 1;
  padding: var(--spacing-lg);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-align: center;
  border: none;
}

.action-button::after {
  border: none;
}

.action-button.primary {
  background: var(--gradient-primary);
  color: var(--text-inverse);
}

.action-button.secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1rpx solid var(--border-base);
}

/* ==================== 证券代码链接样式 ==================== */
.stock-code-link {
  color: var(--text-link) !important;
  text-decoration: underline;
  cursor: pointer;
}

.stock-code-link:hover {
  color: var(--primary-light) !important;
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 750rpx) {
  .result-stats {
    flex-direction: column;
  }
  
  .stats-item {
    margin-bottom: var(--spacing-base);
  }
  
  .stats-item:last-child {
    margin-bottom: 0;
  }
  
  .header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-base);
  }
  
  .condition-item {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .condition-label {
    min-width: auto;
    max-width: none;
    padding-right: 0;
    font-weight: var(--font-weight-medium);
  }
  
  .condition-value {
    padding-left: var(--spacing-base);
    color: var(--text-secondary);
  }
  
  .table-cell {
    min-width: 100rpx;
    max-width: 150rpx;
    padding: var(--spacing-sm) var(--spacing-xs);
  }
}

/* ==================== 表格标题栏增强 ==================== */
.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-info {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* ==================== 加载更多样式 ==================== */
.load-more {
  padding: var(--spacing-lg);
  text-align: center;
}

.load-more-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid var(--border-lighter);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.load-more-hint {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.load-more-end {
  color: var(--text-tertiary);
  font-size: var(--font-size-sm);
  padding: var(--spacing-base);
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-sm);
}

/* ==================== 分页控制器样式 ==================== */
.pagination-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background-color: var(--bg-primary);
  border-top: 1rpx solid var(--border-lighter);
  margin-top: var(--spacing-base);
}

.page-btn {
  background-color: var(--primary-color);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-sm);
  min-width: 120rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-btn.disabled {
  background-color: var(--bg-tertiary);
  color: var(--text-disabled);
}

.page-btn:not(.disabled):active {
  background-color: var(--primary-dark);
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-base);
  padding: var(--spacing-sm) var(--spacing-base);
  border-radius: var(--radius-sm);
  background-color: var(--bg-secondary);
  border: 1rpx solid var(--border-light);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.page-numbers:active {
  background-color: var(--bg-tertiary);
}

.page-current {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
}

.page-separator {
  color: var(--text-tertiary);
}

.page-total {
  color: var(--text-secondary);
}
