const { generateTextWithDeepSeek } = require('../../utils/deepseekApi');
const DailyStockData = require('../../models/DailyStockData');
const FinancialData = require('../../models/FinancialData');
const StockBasicInfo = require('../../models/StockBasicInfo');

/**
 * AI智能生成筛选脚本服务
 * 通过两个Agent实现：第一个Agent解析用户逻辑，第二个Agent生成可执行脚本
 * @param {string} userInput 用户输入的筛选需求描述
 * @returns {Promise<Object>} 包含子逻辑和完整脚本的结果
 */
const generateFilterScript = async (userInput) => {
  try {
    console.log('开始AI智能生成筛选脚本...');
    console.log('用户输入:', userInput);

    // 第一个Agent：解析用户逻辑，判断是否需要拆分
    const logicAnalysis = await parseUserLogicToSubLogics(userInput);
    console.log('逻辑分析结果:', logicAnalysis.needSplit ? '需要拆分' : '不需要拆分');

    // 第二个Agent：根据逻辑分析结果生成可执行脚本
    const executableScripts = await generateExecutableScripts(logicAnalysis);
    console.log('生成的脚本数量:', executableScripts.length);

    // 返回结果
    return {
      success: true,
      userInput,
      logicAnalysis,
      executableScripts,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error('AI智能生成筛选脚本失败:', error);
    throw new Error(`AI智能生成筛选脚本失败: ${error.message}`);
  }
};

/**
 * 第一个Agent：解析用户逻辑，判断是否需要拆分成子逻辑
 * @param {string} userInput 用户输入
 * @returns {Promise<Object>} 逻辑分析结果
 */
const parseUserLogicToSubLogics = async (userInput) => {
  try {
    // 构建第一个Agent的系统提示词
    const systemPrompt = buildLogicParserSystemPrompt();

    // 构建用户提示词
    const userPrompt = buildLogicParserUserPrompt(userInput);

    // 调用DeepSeek API
    const response = await generateTextWithDeepSeek(userPrompt, systemPrompt, {
      temperature: 0.2,
      maxTokens: 2000
    });

    // 解析AI返回的逻辑分析
    const logicAnalysis = parseSubLogicsResponse(response);

    return logicAnalysis;
  } catch (error) {
    throw new Error(`解析用户逻辑失败: ${error.message}`);
  }
};

/**
 * 第二个Agent：根据逻辑分析结果生成可执行脚本
 * @param {Object} logicAnalysis 逻辑分析结果
 * @returns {Promise<Array>} 可执行脚本数组
 */
const generateExecutableScripts = async (logicAnalysis) => {
  try {
    const scripts = [];

    if (!logicAnalysis.needSplit) {
      // 不需要拆分，生成单个脚本
      console.log('生成单个脚本:', logicAnalysis.singleLogic.description);
      const script = await generateSingleExecutableScript(logicAnalysis.singleLogic, 1);
      scripts.push(script);
    } else {
      // 需要拆分，为每个子逻辑生成脚本
      for (let i = 0; i < logicAnalysis.subLogics.length; i++) {
        const subLogic = logicAnalysis.subLogics[i];
        console.log(`正在为子逻辑 ${i + 1} 生成脚本:`, subLogic.description);

        const script = await generateSingleExecutableScript(subLogic, i + 1);
        scripts.push(script);
      }
    }

    return scripts;
  } catch (error) {
    throw new Error(`生成可执行脚本失败: ${error.message}`);
  }
};

/**
 * 为单个逻辑生成可执行脚本
 * @param {Object} logicInfo 逻辑信息对象
 * @param {number} index 逻辑索引
 * @returns {Promise<Object>} 可执行脚本对象
 */
const generateSingleExecutableScript = async (logicInfo, index) => {
  try {
    // 构建第二个Agent的系统提示词
    const systemPrompt = buildScriptGeneratorSystemPrompt();

    // 构建用户提示词
    const userPrompt = buildScriptGeneratorUserPrompt(logicInfo);

    // 调用DeepSeek API
    const response = await generateTextWithDeepSeek(userPrompt, systemPrompt, {
      temperature: 0.1,
      maxTokens: 3000
    });

    // 解析AI返回的脚本代码
    const scriptCode = parseScriptResponse(response);

    return {
      logicIndex: index,
      logicDescription: logicInfo.description,
      logicType: logicInfo.type || 'simple_filter',
      complexity: logicInfo.complexity || 'low',
      scriptCode: scriptCode,
      generatedAt: new Date().toISOString()
    };
  } catch (error) {
    throw new Error(`为逻辑 ${index} 生成脚本失败: ${error.message}`);
  }
};

/**
 * 构建第一个Agent的系统提示词
 * @returns {string} 系统提示词
 */
const buildLogicParserSystemPrompt = () => {
  return `你是一个专业的股票筛选逻辑分析专家。你的任务是判断用户的筛选需求是否需要拆分成多个子逻辑。

**什么情况需要拆分子逻辑：**
1. 需要复杂计算的逻辑（如：计算多年平均值、增长率趋势、排名等）
2. 需要多步骤处理的逻辑（如：先筛选A条件，再基于结果计算B指标）
3. 涉及跨时间段的复杂分析（如：连续N年满足某条件的变化趋势）
4. 需要自定义指标计算的逻辑（如：自定义评分、综合排名等）

**什么情况不需要拆分：**
1. 简单的条件筛选（如：市盈率在某个范围、行业筛选、单一指标比较）
2. 基本的数据查询（如：连续三年净利润大于0，这只是简单的多年数据筛选）
3. 直接的字段比较（如：收盘价大于某值、成交量在某范围）

**输出格式：**
如果不需要拆分，返回：
{
  "needSplit": false,
  "reason": "不需要拆分的原因",
  "singleLogic": {
    "description": "整体逻辑的中文描述",
    "type": "simple_filter",
    "complexity": "low"
  }
}

如果需要拆分，返回：
{
  "needSplit": true,
  "reason": "需要拆分的原因",
  "subLogics": [
    {
      "description": "子逻辑的高层次中文描述以及具体的逻辑描述（不包含具体字段名）",
      "type": "calculation|complex_filter|ranking|trend_analysis",
      "complexity": "medium|high",
      "priority": 1-10,
      "dependency": "依赖的其他子逻辑索引，如果没有则为null"
    }
  ]
}

**重要原则：**
1. 优先考虑不拆分，只有真正复杂的逻辑才拆分
2. 子逻辑描述要用自然语言，不要包含具体的字段名和操作符
3. 每个子逻辑应该代表一个独立的业务概念
4. 拆分后的子逻辑应该比原始需求更容易理解和实现`;
};

/**
 * 构建第一个Agent的用户提示词
 * @param {string} userInput 用户输入
 * @returns {string} 用户提示词
 */
const buildLogicParserUserPrompt = (userInput) => {
  return `请分析以下用户的股票筛选需求，判断是否需要拆分成子逻辑：

用户需求："${userInput}"

请仔细分析：
1. 这个需求是否涉及复杂计算或多步骤处理？
2. 是否可以通过简单的数据库查询直接实现？
3. 是否需要自定义指标计算或复杂的业务逻辑？

如果是简单的筛选条件（如：连续三年净利润大于0、市盈率在某范围、行业筛选等），请不要拆分。
只有真正复杂的逻辑才需要拆分成子逻辑。

只返回JSON格式，不要任何额外说明。`;
};

/**
 * 构建第二个Agent的系统提示词
 * @returns {string} 系统提示词
 */
const buildScriptGeneratorSystemPrompt = () => {
  // 获取数据模型字段信息
  const dailyFields = getDailyStockDataFieldsInfo();
  const financialFields = getFinancialDataFieldsInfo();
  const basicInfoFields = getStockBasicInfoFieldsInfo();

  return `你是一个专业的MongoDB查询脚本生成专家。你的任务是根据给定的逻辑描述生成完整的、可执行的JavaScript代码。

**可用的数据表和字段：**

**1. DailyStockData (当日股票数据表)：**
${dailyFields}

**2. FinancialData (财务数据表)：**
${financialFields}

**3. StockBasicInfo (股票基本信息表)：**
${basicInfoFields}

**你需要生成的代码特点：**
1. 完整的可执行JavaScript函数
2. 使用Mongoose进行数据库查询
3. 包含错误处理
4. 返回符合条件的股票列表
5. 代码简洁高效，只包含核心逻辑
6. 根据逻辑描述选择合适的字段和操作符

**代码模板结构：**
\`\`\`javascript
const executeLogic = async () => {
  try {
    // 核心查询逻辑
    const result = await Model.find(conditions);
    return result;
  } catch (error) {
    throw new Error(\`查询失败: \${error.message}\`);
  }
};
\`\`\`

**重要要求：**
1. 根据逻辑描述选择正确的数据表和字段
2. 使用正确的MongoDB查询语法和操作符
3. 对于跨年度的财务数据查询，使用聚合管道或多次查询
4. 确保代码可以直接运行
5. 包含基本的错误处理
6. 只返回JavaScript代码，不要任何解释或markdown标记`;
};

/**
 * 构建第二个Agent的用户提示词
 * @param {Object} logicInfo 逻辑信息对象
 * @returns {string} 用户提示词
 */
const buildScriptGeneratorUserPrompt = (logicInfo) => {
  return `请为以下逻辑描述生成完整的可执行JavaScript代码：

逻辑描述：${logicInfo.description}
逻辑类型：${logicInfo.type || 'simple_filter'}

请仔细分析这个逻辑描述，选择合适的数据表、字段和查询条件，生成一个完整的JavaScript函数。

特别注意：
1. 对于"连续N年"的条件，需要查询多个年度的财务数据
2. 选择最合适的字段名（参考上面的字段列表）
3. 使用正确的MongoDB查询语法
4. 确保查询逻辑符合业务需求

只返回JavaScript代码，不要任何额外说明或markdown标记。`;
};

/**
 * 解析逻辑分析响应
 * @param {string} response AI返回的响应
 * @returns {Object} 解析结果
 */
const parseSubLogicsResponse = (response) => {
  try {
    console.log('AI返回的逻辑分析:', response);

    // 去除可能的Markdown代码块标记
    let cleanResponse = response.trim();
    if (cleanResponse.startsWith('```json')) {
      cleanResponse = cleanResponse.replace(/^```json\s*/, '');
    }
    if (cleanResponse.startsWith('```')) {
      cleanResponse = cleanResponse.replace(/^```\s*/, '');
    }
    if (cleanResponse.endsWith('```')) {
      cleanResponse = cleanResponse.replace(/\s*```$/, '');
    }

    // 解析JSON
    const parsed = JSON.parse(cleanResponse);

    // 验证必要字段
    if (typeof parsed.needSplit !== 'boolean') {
      throw new Error('返回结果必须包含needSplit字段');
    }

    return parsed;
  } catch (error) {
    throw new Error(`无法解析AI返回的逻辑分析: ${error.message}。返回内容: ${response}`);
  }
};

/**
 * 解析脚本响应
 * @param {string} response AI返回的响应
 * @returns {string} 脚本代码
 */
const parseScriptResponse = (response) => {
  try {
    console.log('AI返回的脚本代码:', response);

    // 去除可能的Markdown代码块标记
    let cleanResponse = response.trim();
    if (cleanResponse.startsWith('```javascript')) {
      cleanResponse = cleanResponse.replace(/^```javascript\s*/, '');
    }
    if (cleanResponse.startsWith('```js')) {
      cleanResponse = cleanResponse.replace(/^```js\s*/, '');
    }
    if (cleanResponse.startsWith('```')) {
      cleanResponse = cleanResponse.replace(/^```\s*/, '');
    }
    if (cleanResponse.endsWith('```')) {
      cleanResponse = cleanResponse.replace(/\s*```$/, '');
    }

    // 验证代码是否包含基本的函数结构
    if (!cleanResponse.includes('async') || !cleanResponse.includes('await')) {
      console.warn('生成的代码可能不包含异步函数结构');
    }

    return cleanResponse;
  } catch (error) {
    throw new Error(`无法解析AI返回的脚本代码: ${error.message}。返回内容: ${response}`);
  }
};

/**
 * 获取当日股票数据字段信息
 * @returns {string} 字段信息字符串
 */
const getDailyStockDataFieldsInfo = () => {
  return `- stockCode: 证券代码 (String)
- closePrice: 收盘价 (Number)
- priceChange: 涨跌额 (Number)
- changePercent: 涨跌幅% (Number)
- openPrice: 今开 (Number)
- highPrice: 最高 (Number)
- lowPrice: 最低 (Number)
- volume: 成交量 (Number)
- turnover: 成交额 (Number)
- turnoverRate: 换手率% (Number)
- amplitude: 振幅% (Number)
- peRatioTTM: 市盈率(TTM) (Number)
- pbRatio: 市净率 (Number)
- totalMarketCap: 总市值(亿元) (Number)
- circulatingMarketCap: 流通市值(亿元) (Number)
- volumeRatio: 量比 (Number)
- ytdGain: 今年以来涨幅% (Number)`;
};

/**
 * 获取财务数据字段信息
 * @returns {string} 字段信息字符串
 */
const getFinancialDataFieldsInfo = () => {
  return `- stockCode: 证券代码 (String)
- fiscalYear: 财政年度 (Number)
- data.keyMetrics.netProfit: 净利润(亿元) (Number)
- data.keyMetrics.netProfitGrowthRate: 净利润同比增长率% (Number)
- data.keyMetrics.totalRevenue: 营业总收入(亿元) (Number)
- data.keyMetrics.totalRevenueGrowthRate: 营业总收入同比增长率% (Number)
- data.keyMetrics.netProfitMargin: 销售净利率% (Number)
- data.keyMetrics.grossProfitMargin: 销售毛利率% (Number)
- data.keyMetrics.basicEarningsPerShare: 基本每股收益 (Number)
- data.keyMetrics.returnOnEquity: 净资产收益率% (Number)
- data.keyMetrics.currentRatio: 流动比率 (Number)
- data.keyMetrics.debtToAssetRatio: 资产负债率% (Number)`;
};

/**
 * 获取股票基本信息字段信息
 * @returns {string} 字段信息字符串
 */
const getStockBasicInfoFieldsInfo = () => {
  return `- stockCode: 证券代码 (String)
- stockName: 证券简称 (String)
- board: 板块 (String) [沪市主板, 深市主板, 创业板, 科创板]
- industry: 行业 (String)
- listingDate: 上市时间 (Date)`;
};

module.exports = {
  generateFilterScript
};
