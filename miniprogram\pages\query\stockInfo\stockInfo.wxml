<!-- 股票信息页面 -->
<view class="stock-info-page">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="stock-code">{{stockInfo.basicInfo['证券代码'] || '--'}}</text>
      <text class="stock-name">{{stockInfo.basicInfo['证券简称'] || '--'}}</text>
    </view>
  </view>

  <!-- 基本信息卡片 -->
  <view class="basic-info-card" wx:if="{{stockInfo.basicInfo}}">
    <view class="card-header">
      <text class="card-title">基本信息</text>
    </view>
    <view class="info-grid">
      <view class="info-item" wx:for="{{basicInfoList}}" wx:key="label">
        <text class="info-label">{{item.label}}</text>
        <text class="info-value">{{item.value}}</text>
      </view>
    </view>
  </view>

  <!-- 数据类型切换 -->
  <view class="data-tabs" wx:if="{{dataTypes.length > 0}}">
    <scroll-view class="tabs-scroll" scroll-x="{{true}}">
      <view class="tab-list">
        <view class="tab-item {{activeTab === item.key ? 'active' : ''}}" 
              wx:for="{{dataTypes}}" 
              wx:key="key"
              bindtap="switchTab" 
              data-tab="{{item.key}}">
          <text class="tab-text">{{item.label}}</text>
          <view class="tab-indicator" wx:if="{{activeTab === item.key}}"></view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 数据内容区域 -->
  <view class="data-content">
    <!-- 数据卡片 -->
    <view class="data-card" wx:if="{{currentDataList.length > 0}}">
      <view class="card-header">
        <text class="card-title">{{currentTabInfo.label}}</text>
        <text class="card-subtitle" wx:if="{{currentTabInfo.subtitle}}">{{currentTabInfo.subtitle}}</text>
      </view>
      <view class="data-grid">
        <view class="data-item" wx:for="{{currentDataList}}" wx:key="label">
          <text class="data-label">{{item.label}}</text>
          <text class="data-value {{item.valueClass || ''}}">{{item.value}}</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{currentDataList.length === 0 && !loading}}">
      <view class="empty-icon">📊</view>
      <text class="empty-title">暂无数据</text>
      <text class="empty-desc">该项目暂时没有可用数据</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="action-button secondary" bindtap="goBack">
      返回
    </button>
    <button class="action-button primary" bindtap="refresh">
      刷新数据
    </button>
  </view>
</view>
