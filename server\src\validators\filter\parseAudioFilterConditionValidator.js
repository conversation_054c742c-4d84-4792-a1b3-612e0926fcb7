/**
 * 验证语音解析请求
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象  
 * @param {Function} next 下一个中间件
 */
const validateParseAudioFilterCondition = (req, res, next) => {
  try {
    const { audioData, format } = req.body;
    
    // 验证音频数据是否存在
    if (!audioData) {
      return res.status(400).json({
        success: false,
        message: '请提供音频数据'
      });
    }
    
    // 验证音频数据格式
    if (typeof audioData !== 'string') {
      return res.status(400).json({
        success: false,
        message: '音频数据必须是字符串格式'
      });
    }
    
    // 验证Base64格式
    const isValidBase64 = validateBase64Format(audioData);
    if (!isValidBase64) {
      return res.status(400).json({
        success: false,
        message: '音频数据必须是有效的Base64格式'
      });
    }
    
    // 验证音频格式参数
    if (format && !isSupportedFormat(format)) {
      return res.status(400).json({
        success: false,
        message: '不支持的音频格式，支持的格式: wav, mp3, pcm, webm, ogg'
      });
    }
    
    // 验证音频数据大小
    const audioSize = estimateAudioSize(audioData);
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (audioSize > maxSize) {
      return res.status(400).json({
        success: false,
        message: '音频数据大小超过限制（最大10MB）'
      });
    }
    
    console.log(`接收到音频数据，格式: ${format || 'wav'}, 估计大小: ${Math.round(audioSize / 1024)} KB`);
    
    next();
  } catch (error) {
    console.error('音频数据验证失败:', error);
    return res.status(400).json({
      success: false,
      message: '音频数据验证失败'
    });
  }
};

/**
 * 验证Base64格式
 * @param {string} audioData Base64音频数据
 * @returns {boolean} 是否有效
 */
const validateBase64Format = (audioData) => {
  if (!audioData || audioData.length === 0) {
    return false;
  }
  
  // 检查是否是有效的Base64格式 (data:audio/...;base64,xxxx 或者 直接的base64字符串)
  const base64WithHeader = /^data:audio\/[^;]+;base64,/.test(audioData);
  const pureBase64 = /^[A-Za-z0-9+/]*={0,2}$/.test(audioData);
  
  return base64WithHeader || pureBase64;
};

/**
 * 验证音频格式是否支持
 * @param {string} format 音频格式
 * @returns {boolean} 是否支持
 */
const isSupportedFormat = (format) => {
  const supportedFormats = ['wav', 'mp3', 'pcm', 'webm', 'ogg'];
  return supportedFormats.includes(format.toLowerCase());
};

/**
 * 估算音频数据大小
 * @param {string} audioBase64 Base64音频数据
 * @returns {number} 估算大小（字节）
 */
const estimateAudioSize = (audioBase64) => {
  // 提取纯Base64数据
  const base64Data = audioBase64.startsWith('data:audio/') 
    ? audioBase64.replace(/^data:audio\/[^;]+;base64,/, '')
    : audioBase64;
  
  // Base64编码后大约是原数据的1.33倍
  return Math.floor((base64Data.length * 3) / 4);
};

/**
 * 验证必需的环境变量
 * @returns {Array} 缺失的环境变量列表
 */
const validateXunfeiConfig = () => {
  const requiredEnvVars = [
    'XF_APP_ID',
    'XF_API_SECRET', 
    'XF_API_KEY'
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  return missingVars;
};

module.exports = {
  validateParseAudioFilterCondition,
  validateXunfeiConfig
};
