/**
 * 存储管理工具
 * 统一管理本地存储的键名和操作
 */

// 存储键名常量
const STORAGE_KEYS = {
  // 用户相关
  ACCESS_TOKEN: 'access_token',
  USER_INFO: 'user_info',
  
  // 应用配置
  APP_CONFIG: 'app_config',
  
  // 缓存数据
  CACHE_PREFIX: 'cache_'
};

/**
 * 保存用户token
 * @param {string|Object} token token字符串或对象
 */
function setToken(token) {
  try {
    const tokenValue = typeof token === 'string' ? token : token.accessToken || token;
    wx.setStorageSync(STORAGE_KEYS.ACCESS_TOKEN, tokenValue);
    console.log('Token保存成功');
  } catch (error) {
    console.error('保存token失败:', error);
  }
}

/**
 * 获取用户token
 * @returns {string|null} token字符串，不存在时返回null
 */
function getToken() {
  try {
    return wx.getStorageSync(STORAGE_KEYS.ACCESS_TOKEN) || null;
  } catch (error) {
    console.error('获取token失败:', error);
    return null;
  }
}

/**
 * 清除用户token
 */
function clearToken() {
  try {
    wx.removeStorageSync(STORAGE_KEYS.ACCESS_TOKEN);
    console.log('Token清除成功');
  } catch (error) {
    console.error('清除token失败:', error);
  }
}

/**
 * 保存用户信息
 * @param {Object} userInfo 用户信息对象
 */
function setUserInfo(userInfo) {
  try {
    wx.setStorageSync(STORAGE_KEYS.USER_INFO, userInfo);
    console.log('用户信息保存成功');
  } catch (error) {
    console.error('保存用户信息失败:', error);
  }
}

/**
 * 获取用户信息
 * @returns {Object|null} 用户信息对象，不存在时返回null
 */
function getUserInfo() {
  try {
    return wx.getStorageSync(STORAGE_KEYS.USER_INFO) || null;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return null;
  }
}

/**
 * 清除用户信息
 */
function clearUserInfo() {
  try {
    wx.removeStorageSync(STORAGE_KEYS.USER_INFO);
    console.log('用户信息清除成功');
  } catch (error) {
    console.error('清除用户信息失败:', error);
  }
}

/**
 * 清除所有用户相关数据
 */
function clearUserData() {
  clearToken();
  clearUserInfo();
}

/**
 * 保存应用配置
 * @param {Object} config 配置对象
 */
function setAppConfig(config) {
  try {
    wx.setStorageSync(STORAGE_KEYS.APP_CONFIG, config);
  } catch (error) {
    console.error('保存应用配置失败:', error);
  }
}

/**
 * 获取应用配置
 * @returns {Object|null} 配置对象
 */
function getAppConfig() {
  try {
    return wx.getStorageSync(STORAGE_KEYS.APP_CONFIG) || null;
  } catch (error) {
    console.error('获取应用配置失败:', error);
    return null;
  }
}

/**
 * 设置缓存数据
 * @param {string} key 缓存键
 * @param {any} data 缓存数据
 * @param {number} expire 过期时间(分钟)，默认60分钟
 */
function setCache(key, data, expire = 60) {
  try {
    const cacheData = {
      data: data,
      expire: Date.now() + expire * 60 * 1000
    };
    wx.setStorageSync(STORAGE_KEYS.CACHE_PREFIX + key, cacheData);
  } catch (error) {
    console.error('设置缓存失败:', error);
  }
}

/**
 * 获取缓存数据
 * @param {string} key 缓存键
 * @returns {any|null} 缓存数据，过期或不存在时返回null
 */
function getCache(key) {
  try {
    const cacheData = wx.getStorageSync(STORAGE_KEYS.CACHE_PREFIX + key);
    if (!cacheData) return null;
    
    if (Date.now() > cacheData.expire) {
      // 缓存过期，清除并返回null
      wx.removeStorageSync(STORAGE_KEYS.CACHE_PREFIX + key);
      return null;
    }
    
    return cacheData.data;
  } catch (error) {
    console.error('获取缓存失败:', error);
    return null;
  }
}

/**
 * 清除缓存
 * @param {string} key 缓存键，不传则清除所有缓存
 */
function clearCache(key) {
  try {
    if (key) {
      wx.removeStorageSync(STORAGE_KEYS.CACHE_PREFIX + key);
    } else {
      // 清除所有缓存
      const info = wx.getStorageInfoSync();
      info.keys.forEach(storageKey => {
        if (storageKey.startsWith(STORAGE_KEYS.CACHE_PREFIX)) {
          wx.removeStorageSync(storageKey);
        }
      });
    }
  } catch (error) {
    console.error('清除缓存失败:', error);
  }
}

module.exports = {
  STORAGE_KEYS,
  setToken,
  getToken,
  clearToken,
  setUserInfo,
  getUserInfo,
  clearUserInfo,
  clearUserData,
  setAppConfig,
  getAppConfig,
  setCache,
  getCache,
  clearCache
};
