const mongoose = require('mongoose');

// 企业主营业务构成数据模型 - 用于存储企业主营业务构成信息
const businessCompositionSchema = new mongoose.Schema({
  // === 基本信息 ===
  // 证券代码
  stockCode: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  
  // 报告日期
  reportDate: {
    type: Date,
    required: true,
    index: true
  },
  
  // 分类类型
  classificationType: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  
  // 主营构成
  businessComposition: {
    type: String,
    required: true,
    trim: true
  },
  
  
  // === 收入相关 ===
  // 主营收入
  mainRevenue: {
    type: Number,
    default: null
  },
  
  // 收入比例
  revenueRatio: {
    type: Number,
    default: null
  },
  
  
  // === 成本相关 ===
  // 主营成本
  mainCost: {
    type: Number,
    default: null
  },
  
  // 成本比例
  costRatio: {
    type: Number,
    default: null
  },
  
  
  // === 利润相关 ===
  // 主营利润
  mainProfit: {
    type: Number,
    default: null
  },
  
  // 利润比例
  profitRatio: {
    type: Number,
    default: null
  },
  
  // 毛利率
  grossProfitMargin: {
    type: Number,
    default: null
  },
  
  
  // 创建时间
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  // 更新时间
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  // 自动添加 createdAt 和 updatedAt
  timestamps: true,
  
  // 设置集合名称
  collection: 'business_composition'
});

// 复合索引 - 确保同一股票同一报告日期同一分类类型同一主营构成的唯一性
businessCompositionSchema.index({ 
  stockCode: 1, 
  reportDate: 1, 
  classificationType: 1,
  businessComposition: 1
}, { unique: true });

// 普通索引
businessCompositionSchema.index({ stockCode: 1, reportDate: -1 }); // 按证券代码和报告日期排序
businessCompositionSchema.index({ reportDate: -1 }); // 按报告日期排序
businessCompositionSchema.index({ classificationType: 1 }); // 按分类类型查询
businessCompositionSchema.index({ revenueRatio: -1 }); // 按收入比例排序
businessCompositionSchema.index({ grossProfitMargin: -1 }); // 按毛利率排序

// === 字段映射 ===

// 获取中英文字段映射
businessCompositionSchema.statics.getFieldMapping = function() {
  return {
    '证券代码': 'stockCode',
    '报告日期': 'reportDate',
    '分类类型': 'classificationType',
    '主营构成': 'businessComposition',
    '主营收入(亿元)': 'mainRevenue',
    '收入比例(%)': 'revenueRatio',
    '主营成本(亿元)': 'mainCost',
    '成本比例(%)': 'costRatio',
    '主营利润(亿元)': 'mainProfit',
    '利润比例(%)': 'profitRatio',
    '毛利率(%)': 'grossProfitMargin'
  };
};

// 获取字段的中文名称
businessCompositionSchema.statics.getChineseFieldName = function(englishField) {
  const mapping = this.getFieldMapping();
  for (const [chinese, english] of Object.entries(mapping)) {
    if (english === englishField) {
      return chinese;
    }
  }
  return englishField;
};

// 获取字段的英文名称
businessCompositionSchema.statics.getEnglishFieldName = function(chineseField) {
  const mapping = this.getFieldMapping();
  return mapping[chineseField] || chineseField;
};

// === 静态方法 ===

// 根据证券代码查找业务构成数据
businessCompositionSchema.statics.findByStockCode = function(stockCode, options = {}) {
  const query = { stockCode };
  if (options.startDate && options.endDate) {
    query.reportDate = { $gte: new Date(options.startDate), $lte: new Date(options.endDate) };
  }
  if (options.classificationType) {
    query.classificationType = options.classificationType;
  }
  
  return this.find(query).sort({ reportDate: -1, revenueRatio: -1 });
};

// 获取最新的业务构成数据
businessCompositionSchema.statics.findLatestByStockCode = function(stockCode, classificationType = null) {
  const query = { stockCode };
  if (classificationType) {
    query.classificationType = classificationType;
  }
  
  return this.find(query).sort({ reportDate: -1 }).limit(20);
};

// 根据报告日期查找
businessCompositionSchema.statics.findByReportDate = function(startDate, endDate, options = {}) {
  const query = {
    reportDate: { $gte: new Date(startDate), $lte: new Date(endDate) }
  };
  
  if (options.stockCodes && options.stockCodes.length > 0) {
    query.stockCode = { $in: options.stockCodes };
  }
  
  if (options.classificationType) {
    query.classificationType = options.classificationType;
  }
  
  return this.find(query)
    .sort({ reportDate: -1, stockCode: 1, revenueRatio: -1 })
    .limit(options.limit || 0)
    .skip(options.skip || 0);
};

// 获取股票的主要收入构成（按收入比例排序）
businessCompositionSchema.statics.findMainRevenueComposition = function(stockCode, reportDate, limit = 10) {
  const query = { 
    stockCode,
    reportDate: new Date(reportDate),
    revenueRatio: { $ne: null, $gt: 0 }
  };
  
  return this.find(query)
    .sort({ revenueRatio: -1 })
    .limit(limit);
};

// 获取按分类类型分组的业务构成
businessCompositionSchema.statics.findByClassificationType = function(stockCode, reportDate, classificationType) {
  const query = { 
    stockCode,
    reportDate: new Date(reportDate),
    classificationType
  };
  
  return this.find(query).sort({ revenueRatio: -1 });
};

// 查找高毛利率的业务构成
businessCompositionSchema.statics.findHighGrossProfitMargin = function(stockCode, reportDate, minMargin = 0.3) {
  const query = { 
    stockCode,
    reportDate: new Date(reportDate),
    grossProfitMargin: { $gte: minMargin }
  };
  
  return this.find(query).sort({ grossProfitMargin: -1 });
};

// === 实例方法 ===

// 格式化显示
businessCompositionSchema.methods.toDisplayString = function() {
  const year = this.reportDate.getFullYear();
  const month = this.reportDate.getMonth() + 1;
  const day = this.reportDate.getDate();
  return `${this.stockCode} - ${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} ${this.businessComposition}`;
};

// 获取格式化的收入比例
businessCompositionSchema.methods.getFormattedRevenueRatio = function() {
  if (this.revenueRatio === null) return 'N/A';
  return `${(this.revenueRatio * 100).toFixed(2)}%`;
};

// 获取格式化的毛利率
businessCompositionSchema.methods.getFormattedGrossProfitMargin = function() {
  if (this.grossProfitMargin === null) return 'N/A';
  return `${(this.grossProfitMargin * 100).toFixed(2)}%`;
};

// 获取格式化的主营收入
businessCompositionSchema.methods.getFormattedMainRevenue = function() {
  if (this.mainRevenue === null) return 'N/A';
  
  if (this.mainRevenue >= 100000000) {
    return `${(this.mainRevenue / 100000000).toFixed(2)}亿元`;
  } else if (this.mainRevenue >= 10000) {
    return `${(this.mainRevenue / 10000).toFixed(2)}万元`;
  }
  return `${this.mainRevenue.toFixed(2)}元`;
};

// 获取业务构成摘要
businessCompositionSchema.methods.getBusinessSummary = function() {
  return {
    businessComposition: this.businessComposition,
    classificationType: this.classificationType,
    mainRevenue: this.getFormattedMainRevenue(),
    revenueRatio: this.getFormattedRevenueRatio(),
    grossProfitMargin: this.getFormattedGrossProfitMargin(),
    costRatio: this.costRatio ? `${(this.costRatio * 100).toFixed(2)}%` : 'N/A',
    profitRatio: this.profitRatio ? `${(this.profitRatio * 100).toFixed(2)}%` : 'N/A'
  };
};

// 检查是否为主要业务（收入比例>10%）
businessCompositionSchema.methods.isMainBusiness = function() {
  return this.revenueRatio && this.revenueRatio > 0.1;
};

// 检查是否为高毛利业务（毛利率>30%）
businessCompositionSchema.methods.isHighMarginBusiness = function() {
  return this.grossProfitMargin && this.grossProfitMargin > 0.3;
};

// 中间件：保存前自动更新时间
businessCompositionSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// 导出模型
module.exports = mongoose.model('BusinessComposition', businessCompositionSchema);
