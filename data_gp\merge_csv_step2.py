#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并沪深A股上市公司CSV文件
将沪市主板A股.csv、沪市科创板A股.csv、深市主板A股.csv合并为一个文件
"""

import pandas as pd
import os

def merge_stock_csv_files():
    """
    合并三个CSV文件，统一列名格式
    """
    try:
        # 读取沪市主板A股.csv
        print("正在读取沪市主板A股.csv...")
        sh_main = pd.read_csv('沪市主板A股.csv', encoding='utf-8', dtype={'证券代码': str})
        
        # 读取沪市科创板A股.csv
        print("正在读取沪市科创板A股.csv...")
        sh_star = pd.read_csv('沪市科创板A股.csv', encoding='utf-8', dtype={'证券代码': str})
        
        # 读取深市主板A股.csv
        print("正在读取深市主板A股.csv...")
        sz_main = pd.read_csv('深市主板A股.csv', encoding='utf-8', dtype={'A股代码': str})
        
        # 处理沪市主板数据
        print("正在处理沪市主板数据...")
        sh_main_processed = sh_main[['证券代码', '证券简称']].copy()
        sh_main_processed['板块'] = '沪市主板'
        sh_main_processed = sh_main_processed[['板块', '证券代码', '证券简称']]
        
        # 处理沪市科创板数据
        print("正在处理沪市科创板数据...")
        sh_star_processed = sh_star[['证券代码', '证券简称']].copy()
        sh_star_processed['板块'] = '科创板'
        sh_star_processed = sh_star_processed[['板块', '证券代码', '证券简称']]
        
        # 处理深市主板数据
        print("正在处理深市主板数据...")
        sz_main_processed = sz_main[['板块', 'A股代码', 'A股简称']].copy()
        # 重命名列名以统一格式
        sz_main_processed = sz_main_processed.rename(columns={
            'A股代码': '证券代码',
            'A股简称': '证券简称'
        })
        # 将"主板"改为"深市主板"
        sz_main_processed['板块'] = sz_main_processed['板块'].replace('主板', '深市主板')
        
        # 合并所有数据
        print("正在合并数据...")
        merged_data = pd.concat([
            sh_main_processed,
            sh_star_processed,
            sz_main_processed
        ], ignore_index=True)
        
        # 确保股票代码格式统一（6位数字，前导零补齐）
        print("正在统一股票代码格式...")
        merged_data['证券代码'] = merged_data['证券代码'].str.zfill(6)
        
        # 去重（如果有重复的证券代码）
        print("正在去重...")
        merged_data = merged_data.drop_duplicates(subset=['证券代码'], keep='first')
        
        # 自定义板块排序
        print("正在排序...")
        # 定义板块排序顺序
        sector_order = {'沪市主板': 1, '深市主板': 2, '创业板': 3, '科创板': 4}
        
        # 创建排序键
        merged_data['板块排序'] = merged_data['板块'].map(sector_order)
        
        # 按自定义板块顺序和证券代码排序
        merged_data = merged_data.sort_values(['板块排序', '证券代码'])
        
        # 删除辅助排序列
        merged_data = merged_data.drop('板块排序', axis=1)
        
        # 保存合并后的文件
        output_filename = '沪深上市公司列表.csv'
        print(f"正在保存到 {output_filename}...")
        merged_data.to_csv(output_filename, index=False, encoding='utf-8-sig')
        
        # 输出统计信息
        print("\n合并完成！")
        print(f"输出文件: {output_filename}")
        print(f"总记录数: {len(merged_data)}")
        print("\n各板块统计:")
        # 按自定义顺序显示统计结果
        sector_counts = merged_data['板块'].value_counts()
        ordered_sectors = ['沪市主板', '深市主板', '创业板', '科创板']
        for sector in ordered_sectors:
            if sector in sector_counts:
                print(f"{sector}: {sector_counts[sector]}")
        
        return True
        
    except FileNotFoundError as e:
        print(f"错误: 找不到文件 - {e}")
        return False
    except Exception as e:
        print(f"错误: {e}")
        return False

if __name__ == "__main__":
    print("开始合并沪深A股上市公司CSV文件...")
    print("=" * 50)
    
    success = merge_stock_csv_files()
    
    if success:
        print("\n" + "=" * 50)
        print("合并操作成功完成！")
    else:
        print("\n" + "=" * 50)
        print("合并操作失败，请检查文件是否存在且格式正确。")
