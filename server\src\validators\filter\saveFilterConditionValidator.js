const Joi = require('joi');

// 筛选条件的验证规则（不包含targetDate）
const filterConditionsSchema = Joi.object({
  dailyConditions: Joi.object().default({}),
  financialConditions: Joi.object().default({}),
  basicInfoConditions: Joi.object().default({})
});

// 保存筛选条件的验证规则
const saveFilterConditionValidator = {
  body: Joi.object({
    name: Joi.string()
      .trim()
      .min(1)
      .max(50)
      .required()
      .messages({
        'string.empty': '筛选条件名称不能为空',
        'string.min': '筛选条件名称至少1个字符',
        'string.max': '筛选条件名称不能超过50个字符',
        'any.required': '筛选条件名称不能为空'
      }),
    
    filterConditions: filterConditionsSchema
      .required()
      .messages({
        'any.required': '筛选条件不能为空'
      })
  }).required()
};

// 获取筛选条件详情的验证规则
const getFilterConditionValidator = {
  params: Joi.object({
    id: Joi.string()
      .pattern(/^[0-9a-fA-F]{24}$/)
      .required()
      .messages({
        'string.pattern.base': '筛选条件ID格式不正确',
        'any.required': '筛选条件ID不能为空'
      })
  }).required()
};

// 重命名筛选条件的验证规则
const renameFilterConditionValidator = {
  params: Joi.object({
    id: Joi.string()
      .pattern(/^[0-9a-fA-F]{24}$/)
      .required()
      .messages({
        'string.pattern.base': '筛选条件ID格式不正确',
        'any.required': '筛选条件ID不能为空'
      })
  }).required(),
  
  body: Joi.object({
    name: Joi.string()
      .trim()
      .min(1)
      .max(50)
      .required()
      .messages({
        'string.empty': '筛选条件名称不能为空',
        'string.min': '筛选条件名称至少1个字符',
        'string.max': '筛选条件名称不能超过50个字符',
        'any.required': '筛选条件名称不能为空'
      })
  }).required()
};

/**
 * 验证保存筛选条件请求的中间件
 * @param {Object} req Express请求对象
 * @param {Object} res Express响应对象
 * @param {Function} next Express下一个中间件
 */
const validateSaveConditionRequest = (req, res, next) => {
  const { error, value } = saveFilterConditionValidator.body.validate(req.body, {
    abortEarly: false,
    allowUnknown: false
  });

  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));

    return res.status(400).json({
      success: false,
      message: '请求参数验证失败',
      errors
    });
  }

  req.validatedData = value;
  next();
};

/**
 * 验证获取筛选条件详情请求的中间件
 * @param {Object} req Express请求对象
 * @param {Object} res Express响应对象
 * @param {Function} next Express下一个中间件
 */
const validateGetConditionRequest = (req, res, next) => {
  const { error, value } = getFilterConditionValidator.params.validate(req.params, {
    abortEarly: false,
    allowUnknown: false
  });

  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));

    return res.status(400).json({
      success: false,
      message: '请求参数验证失败',
      errors
    });
  }

  req.validatedParams = value;
  next();
};

/**
 * 验证删除筛选条件请求的中间件
 * @param {Object} req Express请求对象
 * @param {Object} res Express响应对象
 * @param {Function} next Express下一个中间件
 */
const validateDeleteConditionRequest = (req, res, next) => {
  const { error, value } = getFilterConditionValidator.params.validate(req.params, {
    abortEarly: false,
    allowUnknown: false
  });

  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));

    return res.status(400).json({
      success: false,
      message: '请求参数验证失败',
      errors
    });
  }

  req.validatedParams = value;
  next();
};

/**
 * 验证重命名筛选条件请求的中间件
 * @param {Object} req Express请求对象
 * @param {Object} res Express响应对象
 * @param {Function} next Express下一个中间件
 */
const validateRenameConditionRequest = (req, res, next) => {
  // 验证参数
  const { error: paramsError, value: paramsValue } = renameFilterConditionValidator.params.validate(req.params, {
    abortEarly: false,
    allowUnknown: false
  });

  if (paramsError) {
    const errors = paramsError.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));

    return res.status(400).json({
      success: false,
      message: '请求参数验证失败',
      errors
    });
  }

  // 验证请求体
  const { error: bodyError, value: bodyValue } = renameFilterConditionValidator.body.validate(req.body, {
    abortEarly: false,
    allowUnknown: false
  });

  if (bodyError) {
    const errors = bodyError.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));

    return res.status(400).json({
      success: false,
      message: '请求参数验证失败',
      errors
    });
  }

  req.validatedParams = paramsValue;
  req.validatedData = bodyValue;
  next();
};

module.exports = {
  saveFilterConditionValidator,
  getFilterConditionValidator,
  renameFilterConditionValidator,
  validateSaveConditionRequest,
  validateGetConditionRequest,
  validateDeleteConditionRequest,
  validateRenameConditionRequest
};