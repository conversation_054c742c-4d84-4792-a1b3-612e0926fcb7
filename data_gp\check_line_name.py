import pandas as pd
import os

def check_columns_inclusion():
    # 文件路径
    file1 = "企业当日数据_全字段_雪球为主_20250910.csv"
    file2 = "企业历史日级别价格数据_全字段_end20250904/000001.csv"
    
    try:
        # 检查文件是否存在
        if not os.path.exists(file1):
            print(f"错误：文件 '{file1}' 不存在")
            return
        
        if not os.path.exists(file2):
            print(f"错误：文件 '{file2}' 不存在")
            return
        
        # 读取两个CSV文件的列名
        print("正在读取文件...")
        
        # 只读取第一行来获取列名，避免加载大量数据
        df1 = pd.read_csv(file1, nrows=0)
        df2 = pd.read_csv(file2, nrows=0)
        
        # 获取列名集合
        columns1 = set(df1.columns)
        columns2 = set(df2.columns)
        
        print(f"\n文件1 '{file1}' 共有 {len(columns1)} 个字段")
        print(f"文件2 '{file2}' 共有 {len(columns2)} 个字段")
        
        # 检查文件2中不在文件1中的列
        missing_columns = columns2 - columns1
        
        if missing_columns:
            print(f"\n文件2中有 {len(missing_columns)} 个字段不在文件1中：")
            print("-" * 50)
            for col in sorted(missing_columns):
                print(f"  {col}")
        else:
            print("\n✓ 文件2的所有列名字段都包含在文件1中")
        
        # 可选：显示共同的字段数量
        common_columns = columns1 & columns2
        print(f"\n共同字段数量: {len(common_columns)}")
        
    except FileNotFoundError as e:
        print(f"文件未找到错误: {e}")
    except pd.errors.EmptyDataError:
        print("错误: CSV文件为空")
    except pd.errors.ParserError as e:
        print(f"CSV解析错误: {e}")
    except Exception as e:
        print(f"发生未知错误: {e}")

if __name__ == "__main__":
    check_columns_inclusion()
