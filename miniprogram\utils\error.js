/**
 * 错误处理工具
 */

/**
 * 错误类型枚举
 */
const ERROR_TYPES = {
  NETWORK: 'NETWORK_ERROR',
  AUTH: 'AUTH_ERROR', 
  VALIDATION: 'VALIDATION_ERROR',
  BUSINESS: 'BUSINESS_ERROR',
  SYSTEM: 'SYSTEM_ERROR'
};

/**
 * 自定义错误类
 */
class AppError extends Error {
  constructor(message, type = ERROR_TYPES.SYSTEM, code = null, data = null) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.code = code;
    this.data = data;
    this.timestamp = new Date().toISOString();
  }
}

/**
 * 错误上报
 * @param {Error|string} error 错误对象或错误信息
 * @param {Object} context 错误上下文
 */
function reportError(error, context = {}) {
  const errorInfo = {
    message: error.message || error,
    type: error.type || ERROR_TYPES.SYSTEM,
    code: error.code || null,
    stack: error.stack || null,
    context: context,
    userAgent: wx.getSystemInfoSync(),
    timestamp: new Date().toISOString(),
    route: getCurrentPages().map(page => page.route).join(' -> ')
  };
  
  console.error('错误上报:', errorInfo);
  
  // 这里可以调用后端接口上报错误
  // wx.request({
  //   url: getApp().globalData.baseUrl + '/error/report',
  //   method: 'POST',
  //   data: errorInfo,
  //   fail: (err) => {
  //     console.error('错误上报失败:', err);
  //   }
  // });
}

/**
 * 处理网络错误
 * @param {Object} error 网络错误对象
 * @returns {string} 用户友好的错误信息
 */
function handleNetworkError(error) {
  let message = '网络异常，请检查网络连接';
  
  if (error.errMsg) {
    if (error.errMsg.includes('timeout')) {
      message = '请求超时，请稍后重试';
    } else if (error.errMsg.includes('fail')) {
      message = '网络连接失败，请检查网络设置';
    }
  }
  
  return message;
}

/**
 * 处理API错误
 * @param {Object} response API响应对象
 * @returns {string} 用户友好的错误信息
 */
function handleApiError(response) {
  if (!response || !response.data) {
    return '服务器响应异常';
  }
  
  const { code, message, msg } = response.data;
  
  // 根据错误码返回友好提示
  switch (code) {
    case 401:
      return '登录已过期，请重新登录';
    case 403:
      return '权限不足，无法访问';
    case 404:
      return '请求的资源不存在';
    case 500:
      return '服务器内部错误，请稍后重试';
    default:
      return message || msg || '操作失败，请稍后重试';
  }
}

/**
 * 显示错误提示
 * @param {Error|string} error 错误对象或错误信息
 * @param {Object} options 显示选项
 */
function showError(error, options = {}) {
  const {
    title = '提示',
    duration = 2000,
    showModal = false
  } = options;
  
  let message = '';
  
  if (typeof error === 'string') {
    message = error;
  } else if (error.type === ERROR_TYPES.NETWORK) {
    message = handleNetworkError(error);
  } else {
    message = error.message || '操作失败';
  }
  
  if (showModal) {
    wx.showModal({
      title: title,
      content: message,
      showCancel: false
    });
  } else {
    wx.showToast({
      title: message,
      icon: 'error',
      duration: duration
    });
  }
}

module.exports = {
  ERROR_TYPES,
  AppError,
  reportError,
  handleNetworkError,
  handleApiError,
  showError
};
