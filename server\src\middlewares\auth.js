const jwt = require('jsonwebtoken');
const User = require('../models/User');

/**
 * 保护路由，验证用户是否已认证
 */
exports.protect = async (req, res, next) => {
  try {
    let token;

    // 从请求头中获取token
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    // 检查token是否存在
    if (!token) {
      return res.status(401).json({
        success: false,
        code: 401,
        message: '请先登录',
        timestamp: Date.now()
      });
    }

    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // 根据token中的用户ID获取用户
    const user = await User.findById(decoded.id);

    if (!user) {
      return res.status(401).json({
        success: false,
        code: 401,
        message: '用户不存在或已被删除',
        timestamp: Date.now()
      });
    }

    // 检查用户是否被禁用
    if (!user.isActive) {
      return res.status(403).json({
        success: false,
        code: 403,
        message: '您的账号已被禁用',
        timestamp: Date.now()
      });
    }

    // 将用户信息添加到请求对象中
    req.user = user;
    next();
  } catch (error) {
    console.error('Auth middleware error:', error.message);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        code: 401,
        message: '无效的令牌',
        timestamp: Date.now()
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        code: 401,
        message: '令牌已过期，请重新登录',
        timestamp: Date.now()
      });
    }
    
    return res.status(500).json({
      success: false,
      code: 500,
      message: '认证服务发生错误',
      timestamp: Date.now()
    });
  }
};

/**
 * 限制特定角色访问
 * @param {...String} roles 允许访问的角色列表
 */
exports.authorize = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        code: 403,
        message: '您没有权限执行此操作',
        timestamp: Date.now()
      });
    }
    next();
  };
};

/**
 * 管理员权限验证中间件
 */
exports.admin = (req, res, next) => {
  if (!req.user.isAdmin) {
    return res.status(403).json({
      success: false,
      code: 403,
      message: '需要管理员权限',
      timestamp: Date.now()
    });
  }
  next();
}; 