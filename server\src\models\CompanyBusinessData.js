const mongoose = require('mongoose');

// 企业业务数据模型 - 用于存储企业主营业务信息
const companyBusinessDataSchema = new mongoose.Schema({
  // === 基本信息 ===
  // 证券代码
  stockCode: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  
  
  // === 业务信息 ===
  // 主营业务
  mainBusiness: {
    type: String,
    default: null,
    trim: true
  },
  
  // 产品类型
  productType: {
    type: String,
    default: null,
    trim: true
  },
  
  // 产品名称
  productName: {
    type: String,
    default: null,
    trim: true
  },
  
  // 经营范围
  businessScope: {
    type: String,
    default: null,
    trim: true
  },
  
  
  // 创建时间
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  // 更新时间
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  // 自动添加 createdAt 和 updatedAt
  timestamps: true,
  
  // 设置集合名称
  collection: 'company_business_data'
});

// 唯一索引 - 确保股票代码的唯一性
companyBusinessDataSchema.index({ stockCode: 1 }, { unique: true });

// === 字段映射 ===

// 获取中英文字段映射
companyBusinessDataSchema.statics.getFieldMapping = function() {
  return {
    '证券代码': 'stockCode',
    '主营业务': 'mainBusiness',
    '产品类型': 'productType',
    '产品名称': 'productName',
    '经营范围': 'businessScope'
  };
};

// 获取字段的中文名称
companyBusinessDataSchema.statics.getChineseFieldName = function(englishField) {
  const mapping = this.getFieldMapping();
  for (const [chinese, english] of Object.entries(mapping)) {
    if (english === englishField) {
      return chinese;
    }
  }
  return englishField;
};

// 获取字段的英文名称
companyBusinessDataSchema.statics.getEnglishFieldName = function(chineseField) {
  const mapping = this.getFieldMapping();
  return mapping[chineseField] || chineseField;
};

// === 静态方法 ===

// 根据股票代码查找业务数据
companyBusinessDataSchema.statics.findByStockCode = function(stockCode) {
  return this.findOne({ stockCode });
};

// 批量查找多个股票的业务数据
companyBusinessDataSchema.statics.findByStockCodes = function(stockCodes) {
  return this.find({ stockCode: { $in: stockCodes } })
    .sort({ stockCode: 1 });
};

// 搜索主营业务
companyBusinessDataSchema.statics.searchByMainBusiness = function(keyword, options = {}) {
  const query = {
    mainBusiness: { $regex: keyword, $options: 'i' }
  };
  
  return this.find(query)
    .sort({ stockCode: 1 })
    .limit(options.limit || 50)
    .skip(options.skip || 0);
};

// 搜索产品类型
companyBusinessDataSchema.statics.searchByProductType = function(keyword, options = {}) {
  const query = {
    productType: { $regex: keyword, $options: 'i' }
  };
  
  return this.find(query)
    .sort({ stockCode: 1 })
    .limit(options.limit || 50)
    .skip(options.skip || 0);
};

// 搜索经营范围
companyBusinessDataSchema.statics.searchByBusinessScope = function(keyword, options = {}) {
  const query = {
    businessScope: { $regex: keyword, $options: 'i' }
  };
  
  return this.find(query)
    .sort({ stockCode: 1 })
    .limit(options.limit || 50)
    .skip(options.skip || 0);
};

// 综合搜索
companyBusinessDataSchema.statics.searchComprehensive = function(keyword, options = {}) {
  const query = {
    $or: [
      { stockCode: { $regex: keyword, $options: 'i' } },
      { mainBusiness: { $regex: keyword, $options: 'i' } },
      { productType: { $regex: keyword, $options: 'i' } },
      { productName: { $regex: keyword, $options: 'i' } },
      { businessScope: { $regex: keyword, $options: 'i' } }
    ]
  };
  
  return this.find(query)
    .sort({ stockCode: 1 })
    .limit(options.limit || 50)
    .skip(options.skip || 0);
};

// === 实例方法 ===

// 格式化显示
companyBusinessDataSchema.methods.toDisplayString = function() {
  return `${this.stockCode} - 企业业务信息`;
};

// 获取业务摘要
companyBusinessDataSchema.methods.getBusinessSummary = function() {
  return {
    stockCode: this.stockCode,
    mainBusiness: this.mainBusiness ? this.mainBusiness.substring(0, 100) + (this.mainBusiness.length > 100 ? '...' : '') : 'N/A',
    hasProductInfo: !!(this.productType || this.productName),
    hasBusinessScope: !!this.businessScope
  };
};

// 检查是否有完整的业务信息
companyBusinessDataSchema.methods.hasCompleteBusinessInfo = function() {
  return !!(this.mainBusiness && this.productType && this.businessScope);
};

// 获取格式化的主营业务（限制长度）
companyBusinessDataSchema.methods.getFormattedMainBusiness = function(maxLength = 200) {
  if (!this.mainBusiness) return 'N/A';
  
  if (this.mainBusiness.length <= maxLength) {
    return this.mainBusiness;
  }
  
  return this.mainBusiness.substring(0, maxLength) + '...';
};

// 获取格式化的经营范围（限制长度）
companyBusinessDataSchema.methods.getFormattedBusinessScope = function(maxLength = 300) {
  if (!this.businessScope) return 'N/A';
  
  if (this.businessScope.length <= maxLength) {
    return this.businessScope;
  }
  
  return this.businessScope.substring(0, maxLength) + '...';
};

// 中间件：保存前自动更新时间
companyBusinessDataSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// 导出模型
module.exports = mongoose.model('CompanyBusinessData', companyBusinessDataSchema);
