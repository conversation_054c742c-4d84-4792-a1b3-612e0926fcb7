<!--pages/login/login.wxml-->
<view class="login-page">
  <!-- 背景装饰 -->
  <view class="login-background">
    <view class="bg-decoration bg-decoration-1"></view>
    <view class="bg-decoration bg-decoration-2"></view>
    <view class="bg-decoration bg-decoration-3"></view>
  </view>

  <!-- 登录内容 -->
  <view class="login-content">
    <!-- Logo和标题区域 -->
    <view class="login-header text-center">
      <view class="logo-container margin-bottom-lg">
        <image class="app-logo" src="/assets/images/logo.png" mode="aspectFit" />
      </view>
      <view class="app-title text-xxl font-bold text-primary margin-bottom-xs">
        言策AI
      </view>
      <view class="app-subtitle text-lg text-secondary">
        智能量化投资平台
      </view>
      <view class="app-description text-sm text-tertiary margin-top-base">
        专业的AI驱动量化投资解决方案
      </view>
    </view>

    <!-- 间距区域 -->
    <view class="login-spacer"></view>

    <!-- 登录按钮区域 -->
    <view class="login-action">
      <button 
        class="login-button btn-base btn-primary-style full-width text-lg font-medium"
        bindtap="handleWxLogin"
        loading="{{loginLoading}}"
        disabled="{{loginLoading}}"
      >
        <text class="iconfont icon-wechat margin-right-xs" wx:if="{{!loginLoading}}"></text>
        {{loginLoading ? '登录中...' : '微信一键登录'}}
      </button>
      
      <view class="login-tips text-center margin-top-lg">
        <view class="tips-text text-xs text-tertiary">
          登录即表示同意
          <text class="text-primary" bindtap="showUserAgreement">《用户协议》</text>
          和
          <text class="text-primary" bindtap="showPrivacyPolicy">《隐私政策》</text>
        </view>
      </view>
    </view>
  </view>
</view>
