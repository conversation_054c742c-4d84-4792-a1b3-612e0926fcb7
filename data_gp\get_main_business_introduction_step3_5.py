import pandas as pd
import akshare as ak
import time
import os
from typing import Dict, Any
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StockInfoCrawler:
    def __init__(self, input_file: str, output_file: str, batch_size: int = 50):
        self.input_file = input_file
        self.output_file = output_file
        self.batch_size = batch_size
        self.failed_stocks = []
        
    def load_original_data(self) -> pd.DataFrame:
        """加载原始CSV文件，确保证券代码格式正确"""
        df = pd.read_csv(self.input_file, dtype={'证券代码': str})
        # 确保证券代码为6位字符串，前面补0
        df['证券代码'] = df['证券代码'].str.zfill(6)
        return df
    
    def load_existing_data(self) -> pd.DataFrame:
        """加载已存在的输出文件"""
        if os.path.exists(self.output_file):
            df = pd.read_csv(self.output_file, dtype={'证券代码': str})
            df['证券代码'] = df['证券代码'].str.zfill(6)
            return df
        return pd.DataFrame()
    
    def get_stock_business_info(self, stock_code: str, max_retries: int = 3) -> Dict[str, Any]:
        """获取股票业务信息，支持重试机制"""
        for attempt in range(max_retries):
            try:
                logger.info(f"正在获取股票 {stock_code} 的业务信息 (尝试 {attempt + 1}/{max_retries})")
                
                # 调用akshare API
                stock_info = ak.stock_zyjs_ths(symbol=stock_code)
                
                if not stock_info.empty:
                    info = stock_info.iloc[0]
                    return {
                        '主营业务': info.get('主营业务', ''),
                        '产品类型': info.get('产品类型', ''),
                        '产品名称': info.get('产品名称', ''),
                        '经营范围': info.get('经营范围', '')
                    }
                else:
                    logger.warning(f"股票 {stock_code} 返回空数据")
                    
            except Exception as e:
                logger.error(f"获取股票 {stock_code} 信息失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # 重试前等待2秒
        
        # 所有重试都失败，返回空信息
        return {
            '主营业务': '',
            '产品类型': '',
            '产品名称': '',
            '经营范围': ''
        }
    
    def process_stocks(self):
        """处理股票信息获取的主流程"""
        # 加载原始数据
        original_df = self.load_original_data()
        logger.info(f"加载了 {len(original_df)} 只股票")
        
        # 加载已存在的数据
        existing_df = self.load_existing_data()
        
        if not existing_df.empty:
            logger.info(f"发现已存在的输出文件，包含 {len(existing_df)} 条记录")
            # 找出还需要处理的股票
            processed_codes = set(existing_df['证券代码'].astype(str))
            remaining_df = original_df[~original_df['证券代码'].isin(processed_codes)].copy()
            result_df = existing_df.copy()
        else:
            logger.info("未发现已存在的输出文件，将从头开始处理")
            remaining_df = original_df.copy()
            # 初始化结果DataFrame
            result_df = original_df.copy()
            result_df['主营业务'] = ''
            result_df['产品类型'] = ''
            result_df['产品名称'] = ''
            result_df['经营范围'] = ''
        
        logger.info(f"还需要处理 {len(remaining_df)} 只股票")
        
        # 批量处理
        batch_count = 0
        for i in range(0, len(remaining_df), self.batch_size):
            batch_df = remaining_df.iloc[i:i + self.batch_size]
            batch_count += 1
            
            logger.info(f"开始处理第 {batch_count} 批，包含 {len(batch_df)} 只股票")
            
            for idx, row in batch_df.iterrows():
                stock_code = row['证券代码']
                logger.info(f"处理股票: {stock_code} - {row['证券简称']}")
                
                # 获取业务信息
                business_info = self.get_stock_business_info(stock_code)
                
                # 检查是否获取成功
                if all(value == '' for value in business_info.values()):
                    self.failed_stocks.append(stock_code)
                    logger.warning(f"股票 {stock_code} 获取失败，添加到失败列表")
                
                # 更新结果DataFrame
                if existing_df.empty:
                    # 如果是新文件，直接更新
                    result_df.loc[result_df['证券代码'] == stock_code, '主营业务'] = business_info['主营业务']
                    result_df.loc[result_df['证券代码'] == stock_code, '产品类型'] = business_info['产品类型']
                    result_df.loc[result_df['证券代码'] == stock_code, '产品名称'] = business_info['产品名称']
                    result_df.loc[result_df['证券代码'] == stock_code, '经营范围'] = business_info['经营范围']
                else:
                    # 如果是续传，添加新行
                    new_row = row.copy()
                    new_row['主营业务'] = business_info['主营业务']
                    new_row['产品类型'] = business_info['产品类型']
                    new_row['产品名称'] = business_info['产品名称']
                    new_row['经营范围'] = business_info['经营范围']
                    result_df = pd.concat([result_df, new_row.to_frame().T], ignore_index=True)
                
                # 添加延时避免请求过于频繁
                time.sleep(1)
            
            # 批量保存
            result_df.to_csv(self.output_file, index=False, encoding='utf-8-sig')
            logger.info(f"第 {batch_count} 批处理完成，已保存到 {self.output_file}")
        
        # 处理失败的股票
        self.retry_failed_stocks(result_df)
        
        logger.info("所有股票处理完成！")
    
    def retry_failed_stocks(self, result_df: pd.DataFrame):
        """重试失败的股票"""
        if not self.failed_stocks:
            logger.info("没有失败的股票需要重试")
            return
        
        logger.info(f"开始重试 {len(self.failed_stocks)} 只失败的股票")
        
        for retry_round in range(3):
            logger.info(f"第 {retry_round + 1} 轮重试")
            
            if retry_round > 0:
                logger.info("等待1分钟后开始重试...")
                time.sleep(60)
            
            retry_success = []
            
            for stock_code in self.failed_stocks:
                logger.info(f"重试股票: {stock_code}")
                
                business_info = self.get_stock_business_info(stock_code, max_retries=1)
                
                # 如果获取成功，更新数据
                if not all(value == '' for value in business_info.values()):
                    result_df.loc[result_df['证券代码'] == stock_code, '主营业务'] = business_info['主营业务']
                    result_df.loc[result_df['证券代码'] == stock_code, '产品类型'] = business_info['产品类型']
                    result_df.loc[result_df['证券代码'] == stock_code, '产品名称'] = business_info['产品名称']
                    result_df.loc[result_df['证券代码'] == stock_code, '经营范围'] = business_info['经营范围']
                    
                    retry_success.append(stock_code)
                    logger.info(f"股票 {stock_code} 重试成功")
                
                time.sleep(2)
            
            # 从失败列表中移除成功的股票
            for success_code in retry_success:
                self.failed_stocks.remove(success_code)
            
            # 保存更新后的数据
            result_df.to_csv(self.output_file, index=False, encoding='utf-8-sig')
            
            if not self.failed_stocks:
                logger.info("所有失败的股票都已重试成功！")
                break
            else:
                logger.info(f"还有 {len(self.failed_stocks)} 只股票重试失败")
        
        if self.failed_stocks:
            logger.warning(f"最终仍有 {len(self.failed_stocks)} 只股票获取失败: {self.failed_stocks}")

def main():
    # 配置参数
    input_file = "沪深上市公司列表.csv"
    output_file = "沪深上市公司列表_业务信息.csv"
    batch_size = 50  # 每批处理的股票数量
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        logger.error(f"输入文件 {input_file} 不存在！")
        return
    
    # 创建爬虫实例并开始处理
    crawler = StockInfoCrawler(input_file, output_file, batch_size)
    crawler.process_stocks()

if __name__ == "__main__":
    main()
