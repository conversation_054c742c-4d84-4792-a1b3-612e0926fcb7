const { generateFilterScript } = require('./aiFilterScriptGeneratorService');

/**
 * 测试AI智能生成筛选脚本功能
 */
const testGenerateFilterScript = async () => {
  try {
    console.log('开始测试AI智能生成筛选脚本功能...\n');

    // 测试用例1：简单的市盈率筛选
    const testCase1 = "连续三年净利润增长率大于0的企业";
    console.log('测试用例1:', testCase1);
    const result1 = await generateFilterScript(testCase1);
    console.log('结果1:', JSON.stringify(result1, null, 2));
    console.log('\n' + '='.repeat(80) + '\n');

    // // 测试用例2：复杂的财务指标筛选
    // const testCase2 = "筛选净利润增长率超过20%，ROE大于15%，资产负债率小于50%的科技股";
    // console.log('测试用例2:', testCase2);
    // const result2 = await generateFilterScript(testCase2);
    // console.log('结果2:', JSON.stringify(result2, null, 2));
    // console.log('\n' + '='.repeat(80) + '\n');

    // // 测试用例3：技术指标和基本面结合
    // const testCase3 = "找出今年涨幅超过50%，换手率在5%-15%之间，属于新能源行业的创业板股票";
    // console.log('测试用例3:', testCase3);
    // const result3 = await generateFilterScript(testCase3);
    // console.log('结果3:', JSON.stringify(result3, null, 2));

    console.log('\n测试完成！');

  } catch (error) {
    console.error('测试失败:', error.message);
  }
};

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  testGenerateFilterScript();
}

module.exports = {
  testGenerateFilterScript
};
