/* 言策 AI量化投资小程序 - 基础样式文件 */
/* ================================================ */

/* 引入变量文件 */
@import './variables.wxss';

/* ==================== 样式重置 ==================== */
/* 清除默认样式，统一各平台表现 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 重置常用元素的默认样式 */
view, text, image, button, input, textarea, picker, scroll-view, swiper, swiper-item, 
navigator, label, checkbox, radio, switch, slider, progress, icon, rich-text, 
map, canvas, web-view, ad, official-account, open-data, audio, video, camera, 
live-player, live-pusher, movable-area, movable-view, cover-view, cover-image {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 按钮重置 */
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font-size: inherit;
  color: inherit;
  line-height: inherit;
}

button::after {
  border: none;
}

/* 输入框重置 */
input, textarea {
  background: transparent;
  border: none;
  outline: none;
  font-size: inherit;
  color: inherit;
}

/* 图片默认样式 */
image {
  display: block;
  max-width: 100%;
  height: auto;
}

/* ==================== Flex 布局类 ==================== */
/* 基础 flex 容器 */
.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

/* 方向 */
.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-row-reverse {
  flex-direction: row-reverse;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

/* 换行 */
.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse;
}

/* 主轴对齐 */
.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

/* 交叉轴对齐 */
.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.items-stretch {
  align-items: stretch;
}

/* 内容对齐 */
.content-start {
  align-content: flex-start;
}

.content-end {
  align-content: flex-end;
}

.content-center {
  align-content: center;
}

.content-between {
  align-content: space-between;
}

.content-around {
  align-content: space-around;
}

.content-evenly {
  align-content: space-evenly;
}

/* flex 项目 */
.flex-1 {
  flex: 1 1 0%;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-initial {
  flex: 0 1 auto;
}

.flex-none {
  flex: none;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.flex-shrink {
  flex-shrink: 1;
}

.flex-grow-0 {
  flex-grow: 0;
}

.flex-grow {
  flex-grow: 1;
}

/* 自对齐 */
.self-auto {
  align-self: auto;
}

.self-start {
  align-self: flex-start;
}

.self-end {
  align-self: flex-end;
}

.self-center {
  align-self: center;
}

.self-stretch {
  align-self: stretch;
}

.self-baseline {
  align-self: baseline;
}

/* ==================== 间距类 ==================== */
/* Margin */
.m-0 { margin: 0; }
.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-base { margin: var(--spacing-base); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }
.m-xxl { margin: var(--spacing-xxl); }

/* Margin Top */
.mt-0 { margin-top: 0; }
.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-base { margin-top: var(--spacing-base); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }
.mt-xxl { margin-top: var(--spacing-xxl); }

/* Margin Right */
.mr-0 { margin-right: 0; }
.mr-xs { margin-right: var(--spacing-xs); }
.mr-sm { margin-right: var(--spacing-sm); }
.mr-base { margin-right: var(--spacing-base); }
.mr-md { margin-right: var(--spacing-md); }
.mr-lg { margin-right: var(--spacing-lg); }
.mr-xl { margin-right: var(--spacing-xl); }
.mr-xxl { margin-right: var(--spacing-xxl); }

/* Margin Bottom */
.mb-0 { margin-bottom: 0; }
.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-base { margin-bottom: var(--spacing-base); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }
.mb-xxl { margin-bottom: var(--spacing-xxl); }

/* Margin Left */
.ml-0 { margin-left: 0; }
.ml-xs { margin-left: var(--spacing-xs); }
.ml-sm { margin-left: var(--spacing-sm); }
.ml-base { margin-left: var(--spacing-base); }
.ml-md { margin-left: var(--spacing-md); }
.ml-lg { margin-left: var(--spacing-lg); }
.ml-xl { margin-left: var(--spacing-xl); }
.ml-xxl { margin-left: var(--spacing-xxl); }

/* Margin X轴 */
.mx-0 { margin-left: 0; margin-right: 0; }
.mx-xs { margin-left: var(--spacing-xs); margin-right: var(--spacing-xs); }
.mx-sm { margin-left: var(--spacing-sm); margin-right: var(--spacing-sm); }
.mx-base { margin-left: var(--spacing-base); margin-right: var(--spacing-base); }
.mx-md { margin-left: var(--spacing-md); margin-right: var(--spacing-md); }
.mx-lg { margin-left: var(--spacing-lg); margin-right: var(--spacing-lg); }
.mx-xl { margin-left: var(--spacing-xl); margin-right: var(--spacing-xl); }
.mx-xxl { margin-left: var(--spacing-xxl); margin-right: var(--spacing-xxl); }

/* Margin Y轴 */
.my-0 { margin-top: 0; margin-bottom: 0; }
.my-xs { margin-top: var(--spacing-xs); margin-bottom: var(--spacing-xs); }
.my-sm { margin-top: var(--spacing-sm); margin-bottom: var(--spacing-sm); }
.my-base { margin-top: var(--spacing-base); margin-bottom: var(--spacing-base); }
.my-md { margin-top: var(--spacing-md); margin-bottom: var(--spacing-md); }
.my-lg { margin-top: var(--spacing-lg); margin-bottom: var(--spacing-lg); }
.my-xl { margin-top: var(--spacing-xl); margin-bottom: var(--spacing-xl); }
.my-xxl { margin-top: var(--spacing-xxl); margin-bottom: var(--spacing-xxl); }

/* Padding */
.p-0 { padding: 0; }
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-base { padding: var(--spacing-base); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }
.p-xxl { padding: var(--spacing-xxl); }

/* Padding Top */
.pt-0 { padding-top: 0; }
.pt-xs { padding-top: var(--spacing-xs); }
.pt-sm { padding-top: var(--spacing-sm); }
.pt-base { padding-top: var(--spacing-base); }
.pt-md { padding-top: var(--spacing-md); }
.pt-lg { padding-top: var(--spacing-lg); }
.pt-xl { padding-top: var(--spacing-xl); }
.pt-xxl { padding-top: var(--spacing-xxl); }

/* Padding Right */
.pr-0 { padding-right: 0; }
.pr-xs { padding-right: var(--spacing-xs); }
.pr-sm { padding-right: var(--spacing-sm); }
.pr-base { padding-right: var(--spacing-base); }
.pr-md { padding-right: var(--spacing-md); }
.pr-lg { padding-right: var(--spacing-lg); }
.pr-xl { padding-right: var(--spacing-xl); }
.pr-xxl { padding-right: var(--spacing-xxl); }

/* Padding Bottom */
.pb-0 { padding-bottom: 0; }
.pb-xs { padding-bottom: var(--spacing-xs); }
.pb-sm { padding-bottom: var(--spacing-sm); }
.pb-base { padding-bottom: var(--spacing-base); }
.pb-md { padding-bottom: var(--spacing-md); }
.pb-lg { padding-bottom: var(--spacing-lg); }
.pb-xl { padding-bottom: var(--spacing-xl); }
.pb-xxl { padding-bottom: var(--spacing-xxl); }

/* Padding Left */
.pl-0 { padding-left: 0; }
.pl-xs { padding-left: var(--spacing-xs); }
.pl-sm { padding-left: var(--spacing-sm); }
.pl-base { padding-left: var(--spacing-base); }
.pl-md { padding-left: var(--spacing-md); }
.pl-lg { padding-left: var(--spacing-lg); }
.pl-xl { padding-left: var(--spacing-xl); }
.pl-xxl { padding-left: var(--spacing-xxl); }

/* Padding X轴 */
.px-0 { padding-left: 0; padding-right: 0; }
.px-xs { padding-left: var(--spacing-xs); padding-right: var(--spacing-xs); }
.px-sm { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
.px-base { padding-left: var(--spacing-base); padding-right: var(--spacing-base); }
.px-md { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
.px-lg { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }
.px-xl { padding-left: var(--spacing-xl); padding-right: var(--spacing-xl); }
.px-xxl { padding-left: var(--spacing-xxl); padding-right: var(--spacing-xxl); }

/* Padding Y轴 */
.py-0 { padding-top: 0; padding-bottom: 0; }
.py-xs { padding-top: var(--spacing-xs); padding-bottom: var(--spacing-xs); }
.py-sm { padding-top: var(--spacing-sm); padding-bottom: var(--spacing-sm); }
.py-base { padding-top: var(--spacing-base); padding-bottom: var(--spacing-base); }
.py-md { padding-top: var(--spacing-md); padding-bottom: var(--spacing-md); }
.py-lg { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }
.py-xl { padding-top: var(--spacing-xl); padding-bottom: var(--spacing-xl); }
.py-xxl { padding-top: var(--spacing-xxl); padding-bottom: var(--spacing-xxl); }

/* ==================== 文本对齐 ==================== */
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-justify {
  text-align: justify;
}

/* 垂直对齐 */
.align-baseline {
  vertical-align: baseline;
}

.align-top {
  vertical-align: top;
}

.align-middle {
  vertical-align: middle;
}

.align-bottom {
  vertical-align: bottom;
}

.align-text-top {
  vertical-align: text-top;
}

.align-text-bottom {
  vertical-align: text-bottom;
}

/* ==================== 显示隐藏 ==================== */
.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.hidden {
  display: none;
}

.show {
  display: block;
}

.hide {
  display: none;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

/* 透明度 */
.opacity-0 {
  opacity: 0;
}

.opacity-25 {
  opacity: 0.25;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-75 {
  opacity: 0.75;
}

.opacity-100 {
  opacity: 1;
}

/* ==================== 定位 ==================== */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

.static {
  position: static;
}

/* 层级 */
.z-auto {
  z-index: auto;
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

/* ==================== 文本样式 ==================== */
/* 字体大小 */
.text-xs {
  font-size: var(--font-size-xs);
}

.text-sm {
  font-size: var(--font-size-sm);
}

.text-base {
  font-size: var(--font-size-base);
}

.text-lg {
  font-size: var(--font-size-lg);
}

.text-xl {
  font-size: var(--font-size-xl);
}

.text-xxl {
  font-size: var(--font-size-xxl);
}

.text-title {
  font-size: var(--font-size-title);
}

.text-display {
  font-size: var(--font-size-display);
}

/* 字体粗细 */
.font-light {
  font-weight: var(--font-weight-light);
}

.font-normal {
  font-weight: var(--font-weight-normal);
}

.font-medium {
  font-weight: var(--font-weight-medium);
}

.font-semibold {
  font-weight: var(--font-weight-semibold);
}

.font-bold {
  font-weight: var(--font-weight-bold);
}

/* 文本颜色 */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-tertiary {
  color: var(--text-tertiary);
}

.text-disabled {
  color: var(--text-disabled);
}

.text-inverse {
  color: var(--text-inverse);
}

.text-link {
  color: var(--text-link);
}

.text-placeholder {
  color: var(--text-placeholder);
}

/* 主题色文本 */
.text-brand {
  color: var(--primary-color);
}

.text-brand-light {
  color: var(--primary-light);
}

.text-success {
  color: var(--color-success);
}

.text-warning {
  color: var(--color-warning);
}

.text-danger {
  color: var(--color-danger);
}

.text-info {
  color: var(--color-info);
}

/* 文本装饰 */
.underline {
  text-decoration: underline;
}

.line-through {
  text-decoration: line-through;
}

.no-underline {
  text-decoration: none;
}

/* 文本变换 */
.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

.normal-case {
  text-transform: none;
}

/* 文本溢出 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis {
  text-overflow: ellipsis;
}

.text-clip {
  text-overflow: clip;
}

/* 换行 */
.whitespace-normal {
  white-space: normal;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.whitespace-pre {
  white-space: pre;
}

.whitespace-pre-line {
  white-space: pre-line;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

/* 行高 */
.leading-none {
  line-height: 1;
}

.leading-tight {
  line-height: 1.25;
}

.leading-snug {
  line-height: 1.375;
}

.leading-normal {
  line-height: 1.5;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-loose {
  line-height: 2;
}

/* ==================== 背景样式 ==================== */
/* 背景色 */
.bg-primary {
  background-color: var(--bg-primary);
}

.bg-secondary {
  background-color: var(--bg-secondary);
}

.bg-tertiary {
  background-color: var(--bg-tertiary);
}

.bg-card {
  background-color: var(--bg-card);
}

.bg-disabled {
  background-color: var(--bg-disabled);
}

/* 主题背景色 */
.bg-brand {
  background-color: var(--primary-color);
}

.bg-brand-light {
  background-color: var(--primary-light);
}

.bg-brand-lighter {
  background-color: var(--primary-lighter);
}

.bg-success {
  background-color: var(--color-success);
}

.bg-success-light {
  background-color: var(--color-success-light);
}

.bg-warning {
  background-color: var(--color-warning);
}

.bg-warning-light {
  background-color: var(--color-warning-light);
}

.bg-danger {
  background-color: var(--color-danger);
}

.bg-danger-light {
  background-color: var(--color-danger-light);
}

.bg-info {
  background-color: var(--color-info);
}

.bg-info-light {
  background-color: var(--color-info-light);
}

/* 透明背景 */
.bg-transparent {
  background-color: transparent;
}

/* 渐变背景 */
.bg-gradient-primary {
  background: var(--gradient-primary);
}

.bg-gradient-success {
  background: var(--gradient-success);
}

.bg-gradient-warning {
  background: var(--gradient-warning);
}

.bg-gradient-danger {
  background: var(--gradient-danger);
}

/* ==================== 边框样式 ==================== */
/* 边框宽度 */
.border-0 {
  border-width: 0;
}

.border {
  border-width: 1rpx;
}

.border-2 {
  border-width: 2rpx;
}

.border-4 {
  border-width: 4rpx;
}

.border-8 {
  border-width: 8rpx;
}

/* 边框方向 */
.border-t {
  border-top-width: 1rpx;
}

.border-r {
  border-right-width: 1rpx;
}

.border-b {
  border-bottom-width: 1rpx;
}

.border-l {
  border-left-width: 1rpx;
}

/* 边框颜色 */
.border-light {
  border-color: var(--border-light);
}

.border-medium {
  border-color: var(--border-medium);
}

.border-dark {
  border-color: var(--border-dark);
}

.border-brand {
  border-color: var(--primary-color);
}

.border-success {
  border-color: var(--color-success);
}

.border-warning {
  border-color: var(--color-warning);
}

.border-danger {
  border-color: var(--color-danger);
}

.border-info {
  border-color: var(--color-info);
}

/* 边框样式 */
.border-solid {
  border-style: solid;
}

.border-dashed {
  border-style: dashed;
}

.border-dotted {
  border-style: dotted;
}

.border-none {
  border-style: none;
}

/* 圆角 */
.rounded-none {
  border-radius: 0;
}

.rounded-sm {
  border-radius: var(--radius-sm);
}

.rounded {
  border-radius: var(--radius-base);
}

.rounded-md {
  border-radius: var(--radius-md);
}

.rounded-lg {
  border-radius: var(--radius-lg);
}

.rounded-xl {
  border-radius: var(--radius-xl);
}

.rounded-full {
  border-radius: var(--radius-round);
}

.rounded-pill {
  border-radius: var(--radius-pill);
}

/* 单独圆角 */
.rounded-t-sm {
  border-top-left-radius: var(--radius-sm);
  border-top-right-radius: var(--radius-sm);
}

.rounded-r-sm {
  border-top-right-radius: var(--radius-sm);
  border-bottom-right-radius: var(--radius-sm);
}

.rounded-b-sm {
  border-bottom-left-radius: var(--radius-sm);
  border-bottom-right-radius: var(--radius-sm);
}

.rounded-l-sm {
  border-top-left-radius: var(--radius-sm);
  border-bottom-left-radius: var(--radius-sm);
}

/* ==================== 阴影 ==================== */
.shadow-none {
  box-shadow: none;
}

.shadow-light {
  box-shadow: var(--shadow-light);
}

.shadow {
  box-shadow: var(--shadow-base);
}

.shadow-strong {
  box-shadow: var(--shadow-strong);
}

.shadow-card {
  box-shadow: var(--shadow-card);
}

.shadow-float {
  box-shadow: var(--shadow-float);
}

/* ==================== 交互样式 ==================== */
/* 按钮基础样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-base);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: 1.5;
  text-align: center;
  cursor: pointer;
  transition: var(--transition-base);
  border: 1rpx solid transparent;
  user-select: none;
}

.btn:active {
  transform: translateY(1rpx);
}

/* 按钮尺寸 */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: var(--spacing-base) var(--spacing-lg);
  font-size: var(--font-size-lg);
}

.btn-xl {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-xl);
}

/* 按钮类型 */
.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-inverse);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--text-inverse);
}

.btn-success {
  background-color: var(--color-success);
  color: var(--text-inverse);
}

.btn-warning {
  background-color: var(--color-warning);
  color: var(--text-inverse);
}

.btn-danger {
  background-color: var(--color-danger);
  color: var(--text-inverse);
}

.btn-info {
  background-color: var(--color-info);
  color: var(--text-inverse);
}

/* 轮廓按钮 */
.btn-outline {
  background-color: transparent;
  border-color: var(--border-medium);
  color: var(--text-primary);
}

.btn-outline-primary {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-outline-success {
  border-color: var(--color-success);
  color: var(--color-success);
}

.btn-outline-warning {
  border-color: var(--color-warning);
  color: var(--color-warning);
}

.btn-outline-danger {
  border-color: var(--color-danger);
  color: var(--color-danger);
}

/* 文本按钮 */
.btn-text {
  background-color: transparent;
  color: var(--primary-color);
  padding: var(--spacing-xs) var(--spacing-sm);
}

/* 禁用状态 */
.btn:disabled,
.btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* 加载状态 */
.btn.loading {
  opacity: 0.8;
  cursor: not-allowed;
  pointer-events: none;
}

/* 块级按钮 */
.btn-block {
  width: 100%;
  display: flex;
}

/* ==================== 金融专用类 ==================== */
/* 涨跌颜色 */
.price-rise {
  color: var(--color-rise);
}

.price-fall {
  color: var(--color-fall);
}

.price-neutral {
  color: var(--text-secondary);
}

/* 涨跌背景 */
.bg-rise {
  background-color: var(--color-rise);
  color: var(--text-inverse);
}

.bg-fall {
  background-color: var(--color-fall);
  color: var(--text-inverse);
}

.bg-rise-light {
  background-color: var(--color-rise-light);
  color: var(--color-rise);
}

.bg-fall-light {
  background-color: var(--color-fall-light);
  color: var(--color-fall);
}

/* 数字格式 */
.number {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-variant-numeric: tabular-nums;
}

.price {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-variant-numeric: tabular-nums;
  font-weight: var(--font-weight-medium);
}

.percentage {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-variant-numeric: tabular-nums;
}

/* 金融卡片 */
.finance-card {
  background-color: var(--bg-card);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-card);
  padding: var(--spacing-lg);
  border: 1rpx solid var(--border-light);
}

.finance-card:hover {
  box-shadow: var(--shadow-float);
  transform: translateY(-2rpx);
  transition: var(--transition-base);
}

/* 股票代码 */
.stock-code {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  text-transform: uppercase;
}

/* 趋势指示器 */
.trend-up::before {
  content: '↗';
  color: var(--color-rise);
  margin-right: var(--spacing-xs);
}

.trend-down::before {
  content: '↘';
  color: var(--color-fall);
  margin-right: var(--spacing-xs);
}

.trend-flat::before {
  content: '→';
  color: var(--text-secondary);
  margin-right: var(--spacing-xs);
}

/* ==================== 工具类 ==================== */
/* 清除浮动 */
.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

/* 屏幕阅读器隐藏 */
.sr-only {
  position: absolute;
  width: 1rpx;
  height: 1rpx;
  padding: 0;
  margin: -1rpx;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 过渡动画 */
.transition {
  transition: var(--transition-base);
}

.transition-fast {
  transition: var(--transition-fast);
}

.transition-slow {
  transition: var(--transition-slow);
}

/* 用户选择 */
.select-none {
  user-select: none;
}

.select-text {
  user-select: text;
}

.select-all {
  user-select: all;
}

.select-auto {
  user-select: auto;
}

/* 指针事件 */
.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

/* 光标样式 */
.cursor-auto {
  cursor: auto;
}

.cursor-default {
  cursor: default;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-wait {
  cursor: wait;
}

.cursor-text {
  cursor: text;
}

.cursor-move {
  cursor: move;
}

.cursor-not-allowed {
  cursor: not-allowed;
}
