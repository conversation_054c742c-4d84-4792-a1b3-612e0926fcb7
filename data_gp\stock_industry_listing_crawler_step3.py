"""
沪深上市公司行业信息获取脚本

作用：
1. 读取"沪深上市公司列表.csv"文件中的证券代码
2. 使用akshare API获取每个证券代码对应的行业和上市时间信息
3. 将获取到的信息添加到原CSV数据中
4. 保存为"沪深上市公司列表_带行业信息.csv"文件（证券代码在第一列）
5. 对获取失败的证券代码进行重试机制
6. 将最终仍然失败的证券代码保存到单独的文件中

特性：
- 确保证券代码以字符串形式读取，避免000001被读成1
- 单次失败重试3次机制
- 全部处理完成后对失败项再次重试3轮，每轮间隔1分钟
- 完善的错误处理和进度监控
- 定期保存防止数据丢失
- 生成失败记录文件便于后续处理
- 保存时将证券代码调整到第一列
"""

import pandas as pd
import akshare as ak
import time
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_stock_info(symbol, retry_count=0):
    """
    获取股票的行业和上市时间信息
    
    Args:
        symbol: 证券代码
        retry_count: 当前重试次数
    
    Returns:
        tuple: (industry, listing_date, success)
    """
    try:
        # 调用API获取股票信息
        stock_info = ak.stock_individual_info_em(symbol=symbol)
        
        # 将返回的DataFrame转换为字典，方便查找
        info_dict = dict(zip(stock_info['item'], stock_info['value']))
        
        # 提取行业和上市时间
        industry = info_dict.get('行业', '')
        listing_date = info_dict.get('上市时间', '')
        
        return industry, listing_date, True
    
    except Exception as e:
        logger.error(f"获取股票 {symbol} 信息失败 (第{retry_count+1}次尝试): {str(e)}")
        return '', '', False

def get_stock_info_with_retry(symbol, max_retries=3):
    """
    带重试机制的股票信息获取
    
    Args:
        symbol: 证券代码
        max_retries: 最大重试次数
    
    Returns:
        tuple: (industry, listing_date, success)
    """
    for attempt in range(max_retries):
        industry, listing_date, success = get_stock_info(symbol, attempt)
        
        if success:
            if attempt > 0:
                logger.info(f"股票 {symbol} 在第{attempt+1}次尝试后成功获取信息")
            return industry, listing_date, True
        
        if attempt < max_retries - 1:  # 不是最后一次尝试
            time.sleep(1)  # 重试前等待1秒
    
    return '', '', False

def reorder_columns(df):
    """
    重新排列DataFrame的列顺序，将证券代码放在第一列
    
    Args:
        df: 原始DataFrame
    
    Returns:
        DataFrame: 重新排列列顺序后的DataFrame
    """
    # 获取所有列名
    columns = df.columns.tolist()
    
    # 如果证券代码不在第一位，则重新排列
    if '证券代码' in columns and columns[0] != '证券代码':
        # 移除证券代码
        columns.remove('证券代码')
        # 将证券代码插入到第一位
        columns.insert(0, '证券代码')
        # 重新排列DataFrame
        df = df[columns]
    
    return df

def save_dataframe_with_reorder(df, filename):
    """
    保存DataFrame，确保证券代码在第一列
    
    Args:
        df: 要保存的DataFrame
        filename: 文件名
    """
    # 重新排列列顺序
    df_reordered = reorder_columns(df)
    
    # 保存文件
    df_reordered.to_csv(filename, index=False, encoding='utf-8-sig')
    
    return df_reordered

def process_failed_stocks(df, failed_indices, max_final_retries=3):
    """
    对失败的股票进行最终重试
    
    Args:
        df: DataFrame
        failed_indices: 失败的索引列表
        max_final_retries: 最大最终重试轮数
    
    Returns:
        list: 仍然失败的索引列表
    """
    current_failed = failed_indices.copy()
    
    for retry_round in range(max_final_retries):
        if not current_failed:
            break
            
        logger.info(f"开始第{retry_round+1}轮最终重试，共{len(current_failed)}个失败项")
        
        if retry_round > 0:
            logger.info("等待1分钟后开始重试...")
            time.sleep(60)  # 等待1分钟
        
        still_failed = []
        
        for index in current_failed:
            symbol = df.at[index, '证券代码']
            company_name = df.at[index, '证券简称']
            
            logger.info(f"最终重试: {symbol} - {company_name}")
            
            industry, listing_date, success = get_stock_info_with_retry(symbol, 3)
            
            if success:
                df.at[index, '行业'] = industry
                df.at[index, '上市时间'] = listing_date
                logger.info(f"最终重试成功: {symbol} - 行业={industry}, 上市时间={listing_date}")
            else:
                still_failed.append(index)
                logger.warning(f"最终重试仍然失败: {symbol}")
            
            time.sleep(0.5)  # 请求间隔
        
        current_failed = still_failed
        
        # 保存中间结果（证券代码在第一列）
        save_dataframe_with_reorder(df, '沪深上市公司列表_带行业信息_临时.csv')
        logger.info(f"第{retry_round+1}轮重试完成，剩余失败项: {len(current_failed)}")
    
    return current_failed

def save_failed_records(df, failed_indices, filename):
    """
    保存失败的记录到文件
    
    Args:
        df: DataFrame
        failed_indices: 失败的索引列表
        filename: 保存的文件名
    """
    if failed_indices:
        failed_df = df.iloc[failed_indices][['证券代码', '板块', '证券简称']].copy()
        failed_df['失败原因'] = 'API获取失败'
        failed_df['失败时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 确保证券代码在第一列
        failed_df = reorder_columns(failed_df)
        
        failed_df.to_csv(filename, index=False, encoding='utf-8-sig')
        logger.info(f"已将{len(failed_indices)}条失败记录保存到 '{filename}'")

def main():
    # 读取CSV文件，确保证券代码作为字符串读取
    try:
        df = pd.read_csv('沪深上市公司列表.csv', dtype={'证券代码': str})
        logger.info(f"成功读取CSV文件，共 {len(df)} 条记录")
        
        # 显示原始列顺序
        logger.info(f"原始列顺序: {list(df.columns)}")
        
    except FileNotFoundError:
        logger.error("找不到文件 '沪深上市公司列表.csv'")
        return
    except Exception as e:
        logger.error(f"读取CSV文件失败: {str(e)}")
        return
    
    # 添加新列
    df['行业'] = ''
    df['上市时间'] = ''
    
    # 记录失败的索引
    failed_indices = []
    
    # 遍历每一行，获取股票信息
    total_count = len(df)
    success_count = 0
    
    logger.info("开始第一轮处理...")
    
    for index, row in df.iterrows():
        symbol = row['证券代码']
        company_name = row['证券简称']
        
        logger.info(f"正在处理 {index+1}/{total_count}: {symbol} - {company_name}")
        
        # 获取股票信息（带重试）
        industry, listing_date, success = get_stock_info_with_retry(symbol, 3)
        
        if success:
            # 更新DataFrame
            df.at[index, '行业'] = industry
            df.at[index, '上市时间'] = listing_date
            success_count += 1
            logger.info(f"成功获取信息: 行业={industry}, 上市时间={listing_date}")
        else:
            failed_indices.append(index)
            logger.warning(f"3次重试后仍然失败: {symbol}")
        
        # 添加延时，避免请求过于频繁
        time.sleep(0.5)
        
        # 每处理10条记录保存一次（防止程序中断导致数据丢失）
        if (index + 1) % 10 == 0:
            save_dataframe_with_reorder(df, '沪深上市公司列表_带行业信息_临时.csv')
            logger.info(f"已处理 {index+1} 条记录，临时保存完成")
    
    logger.info(f"第一轮处理完成！成功: {success_count}, 失败: {len(failed_indices)}")
    
    # 对失败的项目进行最终重试
    if failed_indices:
        logger.info(f"开始对{len(failed_indices)}个失败项进行最终重试...")
        final_failed_indices = process_failed_stocks(df, failed_indices, 3)
        
        # 更新成功计数
        success_count = total_count - len(final_failed_indices)
        
        # 保存最终失败的记录
        if final_failed_indices:
            save_failed_records(df, final_failed_indices, '获取失败的证券代码列表.csv')
        
        logger.info(f"最终重试完成！最终成功: {success_count}, 最终失败: {len(final_failed_indices)}")
    
    # 保存最终结果（证券代码在第一列）
    try:
        final_df = save_dataframe_with_reorder(df, '沪深上市公司列表_带行业信息.csv')
        
        # 显示最终列顺序
        logger.info(f"最终列顺序: {list(final_df.columns)}")
        
        logger.info(f"处理完成！")
        logger.info(f"总记录数: {total_count}")
        logger.info(f"成功获取: {success_count}")
        logger.info(f"最终失败: {len(failed_indices) if 'final_failed_indices' not in locals() else len(final_failed_indices)}")
        logger.info("结果已保存到 '沪深上市公司列表_带行业信息.csv'（证券代码在第一列）")
        
        # 删除临时文件
        import os
        if os.path.exists('沪深上市公司列表_带行业信息_临时.csv'):
            os.remove('沪深上市公司列表_带行业信息_临时.csv')
            logger.info("临时文件已清理")
            
    except Exception as e:
        logger.error(f"保存文件失败: {str(e)}")

if __name__ == "__main__":
    start_time = datetime.now()
    logger.info("=" * 50)
    logger.info("沪深上市公司行业信息获取脚本开始运行")
    logger.info(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 50)
    
    main()
    
    end_time = datetime.now()
    duration = end_time - start_time
    logger.info("=" * 50)
    logger.info("脚本运行完成")
    logger.info(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"总耗时: {duration}")
    logger.info("=" * 50)
