const axios = require('axios');
const fs = require('fs');
const path = require('path');

/**
 * 测试语音解析筛选条件API的测试脚本
 */

// 配置
const baseURL = 'http://localhost:3000';
const apiPrefix = '/api/v1/filter';

/**
 * 将音频文件转换为Base64格式
 * @param {string} audioFilePath 音频文件路径
 * @returns {string} Base64编码的音频数据
 */
function audioFileToBase64(audioFilePath) {
  try {
    // 读取音频文件
    const audioBuffer = fs.readFileSync(audioFilePath);
    
    // 获取文件扩展名来确定音频格式
    const fileExtension = path.extname(audioFilePath).toLowerCase().replace('.', '');
    
    // 转换为Base64格式
    const base64Audio = `data:audio/${fileExtension};base64,${audioBuffer.toString('base64')}`;
    
    console.log(`✅ 音频文件读取成功: ${audioFilePath}`);
    console.log(`📁 文件大小: ${Math.round(audioBuffer.length / 1024)} KB`);
    console.log(`🎵 音频格式: ${fileExtension}`);
    
    return { base64Audio, format: fileExtension };
  } catch (error) {
    console.error(`❌ 读取音频文件失败: ${error.message}`);
    throw error;
  }
}

/**
 * 测试语音解析筛选条件API
 */
async function testAudioFilterCondition() {
  console.log('🧪 开始测试语音解析筛选条件API');
  console.log('📍 服务器地址:', baseURL);
  console.log('📅 测试时间:', new Date().toLocaleString('zh-CN'));
  console.log('='.repeat(60));

  // 音频文件路径（请将你的音频文件放在这个路径）
  const audioFilePath = path.join(__dirname, 'test-audio.mp3');
  
  // 检查音频文件是否存在
  if (!fs.existsSync(audioFilePath)) {
    console.log('❌ 音频文件不存在:', audioFilePath);
    console.log('💡 请在以下路径放置一个音频文件：');
    console.log('   - 文件名: test-audio.wav (或 test-audio.mp3)');
    console.log('   - 路径:', audioFilePath);
    console.log('   - 建议录制一段关于股票筛选条件的语音，例如："帮我找市盈率小于20的银行股"');
    return;
  }

  try {
    // 第一步：读取并转换音频文件
    console.log('\n📤 第一步：读取音频文件');
    console.log('-'.repeat(30));
    const { base64Audio, format } = audioFileToBase64(audioFilePath);
    // console.log('✅ 音频文件读取成功',base64Audio,format);
    
    // 第二步：发送API请求
    console.log('\n📤 第二步：发送API请求');
    console.log('-'.repeat(30));
    
    const url = `${baseURL}${apiPrefix}/parse-audio-condition`;
    console.log('🌐 请求URL:', url);
    
    const requestData = {
      audioData: base64Audio,
      format: format
    };
    
    console.log('📦 请求数据:');
    console.log(`   - 音频格式: ${format}`);
    console.log(`   - Base64数据长度: ${base64Audio.length} 字符`);
    
    const startTime = Date.now();
    
    const response = await axios.post(url, requestData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 60000 // 60秒超时
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 第三步：处理响应结果
    console.log('\n📥 第三步：处理响应结果');
    console.log('-'.repeat(30));
    
    console.log('📊 响应状态:', response.status);
    console.log('⏱️  响应时间:', `${duration}ms`);
    
    if (response.status === 200) {
      const { success, message, data } = response.data;
      
      console.log('✅ 请求成功:', message);
      console.log('\n🎤 语音识别结果:');
      console.log(`"${data.recognizedText}"`);
      
      console.log('\n🔍 解析的筛选条件:');
      console.log('📋 当日数据条件:', JSON.stringify(data.filterConditions.dailyConditions, null, 2));
      console.log('📊 财务数据条件:', JSON.stringify(data.filterConditions.financialConditions, null, 2));
      console.log('ℹ️  基本信息条件:', JSON.stringify(data.filterConditions.basicInfoConditions, null, 2));
      
      console.log(`\n⏱️  总处理时间: ${data.processingTime}ms`);
      
      console.log('\n🎉 语音解析筛选条件测试成功！');
    } else {
      console.log('⚠️  状态码不符合预期:', response.status);
    }
    
  } catch (error) {
    console.log('\n❌ 测试失败');
    console.log('-'.repeat(30));
    
    if (error.response) {
      console.log('📥 响应状态:', error.response.status);
      console.log('📥 错误信息:', error.response.data?.message || error.response.data?.error || 'Unknown error');
      
      if (error.response.data?.errors) {
        console.log('📋 详细错误:', error.response.data.errors);
      }
    } else if (error.request) {
      console.log('🌐 网络错误: 无法连接到服务器');
      console.log('💡 请确保后端服务在', baseURL, '上运行');
    } else {
      console.log('💥 请求错误:', error.message);
    }
  }
}

/**
 * 创建示例音频文件说明
 */
function createAudioFileInstructions() {
  const instructionsPath = path.join(__dirname, 'audio-test-instructions.txt');
  const instructions = `
语音解析筛选条件API测试说明
=====================================

1. 准备音频文件：
   - 文件名: test-audio.wav (推荐) 或 test-audio.mp3
   - 文件路径: ${path.join(__dirname, 'test-audio.wav')}
   - 音频内容建议: 录制一段关于股票筛选条件的中文语音

2. 录音内容示例：
   - "帮我找市盈率小于20的银行股"
   - "筛选出总市值大于100亿的沪市主板股票"
   - "找出市净率在1到3之间的券商股"
   - "选择ROE大于10%的制造业股票"

3. 音频要求：
   - 格式: WAV, MP3, PCM, WEBM, OGG
   - 大小: 小于10MB
   - 语言: 中文普通话
   - 时长: 建议5-30秒

4. 运行测试：
   node testAudioFilterCondition.js

注意: 请确保后端服务器在 http://localhost:6000 上运行
`;

  fs.writeFileSync(instructionsPath, instructions);
  console.log('📋 已创建音频测试说明文件:', instructionsPath);
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  // 检查是否有音频文件，如果没有则创建说明文件
  const audioFilePath = path.join(__dirname, 'test-audio.wav');
  const audioFilePath2 = path.join(__dirname, 'test-audio.mp3');
  
  if (!fs.existsSync(audioFilePath) && !fs.existsSync(audioFilePath2)) {
    createAudioFileInstructions();
  }
  
  testAudioFilterCondition().catch(error => {
    console.error('💥 测试运行失败:', error.message);
  });
}

module.exports = { testAudioFilterCondition, audioFileToBase64 };
