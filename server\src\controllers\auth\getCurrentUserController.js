const { success, error } = require('../../utils/response');
const getCurrentUserService = require('../../services/auth/getCurrentUserService');

/**
 * 获取当前用户信息
 * @route GET /api/v1/auth/me
 * @access Private
 */
const getCurrentUser = async (req, res, next) => {
  try {
    // 获取当前用户信息
    const user = await getCurrentUserService.getCurrentUser(req.user.id);

    // 返回成功响应
    return success(res, 200, null, user);
  } catch (err) {
    next(err);
  }
}; 

module.exports = { getCurrentUser };