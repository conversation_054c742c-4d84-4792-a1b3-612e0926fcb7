import pandas as pd
from datetime import datetime

def filter_stock_data(input_file, output_file=None, cutoff_date='2025-09-04'):
    """
    过滤股票数据，只保留指定日期及之前的数据
    
    参数:
    input_file: 输入的CSV文件路径
    output_file: 输出的CSV文件路径（可选，默认为原文件名_filtered.csv）
    cutoff_date: 截止日期，格式为'YYYY-MM-DD'
    """
    
    try:
        # 读取CSV文件，确保证券代码列为字符串格式
        print(f"正在读取文件: {input_file}")
        df = pd.read_csv(input_file, dtype={'证券代码': str})
        
        # 显示原始数据信息
        print(f"原始数据行数: {len(df)}")
        print(f"包含的证券代码: {df['证券代码'].unique()}")
        
        # 将日期列转换为datetime格式
        df['日期'] = pd.to_datetime(df['日期'])
        cutoff_datetime = pd.to_datetime(cutoff_date)
        
        # 过滤数据：只保留截止日期及之前的数据
        filtered_df = df[df['日期'] <= cutoff_datetime].copy()
        
        # 显示过滤后的数据信息
        print(f"过滤后数据行数: {len(filtered_df)}")
        print(f"数据日期范围: {filtered_df['日期'].min()} 到 {filtered_df['日期'].max()}")
        
        # 将日期格式转换回字符串（保持原格式）
        filtered_df['日期'] = filtered_df['日期'].dt.strftime('%Y-%m-%d')
        
        # 确定输出文件名
        if output_file is None:
            if input_file.endswith('.csv'):
                output_file = input_file.replace('.csv', '_filtered.csv')
            else:
                output_file = input_file + '_filtered.csv'
        
        # 保存过滤后的数据
        filtered_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"过滤后的数据已保存到: {output_file}")
        
        # 显示按证券代码统计的信息
        print("\n各证券代码的数据统计:")
        for code in filtered_df['证券代码'].unique():
            code_data = filtered_df[filtered_df['证券代码'] == code]
            print(f"  {code}: {len(code_data)}条记录, 日期范围: {code_data['日期'].min()} 到 {code_data['日期'].max()}")
        
        return filtered_df
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
    except Exception as e:
        print(f"处理文件时发生错误: {str(e)}")
        return None

def main():
    # 配置参数
    input_file = "企业历史日级别价格数据.csv"  # 请修改为你的CSV文件名
    output_file = "企业历史日级别价格数据_end20250904.csv"  # 输出文件名
    cutoff_date = "2025-09-04"  # 截止日期
    
    print("=== 股票数据过滤脚本 ===")
    print(f"截止日期: {cutoff_date}")
    print("-" * 50)
    
    # 执行过滤
    result = filter_stock_data(input_file, output_file, cutoff_date)
    
    if result is not None:
        print("\n处理完成！")
        
        # 可选：显示前几行数据预览
        print("\n过滤后数据预览（前5行）:")
        print(result.head())
    else:
        print("处理失败，请检查文件路径和格式。")

if __name__ == "__main__":
    main()
