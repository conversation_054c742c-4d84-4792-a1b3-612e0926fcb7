const mongoose = require('mongoose');
const DailyStockData = require('../models/DailyStockData');

// 数据库连接配置
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/investment_ai';

// 测试数据
const testData = {
  stockCode: '600000',
  timestamp: new Date('2025-09-04 15:00:00'),
  currentPrice: 13.77,
  priceChange: -0.01,
  priceChangePercent: -0.07,
  previousClose: 13.78,
  openPrice: 13.7,
  highPrice: 13.85,
  lowPrice: 13.5,
  volume: 101214019.0,
  turnover: 1382067033.0,
  turnoverRate: 0.33,
  amplitude: 2.54,
  limitUp: 15.16,
  limitDown: 12.4,
  averagePrice: 13.655,
  week52High: 14.3644,
  week52Low: 7.9489,
  ytdGain: 37.88,
  peRatioDynamic: 7.064,
  peRatioStatic: 9.282,
  peRatioTTM: 8.751,
  pbRatio: 0.61,
  earningsPerShare: 1.57,
  bookValuePerShare: 22.57,
  dividendTTM: 0.407,
  dividendYieldTTM: 2.956,
  totalMarketCap: 420097247174.0,
  circulatingMarketCap: 420097247174.0,
  volumeRatio: 0.71,
  priceVelocity: 0.0,
  fiveMinuteChange: 0.15,
  sixtyDayChange: 15.16
};

async function testDailyStockDataModel() {
  console.log('🧪 开始测试 DailyStockData 模型...');
  
  try {
    // 连接数据库
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ 数据库连接成功');

    // 测试字段映射
    console.log('\n📋 测试字段映射:');
    const fieldMapping = DailyStockData.getFieldMapping();
    console.log('字段映射数量:', Object.keys(fieldMapping).length);
    console.log('示例映射:', {
      '证券代码': fieldMapping['证券代码'],
      '现价': fieldMapping['现价'],
      '成交额': fieldMapping['成交额']
    });

    // 测试创建数据
    console.log('\n💾 测试创建数据:');
    const stockData = new DailyStockData(testData);
    const savedData = await stockData.save();
    console.log('✅ 数据创建成功:', savedData.toDisplayString());

    // 测试查询方法
    console.log('\n🔍 测试查询方法:');
    
    // 按股票代码查询
    const byCode = await DailyStockData.findByStockCode('600000');
    console.log(`按代码查询结果: ${byCode.length} 条`);

    // 查询最新数据
    const latest = await DailyStockData.findLatestByStockCode('600000');
    console.log('最新数据:', latest ? latest.toDisplayString() : '无数据');

    // 测试实例方法
    console.log('\n📊 测试实例方法:');
    if (latest) {
      console.log('价格变动:', latest.getFormattedPriceChange());
      console.log('成交额:', latest.getFormattedTurnover());
      console.log('市值:', latest.getFormattedMarketCap());
      console.log('技术指标摘要:', latest.getTechnicalSummary());
      console.log('趋势状态:', latest.getTrendStatus());
      console.log('是否异常数据:', latest.isAnomalousData());
    }

    // 测试更新数据（相同时间点）
    console.log('\n🔄 测试数据更新:');
    const updateData = { ...testData };
    updateData.currentPrice = 13.80; // 修改价格
    updateData.priceChange = 0.02;
    updateData.priceChangePercent = 0.15;
    
    const updatedStock = await DailyStockData.findOneAndUpdate(
      { stockCode: updateData.stockCode, timestamp: updateData.timestamp },
      { $set: updateData },
      { new: true, upsert: true }
    );
    console.log('✅ 数据更新成功:', updatedStock.getFormattedPriceChange());

    // 测试批量查询
    console.log('\n📦 测试批量查询:');
    const multipleLatest = await DailyStockData.findLatestByStockCodes(['600000', '600004']);
    console.log(`批量查询结果: ${multipleLatest.length} 条`);

    // 清理测试数据
    console.log('\n🧹 清理测试数据:');
    const deleteResult = await DailyStockData.deleteMany({ stockCode: '600000' });
    console.log(`✅ 已删除 ${deleteResult.deletedCount} 条测试数据`);

    console.log('\n🎉 所有测试通过!');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error(error.stack);
  } finally {
    // 关闭数据库连接
    await mongoose.disconnect();
    console.log('✅ 数据库连接已关闭');
  }
}

// 当直接运行此脚本时执行测试
if (require.main === module) {
  testDailyStockDataModel().catch(error => {
    console.error('💥 测试过程发生严重错误:', error);
    process.exit(1);
  });
}

module.exports = { testDailyStockDataModel };
