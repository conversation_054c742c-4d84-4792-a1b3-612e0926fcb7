# 股票基本信息管理

## 概述

本模块用于管理股票基本信息，包括证券代码、证券简称、板块、行业和上市时间等信息。

## 数据模型

### StockBasicInfo模型 (`server/src/models/StockBasicInfo.js`)

#### 字段说明

| 中文字段 | 英文字段 | 类型 | 说明 |
|---------|----------|------|------|
| 证券代码 | stockCode | String | 6位数字股票代码，唯一索引 |
| 证券简称 | stockName | String | 股票简称 |
| 板块 | board | String | 交易板块（如：沪市主板、深市创业板等） |
| 行业 | industry | String | 所属行业 |
| 上市时间 | listingDate | Date | 股票上市日期 |

#### 板块映射

| 中文板块 | 英文代码 |
|----------|----------|
| 沪市主板 | SH_MAIN |
| 沪市科创板 | SH_STAR |
| 深市主板 | SZ_MAIN |
| 深市创业板 | SZ_GEM |
| 深市中小板 | SZ_SME |
| 北交所 | BJ_MAIN |

#### 行业映射

模型中包含了完整的行业中英文映射，如：
- 银行 → BANKING
- 航空机场 → AVIATION_AIRPORT
- 汽车整车 → AUTO_MANUFACTURING
- 等等...

## 数据导入

### 导入脚本 (`server/scripts/import-stock-basic-info.js`)

#### 功能特性

1. **BOM检测与移除**：自动检测并移除CSV文件中的UTF-8 BOM
2. **股票代码格式化**：确保股票代码为6位数字（自动补充前导零）
3. **数据验证**：验证所有必要字段的完整性
4. **批量处理**：支持大文件的分批导入
5. **错误处理**：详细的错误报告和数据验证

#### 使用方法

1. **安装依赖**：
   ```bash
   cd server
   npm install
   ```

2. **运行导入脚本**：
   ```bash
   npm run import-stocks
   ```

3. **或者直接运行**：
   ```bash
   node scripts/import-stock-basic-info.js
   ```

#### 配置

- **数据库连接**：通过环境变量 `MONGODB_URI` 配置，默认为 `mongodb://localhost:27017/investment-ai`
- **CSV文件路径**：默认为 `data_gp/沪深上市公司列表_带行业信息.csv`

#### 导入过程

1. 连接MongoDB数据库
2. 读取CSV文件并移除BOM
3. 逐行解析和验证数据
4. 清空现有数据（可选）
5. 批量插入新数据
6. 显示统计信息

## API方法

### 静态方法

#### 字段映射方法
- `getFieldMapping()` - 获取中英文字段映射
- `getBoardMapping()` - 获取板块映射
- `getIndustryMapping()` - 获取行业映射
- `getChineseFieldName(englishField)` - 获取字段中文名
- `getEnglishFieldName(chineseField)` - 获取字段英文名

#### 查询方法
- `findByStockCode(stockCode)` - 根据股票代码查找
- `findByStockName(stockName)` - 根据股票名称查找
- `findByBoard(board, options)` - 根据板块查找
- `findByIndustry(industry, options)` - 根据行业查找
- `findByListingDateRange(startDate, endDate, options)` - 根据上市时间范围查找
- `searchStocks(keyword, options)` - 搜索股票（支持代码和名称模糊搜索）

#### 统计方法
- `getBoardStatistics()` - 获取板块统计
- `getIndustryStatistics()` - 获取行业统计

#### 数据操作方法
- `bulkUpsert(stocksData)` - 批量插入或更新

## 使用示例

### 基本查询

```javascript
const StockBasicInfo = require('./src/models/StockBasicInfo');

// 查找特定股票
const stock = await StockBasicInfo.findByStockCode('600000');

// 查找银行板块的股票
const bankStocks = await StockBasicInfo.findByIndustry('银行');

// 搜索包含"银行"的股票
const searchResults = await StockBasicInfo.searchStocks('银行', { limit: 10 });

// 获取板块统计
const boardStats = await StockBasicInfo.getBoardStatistics();
```

### 字段映射使用

```javascript
// 获取字段映射
const fieldMapping = StockBasicInfo.getFieldMapping();
console.log(fieldMapping);
// 输出: { '证券代码': 'stockCode', '证券简称': 'stockName', ... }

// 获取板块英文名
const englishBoard = StockBasicInfo.getEnglishBoardName('沪市主板');
console.log(englishBoard); // 输出: SH_MAIN
```

## 注意事项

1. **股票代码格式**：系统会自动将股票代码补充为6位数字（如：'1' → '000001'）
2. **数据唯一性**：股票代码在数据库中是唯一的
3. **日期格式**：CSV中的日期应为YYYYMMDD格式（如：********）
4. **字符编码**：支持UTF-8编码的CSV文件，自动处理BOM
5. **批量操作**：大量数据导入时会自动分批处理，避免内存问题

## 错误处理

导入脚本包含完善的错误处理机制：

- **数据验证错误**：记录无效的数据行
- **格式错误**：处理股票代码和日期格式问题
- **数据库错误**：处理连接和插入错误
- **文件错误**：处理文件不存在或读取错误

所有错误都会被记录并在导入完成后显示详细报告。
