// test-parse-condition-axios.js

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const API_ENDPOINT = '/api/v1/filter/parse-condition';

// 创建 axios 实例
const apiClient = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    // 如果需要认证，在这里添加
    // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
  }
});

// 测试用例（与上面相同）
const testCases = [
  {
    name: '正常测试 - 简单条件',
    data: { userInput: '请问你是谁' },
    expectedStatus: 200
  }
];

/**
 * 发送测试请求 (axios 版本)
 */
async function sendTestRequest(testCase) {
  try {
    console.log(`\n🧪 测试: ${testCase.name}`);
    console.log(`📤 请求数据:`, JSON.stringify(testCase.data, null, 2));

    const response = await apiClient.post(API_ENDPOINT, testCase.data);
    
    console.log(`📥 响应状态: ${response.status}`);
    console.log(`📥 响应数据:`, JSON.stringify(response.data, null, 2));

    const passed = response.status === testCase.expectedStatus;
    console.log(passed ? `✅ 测试通过` : `❌ 测试失败`);

    return {
      testName: testCase.name,
      passed,
      status: response.status,
      responseData: response.data
    };

  } catch (error) {
    const status = error.response?.status || 0;
    const responseData = error.response?.data;
    
    console.log(`📥 响应状态: ${status}`);
    if (responseData) {
      console.log(`📥 响应数据:`, JSON.stringify(responseData, null, 2));
    }
    
    const passed = status === testCase.expectedStatus;
    console.log(passed ? `✅ 测试通过` : `❌ 测试失败`);

    return {
      testName: testCase.name,
      passed,
      status,
      responseData,
      error: error.message
    };
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始测试解析筛选条件API接口...');
  console.log(`🌐 测试地址: ${BASE_URL}${API_ENDPOINT}`);
  
  const results = [];
  
  for (const testCase of testCases) {
    const result = await sendTestRequest(testCase);
    results.push(result);
    
    // 延迟
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // 输出汇总
  console.log('\n' + '='.repeat(60));
  console.log('📊 测试结果汇总:');
  const passedCount = results.filter(r => r.passed).length;
  console.log(`✅ 通过: ${passedCount}/${results.length}`);
  console.log(`❌ 失败: ${results.length - passedCount}/${results.length}`);
}

// 运行测试
runAllTests().catch(console.error);
