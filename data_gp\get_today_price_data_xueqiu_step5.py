"""
股票数据处理与合并脚本
功能：
1. 读取"沪深上市公司列表.csv"文件中的所有证券代码
2. 通过akshare API获取股票实时数据，提取关键财务指标字段
3. 保存未成功获取数据的股票代码到文件
4. 合并"当日股票数据_清洗后.csv"中的部分字段到获取的数据中
5. 生成最终的合并文件"企业当日数据_全字段_雪球为主.csv"
"""

import pandas as pd
import akshare as ak
import time
import os

def get_stock_prefix(board):
    """根据板块确定股票代码前缀"""
    if board in ["沪市主板", "科创板"]:
        return "SH"
    elif board in ["深市主板", "创业板"]:
        return "SZ"
    else:
        return "SH"  # 默认使用SH

def process_stock_data(symbol, stock_code, max_retries=3, retry_delay=1):
    """处理单只股票数据，包含重试机制"""
    for attempt in range(max_retries):
        try:
            stock_data = ak.stock_individual_spot_xq(symbol=symbol)
            data_dict = dict(zip(stock_data['item'], stock_data['value']))
            
            required_fields = [
                "证券代码", "时间", "现价", "涨跌额", "涨幅", "昨收", "今开", "最高", "最低",
                "成交量", "成交额", "换手率", "振幅", "涨停", "跌停", "均价",
                "52周最高", "52周最低", "今年以来涨幅", "市盈率(动)", "市盈率(静)", 
                "市盈率(TTM)", "市净率", "每股收益", "每股净资产", "股息(TTM)", 
                "股息率(TTM)", "总市值", "流通市值"
            ]
            
            result = {"证券代码": stock_code}  # 确保证券代码保持为字符串格式
            
            field_mapping = {
                "现价": "现价", "涨跌额": "涨跌", "涨幅": "涨幅", "昨收": "昨收", 
                "今开": "今开", "最高": "最高", "最低": "最低", "成交量": "成交量",
                "成交额": "成交额", "换手率": "周转率", "振幅": "振幅", "涨停": "涨停",
                "跌停": "跌停", "均价": "均价", "52周最高": "52周最高", "52周最低": "52周最低",
                "今年以来涨幅": "今年以来涨幅", "市盈率(动)": "市盈率(动)", "市盈率(静)": "市盈率(静)",
                "市盈率(TTM)": "市盈率(TTM)", "市净率": "市净率", "每股收益": "每股收益",
                "每股净资产": "每股净资产", "股息(TTM)": "股息(TTM)", "股息率(TTM)": "股息率(TTM)",
                "时间": "时间", "总市值": "资产净值/总市值", "流通市值": "流通值"
            }
            
            for field in required_fields[1:]:
                result[field] = data_dict.get(field_mapping[field], None)
            
            return result
            
        except Exception as e:
            print(f"处理股票 {stock_code} 第 {attempt + 1} 次尝试失败: {str(e)}")
            if attempt < max_retries - 1:  # 如果不是最后一次尝试
                print(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                print(f"股票 {stock_code} 重试 {max_retries} 次后仍然失败")
    
    return None

def retry_failed_stocks(failed_stocks_info, batch_data, output_file, batch_size):
    """对失败的股票进行最终重试"""
    print(f"\n开始对 {len(failed_stocks_info)} 只失败的股票进行最终重试...")
    print("等待60秒后开始重试...")
    time.sleep(60)
    
    final_failed_stocks = []
    retry_success_count = 0
    
    for i, (symbol, stock_code) in enumerate(failed_stocks_info):
        print(f"最终重试第 {i + 1}/{len(failed_stocks_info)} 只股票: {stock_code} ({symbol})")
        
        stock_data = process_stock_data(symbol, stock_code, max_retries=3, retry_delay=1)
        
        if stock_data:
            batch_data.append(stock_data)
            retry_success_count += 1
            print(f"重试成功: {stock_code}")
            
            # 如果批次数据达到指定大小，保存一次
            if len(batch_data) >= batch_size:
                df_batch = pd.DataFrame(batch_data)
                # 确保证券代码列保持字符串格式
                df_batch['证券代码'] = df_batch['证券代码'].astype(str)
                df_batch.to_csv(output_file, mode='a', header=False, index=False, encoding='utf-8-sig')
                print(f"追加保存了 {len(batch_data)} 条重试成功的数据")
                batch_data.clear()
        else:
            final_failed_stocks.append(stock_code)
            print(f"最终失败: {stock_code}")
        
        time.sleep(0.1)
    
    # 保存剩余的批次数据
    if batch_data:
        df_batch = pd.DataFrame(batch_data)
        # 确保证券代码列保持字符串格式
        df_batch['证券代码'] = df_batch['证券代码'].astype(str)
        df_batch.to_csv(output_file, mode='a', header=False, index=False, encoding='utf-8-sig')
        print(f"追加保存了 {len(batch_data)} 条重试成功的数据")
    
    print(f"最终重试完成: 成功 {retry_success_count} 只，失败 {len(final_failed_stocks)} 只")
    return final_failed_stocks

def main():
    # 读取股票列表时指定证券代码列为字符串类型
    try:
        stock_list = pd.read_csv("沪深上市公司列表.csv", encoding='utf-8', dtype={'证券代码': str})
    except:
        try:
            stock_list = pd.read_csv("沪深上市公司列表.csv", encoding='gbk', dtype={'证券代码': str})
        except Exception as e:
            print(f"读取CSV文件失败: {str(e)}")
            return
    
    print(f"共找到 {len(stock_list)} 只股票")
    
    batch_size = 50
    output_file = "企业当日数据.csv"
    failed_file = "获取失败股票.csv"
    
    if os.path.exists(output_file):
        os.remove(output_file)
    
    processed_count = 0
    failed_stocks_info = []  # 存储失败股票的信息 (symbol, stock_code)
    batch_data = []
    
    # 第一轮处理
    for index, row in stock_list.iterrows():
        board = row['板块']
        stock_code = str(row['证券代码']).zfill(6)  # 确保为6位字符串
        
        prefix = get_stock_prefix(board)
        symbol = f"{prefix}{stock_code}"
        
        print(f"正在处理第 {index + 1}/{len(stock_list)} 只股票: {stock_code} ({symbol})")
        
        stock_data = process_stock_data(symbol, stock_code, max_retries=3, retry_delay=1)
        
        if stock_data:
            batch_data.append(stock_data)
            processed_count += 1
        else:
            failed_stocks_info.append((symbol, stock_code))
        
        # 批量保存数据
        if len(batch_data) >= batch_size or index == len(stock_list) - 1:
            if batch_data:
                df_batch = pd.DataFrame(batch_data)
                # 确保证券代码列保持字符串格式
                df_batch['证券代码'] = df_batch['证券代码'].astype(str)
                
                if not os.path.exists(output_file):
                    df_batch.to_csv(output_file, index=False, encoding='utf-8-sig')
                    print(f"创建文件并保存了 {len(batch_data)} 条数据")
                else:
                    df_batch.to_csv(output_file, mode='a', header=False, index=False, encoding='utf-8-sig')
                    print(f"追加保存了 {len(batch_data)} 条数据")
                
                batch_data = []
        
        time.sleep(0.1)
    
    # 如果有失败的股票，进行最终重试
    if failed_stocks_info:
        final_failed_stocks = retry_failed_stocks(failed_stocks_info, batch_data, output_file, batch_size)
        processed_count += len(failed_stocks_info) - len(final_failed_stocks)
        
        # 保存最终失败的股票代码
        if final_failed_stocks:
            failed_df = pd.DataFrame({"未成功获取的股票代码": final_failed_stocks})
            # 确保失败股票代码也保持字符串格式
            failed_df['未成功获取的股票代码'] = failed_df['未成功获取的股票代码'].astype(str)
            failed_df.to_csv(failed_file, index=False, encoding='utf-8-sig')
            print(f"最终未成功获取的股票代码已保存到: {failed_file}")
    
    print(f"\n数据获取完成！")
    print(f"总共处理了 {len(stock_list)} 只股票")
    print(f"成功获取了 {processed_count} 只股票的数据")
    print(f"数据已保存到: {output_file}")

    # 合并额外数据
    try:
        # 读取两个文件时都指定证券代码列为字符串类型
        clear_data = pd.read_csv("当日股票数据_清洗后.csv", encoding='utf-8', dtype={'证券代码': str})
        existing_data = pd.read_csv(output_file, encoding='utf-8-sig', dtype={'证券代码': str})
        
        # 确保两个DataFrame的证券代码列都是字符串格式并且长度一致
        clear_data['证券代码'] = clear_data['证券代码'].astype(str).str.zfill(6)
        existing_data['证券代码'] = existing_data['证券代码'].astype(str).str.zfill(6)
        
        print(f"清洗后数据中的证券代码示例: {clear_data['证券代码'].head().tolist()}")
        print(f"现有数据中的证券代码示例: {existing_data['证券代码'].head().tolist()}")
        
        merged_data = pd.merge(existing_data, clear_data[["证券代码", "量比", "涨速", "5分钟涨跌", "60日涨跌幅"]], on="证券代码", how="left")
        
        # 确保合并后的数据中证券代码仍为字符串格式
        merged_data['证券代码'] = merged_data['证券代码'].astype(str)
        
        merged_output_file = "企业当日数据_全字段_雪球为主.csv"
        merged_data.to_csv(merged_output_file, index=False, encoding='utf-8-sig')
        print(f"合并数据已保存到: {merged_output_file}")
        print(f"合并后数据中的证券代码示例: {merged_data['证券代码'].head().tolist()}")
        
    except Exception as e:
        print(f"合并数据时出错: {str(e)}")

if __name__ == "__main__":
    main()
