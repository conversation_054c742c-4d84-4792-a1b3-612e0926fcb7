const mongoose = require('mongoose');

// 股票基本信息模型 - 用于存储上市公司基本信息
const stockBasicInfoSchema = new mongoose.Schema({
  // === 基本信息 ===
  // 证券代码
  stockCode: {
    type: String,
    required: true,
    trim: true,
    unique: true,
    index: true
  },
  
  // 证券简称
  stockName: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  
  // 板块
  board: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  
  // 行业
  industry: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  
  // 上市时间
  listingDate: {
    type: Date,
    required: true,
    index: true
  },
  
  // 创建时间
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  // 更新时间
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  // 自动添加 createdAt 和 updatedAt
  timestamps: true,
  
  // 设置集合名称
  collection: 'stock_basic_info'
});

// 复合索引
stockBasicInfoSchema.index({ board: 1, industry: 1 }); // 按板块和行业查询
stockBasicInfoSchema.index({ listingDate: -1 }); // 按上市时间排序
stockBasicInfoSchema.index({ stockName: 1 }); // 按股票名称查询

// === 字段映射 ===

// 获取中英文字段映射
stockBasicInfoSchema.statics.getFieldMapping = function() {
  return {
    '证券代码': 'stockCode',
    '证券简称': 'stockName',
    '板块': 'board',
    '行业': 'industry',
    '上市时间': 'listingDate'
  };
};

// 获取字段的中文名称
stockBasicInfoSchema.statics.getChineseFieldName = function(englishField) {
  const mapping = this.getFieldMapping();
  for (const [chinese, english] of Object.entries(mapping)) {
    if (english === englishField) {
      return chinese;
    }
  }
  return englishField;
};

// 获取字段的英文名称
stockBasicInfoSchema.statics.getEnglishFieldName = function(chineseField) {
  const mapping = this.getFieldMapping();
  return mapping[chineseField] || chineseField;
};

// === 静态方法 ===

// 根据股票代码查找
stockBasicInfoSchema.statics.findByStockCode = function(stockCode) {
  return this.findOne({ stockCode });
};

// 根据股票名称查找
stockBasicInfoSchema.statics.findByStockName = function(stockName) {
  return this.findOne({ stockName });
};

// 根据板块查找
stockBasicInfoSchema.statics.findByBoard = function(board, options = {}) {
  const query = { board };
  return this.find(query)
    .sort({ stockCode: 1 })
    .limit(options.limit || 0)
    .skip(options.skip || 0);
};

// 根据行业查找
stockBasicInfoSchema.statics.findByIndustry = function(industry, options = {}) {
  const query = { industry };
  return this.find(query)
    .sort({ stockCode: 1 })
    .limit(options.limit || 0)
    .skip(options.skip || 0);
};

// 根据上市时间范围查找
stockBasicInfoSchema.statics.findByListingDateRange = function(startDate, endDate, options = {}) {
  const query = {
    listingDate: { $gte: new Date(startDate), $lte: new Date(endDate) }
  };
  
  return this.find(query)
    .sort({ listingDate: -1 })
    .limit(options.limit || 0)
    .skip(options.skip || 0);
};

// 获取所有板块统计
stockBasicInfoSchema.statics.getBoardStatistics = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$board',
        count: { $sum: 1 },
        stocks: { $push: { stockCode: '$stockCode', stockName: '$stockName' } }
      }
    },
    { $sort: { count: -1 } }
  ]);
};

// 获取所有行业统计
stockBasicInfoSchema.statics.getIndustryStatistics = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$industry',
        count: { $sum: 1 },
        stocks: { $push: { stockCode: '$stockCode', stockName: '$stockName' } }
      }
    },
    { $sort: { count: -1 } }
  ]);
};

// 搜索股票（支持代码和名称模糊搜索）
stockBasicInfoSchema.statics.searchStocks = function(keyword, options = {}) {
  const query = {
    $or: [
      { stockCode: { $regex: keyword, $options: 'i' } },
      { stockName: { $regex: keyword, $options: 'i' } }
    ]
  };
  
  return this.find(query)
    .sort({ stockCode: 1 })
    .limit(options.limit || 50)
    .skip(options.skip || 0);
};

// 批量插入或更新
stockBasicInfoSchema.statics.bulkUpsert = function(stocksData) {
  const bulkOps = stocksData.map(stock => ({
    updateOne: {
      filter: { stockCode: stock.stockCode },
      update: { $set: stock },
      upsert: true
    }
  }));
  
  return this.bulkWrite(bulkOps);
};

module.exports = mongoose.model('StockBasicInfo', stockBasicInfoSchema);
