const axios = require('axios');

/**
 * 专门测试股息率(TTM)筛选功能
 */

// 配置
const baseURL = 'http://localhost:3000';
const apiPrefix = '/api/v1/filter';

async function testDividendYieldFilter() {
  console.log('🧪 开始测试股息率(TTM)筛选功能');
  console.log('📍 服务器地址:', baseURL);
  console.log('📅 测试时间:', new Date().toLocaleString('zh-CN'));
  console.log('='.repeat(60));

  // 测试1：基础股息率筛选
  await runTest('💎 基础股息率筛选 (2%-8%)', 'POST', '/base-filter-stocks', {
    dailyConditions: {
      dividendYieldTTMRange: { min: 2, max: 8 }
    },
    targetDate: '2025-09-12'
  });

  // 测试2：高股息率筛选
  await runTest('💰 高股息率筛选 (>5%)', 'POST', '/base-filter-stocks', {
    dailyConditions: {
      dividendYieldTTMRange: { min: 5, max: null }
    },
    targetDate: '2025-09-12'
  });

  // 测试3：股息率+估值综合筛选
  await runTest('🎯 股息率+估值综合筛选', 'POST', '/base-filter-stocks', {
    dailyConditions: {
      dividendYieldTTMRange: { min: 3, max: null },
      peRatioTTMRange: { min: 5, max: 20 },
      pbRatioRange: { min: 0.5, max: 3 }
    },
    targetDate: '2025-09-12'
  });

  // 测试4：股息率+板块筛选
  await runTest('🏢 股息率+板块筛选', 'POST', '/base-filter-stocks', {
    dailyConditions: {
      dividendYieldTTMRange: { min: 3, max: null }
    },
    basicInfoConditions: {
      board: ['沪市主板', '深市主板']
    },
    targetDate: '2025-09-12'
  });

  // 测试5：股息率+行业筛选
  await runTest('🏭 股息率+行业筛选', 'POST', '/base-filter-stocks', {
    dailyConditions: {
      dividendYieldTTMRange: { min: 2, max: null }
    },
    basicInfoConditions: {
      industry: ['银行', '电力', '石油化工']
    },
    targetDate: '2025-09-12'
  });

  console.log('\n🎉 股息率筛选测试完成！');
}

async function runTest(testName, method, endpoint, data = null) {
  console.log(`\n${testName}`);
  console.log('-'.repeat(40));
  
  try {
    const url = `${baseURL}${apiPrefix}${endpoint}`;
    
    if (data) {
      console.log('📤 发送数据:', JSON.stringify(data, null, 2));
    }
    
    let response;
    const startTime = Date.now();
    
    if (method === 'GET') {
      response = await axios.get(url);
    } else if (method === 'POST') {
      response = await axios.post(url, data);
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log('📥 响应状态:', response.status);
    console.log('⏱️  响应时间:', `${duration}ms`);
    
    // 检查响应
    if (response.status === 200) {
      console.log('✅ 测试通过');
      
      // 如果是筛选股票的响应，显示结果统计
      if (response.data.data && response.data.data.stockCodes) {
        const count = response.data.data.count || response.data.data.stockCodes.length;
        console.log(`📊 筛选结果: 找到 ${count} 只股票`);
        
        // 显示前几个股票的详细信息
        if (count > 0 && count <= 10) {
          console.log('🏷️  股票代码:', response.data.data.stockCodes.slice(0, 10).join(', '));
          
          // 如果返回结果中包含股息率信息，显示它
          if (response.data.data.stockCodes.length > 0) {
            const firstStock = response.data.data.stockCodes[0];
            if (typeof firstStock === 'object' && firstStock.dividendYieldTTM !== undefined) {
              console.log(`💎 首个股票股息率: ${firstStock.dividendYieldTTM}%`);
            }
          }
        } else if (count > 10) {
          console.log('🏷️  前10个股票代码:', response.data.data.stockCodes.slice(0, 10).join(', '));
        }
      }
    } else {
      console.log('⚠️  状态码不符合预期');
    }
    
  } catch (error) {
    if (error.response) {
      console.log('📥 响应状态:', error.response.status);
      console.log('📥 错误响应:', JSON.stringify(error.response.data, null, 2));
      console.log('❌ 测试失败');
    } else {
      console.log('❌ 网络错误:', error.message);
      console.log('💡 请确保后端服务在', baseURL, '上运行');
    }
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testDividendYieldFilter().catch(error => {
    console.error('💥 测试运行失败:', error.message);
  });
}

module.exports = { testDividendYieldFilter, runTest };
