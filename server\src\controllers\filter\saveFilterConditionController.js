const {
  saveFilterCondition,
  getUserFilterConditions,
  deleteFilterCondition,
  renameFilterCondition
} = require('../../services/filter/saveFilterConditionService');

/**
 * 保存筛选条件
 */
const saveCondition = async (req, res) => {
  try {
    const { name, filterConditions } = req.body;
    const userId = req.user.id; // 假设通过中间件获取用户ID

    const result = await saveFilterCondition(userId, name, filterConditions);

    res.status(201).json({
      success: true,
      message: '筛选条件保存成功',
      data: result
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
};

/**
 * 获取用户所有保存的筛选条件列表（包含详情）
 */
const getConditionsList = async (req, res) => {
  try {
    const userId = req.user.id; // 假设通过中间件获取用户ID

    const result = await getUserFilterConditions(userId);

    res.status(200).json({
      success: true,
      message: '获取筛选条件列表成功',
      data: result
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
};

/**
 * 删除筛选条件
 */
const deleteCondition = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id; // 假设通过中间件获取用户ID

    const result = await deleteFilterCondition(id, userId);

    res.status(200).json({
      success: true,
      message: result.message,
      data: { id: result.id, name: result.name }
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
};

/**
 * 重命名筛选条件
 */
const renameCondition = async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;
    const userId = req.user.id; // 假设通过中间件获取用户ID

    const result = await renameFilterCondition(id, userId, name);

    res.status(200).json({
      success: true,
      message: '筛选条件重命名成功',
      data: result
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
};

module.exports = {
  saveCondition,
  getConditionsList,
  deleteCondition,
  renameCondition
};