<!--基础股票筛选页面-->
<view class="filter-page">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="title-section">
      <text class="page-title">股票筛选</text>
      <text class="page-subtitle">设置筛选条件，精准选股</text>
    </view>
    <view class="header-actions">
      <view class="btn-my-conditions" bindtap="showMyConditions">
        <text class="icon">📋</text>
        <text>我的筛选条件</text>
      </view>
    </view>
  </view>

  <!-- 智能筛选区域 -->
  <view class="smart-filter-section">
    <view class="smart-filter-input-area">
      <textarea 
        class="smart-filter-input"
        placeholder="输入您的筛选需求...,会为您自动解析为筛选条件,当前仅支持已有的条件字段"
        value="{{smartFilterInput}}"
        bindinput="onSmartFilterInput"
        maxlength="500"
        auto-height
      />
      <view class="smart-filter-actions">
        <view class="input-info">
          <text class="char-count">{{smartFilterInput.length}}/500</text>
        </view>
        <view 
          class="btn-parse {{parseLoading ? 'loading' : 'active'}}"
          bindtap="parseSmartFilter"
        >
          {{parseLoading ? '解析中...' : '解析条件'}}
        </view>
      </view>
    </view>
    <view class="smart-filter-examples" wx:if="{{!smartFilterInput.trim()}}">
      <text class="examples-title">示例：</text>
      <view class="examples-list">
        <text class="example-item" bindtap="useExample" data-example="市盈率小于20且股息率大于3%的银行股">市盈率小于20且股息率大于3%的银行股</text>
      </view>
    </view>
  </view>

  <!-- 筛选条件分组选项卡 -->
  <view class="filter-tabs">
    <block wx:for="{{filterConfig.conditionGroups}}" wx:key="key">
      <view 
        class="tab-item {{activeGroup === item.key ? 'active' : ''}}"
        bindtap="switchGroup"
        data-group="{{item.key}}"
      >
        <text class="tab-label">{{item.label}}</text>
      </view>
    </block>
  </view>

  <!-- 筛选条件网格 -->
  <scroll-view class="filter-content" scroll-y="{{true}}">
    
    <!-- 当日数据筛选条件 - 按二级分类展示 -->
    <view wx:if="{{activeGroup === 'dailyConditions'}}">
      <block wx:for="{{conditionClassifications.dailyConditions}}" wx:key="categoryKey" wx:for-item="category">
        <view class="condition-category">
          <view class="category-header">
            <text class="category-title">{{category.label}}</text>
            <text class="category-desc">{{category.description}}</text>
          </view>
          <view class="condition-grid">
            <block wx:for="{{category.conditions}}" wx:key="fieldKey" wx:for-item="fieldKey">
              <view 
                class="condition-card {{isFieldSet('dailyConditions', fieldKey) ? 'selected' : ''}}"
                bindtap="openFieldModal"
                data-group="dailyConditions"
                data-field="{{fieldKey}}"
              >
                <text class="card-label">{{filterConfig.dailyConditions[fieldKey].label}}</text>
                <view class="card-status" wx:if="{{isFieldSet('dailyConditions', fieldKey)}}">
                  <text class="status-text">{{getFieldDisplayValue('dailyConditions', fieldKey)}}</text>
                </view>
              </view>
            </block>
          </view>
        </view>
      </block>
    </view>

    <!-- 财务数据筛选条件 - 按二级分类展示 -->
    <view wx:if="{{activeGroup === 'financialConditions'}}">
      <block wx:for="{{conditionClassifications.financialConditions}}" wx:key="categoryKey" wx:for-item="category">
        <view class="condition-category">
          <view class="category-header">
            <text class="category-title">{{category.label}}</text>
            <text class="category-desc">{{category.description}}</text>
          </view>
          <view class="condition-grid">
            <block wx:for="{{category.conditions}}" wx:key="fieldKey" wx:for-item="fieldKey">
              <view 
                class="condition-card {{isFieldSet('financialConditions', fieldKey) ? 'selected' : ''}}"
                bindtap="openFieldModal"
                data-group="financialConditions"
                data-field="{{fieldKey}}"
              >
                <text class="card-label">{{filterConfig.financialConditions[fieldKey].label}}</text>
                <view class="card-status" wx:if="{{isFieldSet('financialConditions', fieldKey)}}">
                  <text class="status-text">{{getFieldDisplayValue('financialConditions', fieldKey)}}</text>
                </view>
              </view>
            </block>
          </view>
        </view>
      </block>
    </view>

    <!-- 基本信息筛选条件 - 按二级分类展示 -->
    <view wx:if="{{activeGroup === 'basicInfoConditions'}}">
      <block wx:for="{{conditionClassifications.basicInfoConditions}}" wx:key="categoryKey" wx:for-item="category">
        <view class="condition-category">
          <view class="category-header">
            <text class="category-title">{{category.label}}</text>
            <text class="category-desc">{{category.description}}</text>
          </view>
          <view class="condition-grid">
            <block wx:for="{{category.conditions}}" wx:key="fieldKey" wx:for-item="fieldKey">
              <view 
                class="condition-card {{isFieldSet('basicInfoConditions', fieldKey) ? 'selected' : ''}}"
                bindtap="openFieldModal"
                data-group="basicInfoConditions"
                data-field="{{fieldKey}}"
              >
                <text class="card-label">{{filterConfig.basicInfoConditions[fieldKey].label}}</text>
                <view class="card-status" wx:if="{{isFieldSet('basicInfoConditions', fieldKey)}}">
                  <text class="status-text">{{getFieldDisplayValue('basicInfoConditions', fieldKey)}}</text>
                </view>
              </view>
            </block>
          </view>
        </view>
      </block>
    </view>

  </scroll-view>

  <!-- 底部操作区 -->
  <view class="filter-actions">
    <!-- 目标日期选择 -->
    <view class="date-picker">
      <text class="date-label">目标日期：</text>
      <picker 
        mode="date" 
        value="{{targetDate}}" 
        bindchange="onDateChange"
        class="date-picker-input {{enableDateSelection ? '' : 'disabled'}}"
        disabled="{{!enableDateSelection}}"
      >
        <text class="date-value {{enableDateSelection ? '' : 'disabled'}}">{{targetDate || '请选择日期'}}</text>
      </picker>
      <text class="date-tip" wx:if="{{!enableDateSelection}}"></text>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="btn-selected" bindtap="showSelectedConditions">
        已选指标 {{selectedCount}}
      </button>
      <button class="btn-reset" bindtap="resetConditions">重置条件</button>
      <button class="btn-query" bindtap="startQuery">查看结果</button>
    </view>
  </view>

  <!-- 字段设置弹窗 -->
  <view class="modal-overlay {{showModal ? 'show' : ''}}" bindtap="closeModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">{{currentFieldConfig.label}}</text>
        <view class="modal-close" bindtap="closeModal">✕</view>
      </view>
      
      <view class="modal-body">
        <text class="field-desc">{{currentFieldConfig.description}}</text>
        
        <!-- 范围输入 -->
        <view class="range-setting" wx:if="{{currentFieldConfig.type === 'range'}}">
          <view class="range-item">
            <text class="range-label">最小值</text>
            <input 
              class="range-input-modal"
              type="digit"
              placeholder="请输入最小值"
              value="{{tempValue.min}}"
              bindinput="onTempRangeMinInput"
              bindtap="stopPropagation"
              bindfocus="stopPropagation"
            />
            <text class="range-unit" wx:if="{{currentFieldConfig.unit}}">{{currentFieldConfig.unit}}</text>
          </view>
          <view class="range-item">
            <text class="range-label">最大值</text>
            <input 
              class="range-input-modal"
              type="digit"
              placeholder="请输入最大值"
              value="{{tempValue.max}}"
              bindinput="onTempRangeMaxInput"
              bindtap="stopPropagation"
              bindfocus="stopPropagation"
            />
            <text class="range-unit" wx:if="{{currentFieldConfig.unit}}">{{currentFieldConfig.unit}}</text>
          </view>
        </view>

        <!-- 布尔值设置 -->
        <view class="boolean-setting" wx:if="{{currentFieldConfig.type === 'boolean'}}">
          <label class="boolean-option">
            <switch 
              checked="{{tempValue}}"
              bindchange="onTempBooleanToggle"
              bindtap="stopPropagation"
            />
            <text class="boolean-label">启用此条件</text>
          </label>
        </view>

        <!-- 多选设置 -->
        <view class="multiselect-setting" wx:if="{{currentFieldConfig.type === 'multiSelect'}}">
          <text class="setting-subtitle">{{currentField === 'board' ? '按照上市公司所属市场范围划分，支持多选。' : '按照行业分类划分，支持多选。'}}</text>
          <view class="option-list">
            <!-- 统一的选项处理（字符串数组） -->
            <block wx:for="{{currentFieldConfig.options}}" wx:key="index">
              <view 
                class="option-item {{tempMultiSelectStatus[item] ? 'selected' : ''}}"
                data-value="{{item}}"
                bindtap="onTempMultiSelectToggle"
              >
                <text class="option-label">{{item}}</text>
                <text class="option-check" wx:if="{{tempMultiSelectStatus[item]}}">✓</text>
              </view>
            </block>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <button class="btn-cancel" bindtap="closeModal">取消</button>
        <button class="btn-confirm" bindtap="confirmFieldSetting">确定</button>
      </view>
    </view>
  </view>

  <!-- 已选指标弹窗 -->
  <view class="modal-overlay {{showSelectedModal ? 'show' : ''}}" bindtap="closeSelectedModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">已选指标 {{selectedCount}}</text>
        <view class="modal-close" bindtap="closeSelectedModal">✕</view>
      </view>
      
      <view class="modal-body">
        <view class="selected-list" wx:if="{{selectedCount > 0}}">
          <block wx:for="{{selectedConditionsList}}" wx:key="index">
            <view class="selected-item">
              <view class="selected-info">
                <text class="selected-group">{{item.groupLabel}}</text>
                <text class="selected-field">{{item.fieldLabel}}</text>
                <text class="selected-value">{{item.displayValue}}</text>
              </view>
              <view class="selected-actions">
                <view 
                  class="selected-edit"
                  bindtap="editSelectedCondition"
                  data-group="{{item.groupKey}}"
                  data-field="{{item.fieldKey}}"
                >
                  编辑
                </view>
                <view 
                  class="selected-remove"
                  bindtap="removeSelectedCondition"
                  data-group="{{item.groupKey}}"
                  data-field="{{item.fieldKey}}"
                >
                  删除
                </view>
              </view>
            </view>
          </block>
        </view>
        <view class="empty-selected" wx:else>
          <text>暂无已选指标</text>
        </view>
      </view>

      <view class="modal-footer">
        <button class="btn-save-condition" bindtap="saveConditions">保存条件</button>
        <button class="btn-confirm" bindtap="closeSelectedModal">确定</button>
      </view>
    </view>
  </view>

  <!-- 我的筛选条件弹窗 -->
  <view class="modal-overlay {{showMyConditionsModal ? 'show' : ''}}" bindtap="closeMyConditionsModal">
    <view class="modal-content large-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">我的筛选条件</text>
        <view class="modal-close" bindtap="closeMyConditionsModal">✕</view>
      </view>
      
      <view class="modal-body">
        <view class="my-conditions-list" wx:if="{{myConditionsList.length > 0}}">
          <block wx:for="{{myConditionsList}}" wx:key="id">
            <view class="condition-item">
              <view class="condition-info">
                <text class="condition-name">{{item.name}}</text>
                <text class="condition-time">{{item.createdAt}}</text>
                <view class="condition-summary">
                  <text class="summary-text">共{{item.conditionCount}}个筛选条件</text>
                </view>
              </view>
              <view class="condition-actions">
                <view 
                  class="action-btn import-btn" 
                  bindtap="importCondition"
                  data-condition="{{item}}"
                >
                  导入指标
                </view>
                <view 
                  class="action-btn query-btn" 
                  bindtap="queryWithCondition"
                  data-condition="{{item}}"
                >
                  查看结果
                </view>
                <view 
                  class="action-btn rename-btn" 
                  bindtap="renameCondition"
                  data-condition="{{item}}"
                >
                  重命名
                </view>
                <view 
                  class="action-btn delete-btn" 
                  bindtap="deleteCondition"
                  data-condition="{{item}}"
                >
                  删除
                </view>
              </view>
            </view>
          </block>
        </view>
        <view class="empty-conditions" wx:else>
          <text class="empty-text">暂无保存的筛选条件</text>
          <text class="empty-tip">您可以在"已选指标"中保存当前的筛选条件</text>
        </view>
      </view>

      <view class="modal-footer">
        <button class="btn-confirm" bindtap="closeMyConditionsModal">关闭</button>
      </view>
    </view>
  </view>

  <!-- 保存条件名称输入弹窗 -->
  <view class="modal-overlay {{showSaveModal ? 'show' : ''}}" bindtap="closeSaveModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">保存筛选条件</text>
        <view class="modal-close" bindtap="closeSaveModal">✕</view>
      </view>
      
      <view class="modal-body">
        <view class="input-group">
          <text class="input-label">条件名称</text>
          <input 
            class="condition-name-input"
            type="text"
            placeholder="请输入筛选条件名称"
            value="{{conditionName}}"
            bindinput="onConditionNameInput"
            maxlength="20"
          />
        </view>
      </view>

      <view class="modal-footer">
        <button class="btn-cancel" bindtap="closeSaveModal">取消</button>
        <button class="btn-confirm" bindtap="confirmSaveCondition">保存</button>
      </view>
    </view>
  </view>

  <!-- 重命名条件弹窗 -->
  <view class="modal-overlay {{showRenameModal ? 'show' : ''}}" bindtap="closeRenameModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">重命名筛选条件</text>
        <view class="modal-close" bindtap="closeRenameModal">✕</view>
      </view>
      
      <view class="modal-body">
        <view class="input-group">
          <text class="input-label">新名称</text>
          <input 
            class="condition-name-input"
            type="text"
            placeholder="请输入新的条件名称"
            value="{{newConditionName}}"
            bindinput="onNewConditionNameInput"
            maxlength="20"
          />
        </view>
      </view>

      <view class="modal-footer">
        <button class="btn-cancel" bindtap="closeRenameModal">取消</button>
        <button class="btn-confirm" bindtap="confirmRenameCondition">确定</button>
      </view>
    </view>
  </view>

</view>
