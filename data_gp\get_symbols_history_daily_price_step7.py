import pandas as pd
import akshare as ak
import time
import os
from datetime import datetime

def get_stock_history_batch(csv_file_path, output_file_path, batch_size=50, start_date="20100101"):
    """
    批量获取股票历史数据并保存到CSV文件
    
    Parameters:
    csv_file_path: 沪深上市公司列表.csv的路径
    output_file_path: 输出文件路径
    batch_size: 每批处理的股票数量
    start_date: 开始日期
    """
    
    # 读取股票列表，确保证券代码不丢失前导0
    try:
        stock_list_df = pd.read_csv(csv_file_path, dtype={'证券代码': str})
        print(f"成功读取股票列表，共{len(stock_list_df)}只股票")
    except Exception as e:
        print(f"读取股票列表文件失败: {e}")
        return
    
    # 检查是否已存在输出文件，如果存在则读取已处理的股票代码
    processed_stocks = set()
    if os.path.exists(output_file_path):
        try:
            existing_df = pd.read_csv(output_file_path, dtype={'证券代码': str})
            processed_stocks = set(existing_df['证券代码'].unique())
            print(f"发现已存在的数据文件，已处理{len(processed_stocks)}只股票")
        except Exception as e:
            print(f"读取已存在文件失败: {e}")
    
    # 过滤出未处理的股票
    remaining_stocks = stock_list_df[~stock_list_df['证券代码'].isin(processed_stocks)]
    print(f"剩余需要处理的股票数量: {len(remaining_stocks)}")
    
    # 分批处理
    total_batches = (len(remaining_stocks) + batch_size - 1) // batch_size
    
    for batch_num in range(total_batches):
        start_idx = batch_num * batch_size
        end_idx = min((batch_num + 1) * batch_size, len(remaining_stocks))
        batch_stocks = remaining_stocks.iloc[start_idx:end_idx]
        
        print(f"\n处理第 {batch_num + 1}/{total_batches} 批，股票 {start_idx + 1}-{end_idx}")
        
        batch_data = []
        
        for idx, row in batch_stocks.iterrows():
            stock_code = row['证券代码']
            print(f"  正在获取股票 {stock_code} 的历史数据...")
            
            try:
                # 调用API获取历史数据
                stock_hist_df = ak.stock_zh_a_hist(
                    symbol=stock_code, 
                    period="daily", 
                    start_date=start_date,
                    end_date='20250905', 
                    adjust="qfq"
                )
                
                if not stock_hist_df.empty:
                    # 重命名股票代码字段为证券代码
                    stock_hist_df = stock_hist_df.rename(columns={'股票代码': '证券代码'})
                    
                    # 调整列的顺序，将证券代码放在第一列，日期放在第二列
                    columns = ['证券代码', '日期'] + [col for col in stock_hist_df.columns if col not in ['证券代码', '日期']]
                    stock_hist_df = stock_hist_df[columns]
                    
                    # 确保证券代码保持字符串格式
                    stock_hist_df['证券代码'] = stock_hist_df['证券代码'].astype(str)
                    
                    batch_data.append(stock_hist_df)
                    print(f"    成功获取 {len(stock_hist_df)} 条记录")
                else:
                    print(f"    股票 {stock_code} 没有数据")
                    
            except Exception as e:
                print(f"    获取股票 {stock_code} 数据失败: {e}")
                continue
            
            # 添加延时避免API限制
            time.sleep(0.1)
        
        # 保存当前批次的数据
        if batch_data:
            batch_combined_df = pd.concat(batch_data, ignore_index=True)
            
            # 如果是第一批且文件不存在，创建新文件；否则追加到现有文件
            if batch_num == 0 and not os.path.exists(output_file_path):
                batch_combined_df.to_csv(output_file_path, index=False, encoding='utf-8-sig')
                print(f"  创建新文件并保存了 {len(batch_combined_df)} 条记录")
            else:
                batch_combined_df.to_csv(output_file_path, mode='a', header=False, index=False, encoding='utf-8-sig')
                print(f"  追加保存了 {len(batch_combined_df)} 条记录")
        else:
            print(f"  第 {batch_num + 1} 批没有获取到任何数据")
        
        print(f"第 {batch_num + 1} 批处理完成")
    
    print(f"\n所有数据处理完成！结果保存在: {output_file_path}")

def main():
    # 文件路径配置
    stock_list_file = "沪深上市公司列表.csv"
    output_file = "企业历史日级别价格数据_20250905.csv"
    
    # 检查输入文件是否存在
    if not os.path.exists(stock_list_file):
        print(f"错误: 找不到文件 {stock_list_file}")
        return
    
    print("开始批量获取股票历史数据...")
    print(f"输入文件: {stock_list_file}")
    print(f"输出文件: {output_file}")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行批量获取
    get_stock_history_batch(
        csv_file_path=stock_list_file,
        output_file_path=output_file,
        batch_size=50,  # 每批处理50只股票，可以根据需要调整
        start_date="20250905"
    )
    
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
