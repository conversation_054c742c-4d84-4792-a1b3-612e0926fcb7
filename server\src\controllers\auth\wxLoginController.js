const authvalidator = require('../../validators/authValidator');
const { success, error } = require('../../utils/response');
const wxLoginService = require('../../services/auth/wxLoginService');


/**
 * 微信小程序登录
 * @route POST /api/v1/auth/wx-login
 * @access Public
 */
const wxLogin = async (req, res, next) => {
  try {
    console.log('收到微信登录请求:', {
      timestamp: new Date().toISOString()
    });

    // 验证请求数据
    const { error: validationError, value } = authvalidator.wxLoginSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      console.log('微信登录参数验证失败:', errors);
      return error(res, 400, '请求参数错误', errors);
    }

    // 处理微信登录
    const result = await wxLoginService.wxLogin(value.code);

    console.log('微信登录成功:', {
      userId: result.user.id,
      isNewUser: result.isNewUser,
      timestamp: new Date().toISOString()
    });

    // 返回成功响应
    const message = result.isNewUser ? '注册成功' : '登录成功';
    return success(res, 200, message, result);
  } catch (err) {
    console.error('微信登录控制器错误:', {
      error: err,
      timestamp: new Date().toISOString()
    });

    // 如果是业务逻辑错误，直接返回
    if (err.statusCode) {
      return error(res, err.statusCode, err.message, err.details ? [err.details] : undefined);
    }

    // 传递给全局错误处理中间件
    next(err);
  }
};

module.exports = { wxLogin };