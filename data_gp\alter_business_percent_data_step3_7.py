"""
企业主营业务构成数据处理脚本

功能说明：
1. 遍历"企业主营业务构成"文件夹中的所有CSV文件
2. 对数据进行以下处理：
   - 将分类类型为空的记录改为"按行业分类"
   - 主营收入、主营成本、主营利润除以1亿并保留两位小数
   - 收入比例、成本比例、利润比例、毛利率乘以100并保留两位小数
   - 重命名列名并添加单位标识
   - 删除"原始证券代码"列
3. 将处理后的文件保存到"企业主营业务构成_数据处理后"文件夹
4. 确保股票代码格式正确（如000001不会被读成1）

作者：Assistant
创建日期：2024
"""

import pandas as pd
import os
import glob

def process_business_composition_data():
    # 定义输入和输出文件夹路径
    input_folder = "企业主营业务构成"
    output_folder = "企业主营业务构成_数据处理后"
    
    # 创建输出文件夹（如果不存在）
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"已创建输出文件夹: {output_folder}")
    
    # 获取所有CSV文件路径
    csv_files = glob.glob(os.path.join(input_folder, "*.csv"))
    
    if not csv_files:
        print(f"在文件夹 '{input_folder}' 中未找到CSV文件")
        return
    
    print(f"找到 {len(csv_files)} 个CSV文件，开始处理...")
    
    # 处理每个CSV文件
    for i, file_path in enumerate(csv_files, 1):
        try:
            # 读取CSV文件，确保股票代码作为字符串读取
            df = pd.read_csv(file_path, dtype={'股票代码': str})
            
            print(f"正在处理第 {i}/{len(csv_files)} 个文件: {os.path.basename(file_path)}")
            
            # 1. 将分类类型为空的记录改为"按行业分类"
            df['分类类型'] = df['分类类型'].fillna('按行业分类')
            df.loc[df['分类类型'] == '', '分类类型'] = '按行业分类'
            
            # 2. 处理金额字段：除以1亿并保留两位小数
            money_columns = ['主营收入', '主营成本', '主营利润']
            for col in money_columns:
                if col in df.columns:
                    # 将字符串转换为数值，处理可能的空值
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                    df[col] = (df[col] / 100000000).round(2)
            
            # 3. 处理比例字段：乘以100并保留两位小数
            ratio_columns = ['收入比例', '成本比例', '利润比例', '毛利率']
            for col in ratio_columns:
                if col in df.columns:
                    # 将字符串转换为数值，处理可能的空值
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                    df[col] = (df[col] * 100).round(2)
            
            # 4. 重命名列
            column_rename_map = {
                '股票代码': '证券代码',
                '主营收入': '主营收入(亿元)',
                '主营成本': '主营成本(亿元)',
                '主营利润': '主营利润(亿元)',
                '收入比例': '收入比例(%)',
                '成本比例': '成本比例(%)',
                '利润比例': '利润比例(%)',
                '毛利率': '毛利率(%)'
            }
            
            df = df.rename(columns=column_rename_map)
            
            # 5. 删除"原始证券代码"列
            if '原始证券代码' in df.columns:
                df = df.drop(columns=['原始证券代码'])
            
            # 6. 保存处理后的文件
            output_file_path = os.path.join(output_folder, os.path.basename(file_path))
            df.to_csv(output_file_path, index=False, encoding='utf-8-sig')
            
            print(f"文件处理完成: {os.path.basename(file_path)} -> {os.path.basename(output_file_path)}")
            
        except Exception as e:
            print(f"处理文件 {os.path.basename(file_path)} 时出错: {str(e)}")
            continue
    
    print(f"\n所有文件处理完成！处理后的文件已保存到: {output_folder}")

if __name__ == "__main__":
    # 执行数据处理
    process_business_composition_data()
