/**
 * HTTP API服务器
 * 该文件负责启动Express API服务器
 */

const http = require('http');
const app = require('./app');

// 获取HTTP API端口，默认为5000
const PORT = process.env.PORT || 6000;

// 创建HTTP服务器
const server = http.createServer(app);

// 启动HTTP API服务器
server.listen(PORT, '0.0.0.0', () => {
  console.log('-----------------------------------------------');
  console.log(`✅ HTTP API服务器成功启动! 端口: ${PORT}`);
  console.log(`🌐 API地址: http://localhost:${PORT}/api/v1`);
  console.log('-----------------------------------------------');
});

// 导出HTTP服务器实例
module.exports = server; 