const { success, error } = require('../../utils/response');
const getSupportedConditionsService = require('../../services/filter/getSupportedConditionsService');


/**
 * 获取支持的筛选条件列表
 * @route GET /api/v1/filter/conditions
 * @access Private
 */
const getSupportedConditions = async (req, res, next) => {
  try {
    const conditions = getSupportedConditionsService.getSupportedConditions();
    
    return success(res, 200, '获取支持的筛选条件成功', conditions);
    
  } catch (err) {
    next(err);
  }
};

module.exports = { getSupportedConditions };
