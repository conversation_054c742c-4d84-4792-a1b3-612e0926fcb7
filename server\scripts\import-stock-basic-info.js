const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const mongoose = require('mongoose');
const StockBasicInfo = require('../src/models/StockBasicInfo');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// 数据库连接配置
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/investment-ai';

// CSV文件路径
const CSV_FILE_PATH = path.join(__dirname, '../../data_gp/沪深上市公司列表_带行业信息.csv');

// 检测并移除BOM
function removeBOM(buffer) {
  // UTF-8 BOM: EF BB BF
  if (buffer.length >= 3 && 
      buffer[0] === 0xEF && 
      buffer[1] === 0xBB && 
      buffer[2] === 0xBF) {
    console.log('检测到BOM，正在移除...');
    return buffer.slice(3);
  }
  return buffer;
}

// 解析日期字符串 (YYYYMMDD格式)
function parseDate(dateString) {
  if (!dateString || dateString.length !== 8) {
    throw new Error(`无效的日期格式: ${dateString}`);
  }
  
  const year = dateString.substring(0, 4);
  const month = dateString.substring(4, 6);
  const day = dateString.substring(6, 8);
  
  return new Date(`${year}-${month}-${day}`);
}

// 验证股票代码格式
function validateStockCode(stockCode) {
  // 确保股票代码是6位数字字符串
  if (typeof stockCode !== 'string' || !/^\d{6}$/.test(stockCode)) {
    throw new Error(`无效的股票代码格式: ${stockCode}`);
  }
  return stockCode;
}

// 清理和验证数据
function cleanData(row) {
  try {
    // 移除所有字段的前后空格
    const cleanedRow = {};
    Object.keys(row).forEach(key => {
      cleanedRow[key.trim()] = typeof row[key] === 'string' ? row[key].trim() : row[key];
    });
    
    // 验证必要字段
    if (!cleanedRow['证券代码'] || !cleanedRow['证券简称'] || 
        !cleanedRow['板块'] || !cleanedRow['行业'] || !cleanedRow['上市时间']) {
      throw new Error('缺少必要字段');
    }
    
    // 确保股票代码格式正确（补充前导零）
    let stockCode = cleanedRow['证券代码'].toString();
    stockCode = stockCode.padStart(6, '0'); // 补充前导零到6位
    
    return {
      stockCode: validateStockCode(stockCode),
      stockName: cleanedRow['证券简称'],
      board: cleanedRow['板块'],        // 直接使用中文，不映射
      industry: cleanedRow['行业'],     // 直接使用中文，不映射
      listingDate: parseDate(cleanedRow['上市时间']),
      // companyProfile: cleanedRow['公司简介'] || null,
      // mainBusiness: cleanedRow['主营业务'] || null
    };
  } catch (error) {
    console.error('数据清理错误:', error.message, '原始数据:', row);
    return null;
  }
}

// 解析CSV文件
async function parseCSVFile() {
  return new Promise((resolve, reject) => {
    console.log('开始解析CSV文件...');
    
    const stocksData = [];
    const errors = [];
    let totalRows = 0;
    let successRows = 0;
    
    // 读取文件并移除BOM
    const fileBuffer = fs.readFileSync(CSV_FILE_PATH);
    const cleanBuffer = removeBOM(fileBuffer);
    const csvContent = cleanBuffer.toString('utf8');
    
    const stream = require('stream');
    const readable = new stream.Readable();
    readable.push(csvContent);
    readable.push(null);
    
    readable
      .pipe(csv())
      .on('data', (row) => {
        totalRows++;
        const cleanedData = cleanData(row);
        
        if (cleanedData) {
          stocksData.push(cleanedData);
          successRows++;
          
          // 每1000条数据打印一次进度
          if (successRows % 1000 === 0) {
            console.log(`已处理 ${successRows} 条数据...`);
          }
        } else {
          errors.push({
            row: totalRows,
            data: row,
            error: '数据清理失败'
          });
        }
      })
      .on('end', () => {
        console.log(`\n数据解析完成:`);
        console.log(`- 总行数: ${totalRows}`);
        console.log(`- 成功解析: ${successRows}`);
        console.log(`- 解析失败: ${errors.length}`);
        
        if (errors.length > 0) {
          console.log('\n解析失败的数据:');
          errors.slice(0, 10).forEach(error => {
            console.log(`第${error.row}行:`, error.error, error.data);
          });
          if (errors.length > 10) {
            console.log(`...还有${errors.length - 10}条错误数据`);
          }
        }
        
        resolve({
          stocksData,
          totalRows,
          successRows,
          errors: errors.length
        });
      })
      .on('error', (error) => {
        reject(error);
      });
  });
}

// 导入股票基本信息
async function importStockBasicInfo() {
  let connection = null;
  
  try {
    console.log('开始导入股票基本信息...');
    console.log(`CSV文件路径: ${CSV_FILE_PATH}`);
    
    // 检查文件是否存在
    if (!fs.existsSync(CSV_FILE_PATH)) {
      throw new Error(`CSV文件不存在: ${CSV_FILE_PATH}`);
    }
    
    // 连接数据库
    console.log('连接数据库...');
    connection = await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('数据库连接成功');
    
    // 解析CSV文件
    const parseResult = await parseCSVFile();
    const { stocksData, totalRows, successRows } = parseResult;
    
    if (stocksData.length === 0) {
      throw new Error('没有有效的数据可以导入');
    }
    
    // 确保数据库连接正常
    if (mongoose.connection.readyState !== 1) {
      throw new Error('数据库连接异常');
    }
    
    // 清空现有数据（可选）
    console.log('\n清空现有股票基本信息数据...');
    await StockBasicInfo.deleteMany({});
    console.log('现有数据已清空');
    
    // 批量插入数据
    console.log(`\n开始批量插入 ${stocksData.length} 条股票基本信息...`);
    
    // 分批插入，避免内存问题
    const batchSize = 1000;
    let insertedCount = 0;
    
    for (let i = 0; i < stocksData.length; i += batchSize) {
      const batch = stocksData.slice(i, i + batchSize);
      try {
        const result = await StockBasicInfo.bulkUpsert(batch);
        insertedCount += result.upsertedCount + result.modifiedCount;
        console.log(`已插入 ${insertedCount} / ${stocksData.length} 条数据`);
      } catch (batchError) {
        console.error(`批次 ${Math.floor(i/batchSize) + 1} 插入失败:`, batchError.message);
        // 尝试逐条插入这个批次
        for (const stock of batch) {
          try {
            await StockBasicInfo.findOneAndUpdate(
              { stockCode: stock.stockCode },
              stock,
              { upsert: true, new: true }
            );
            insertedCount++;
          } catch (singleError) {
            console.error(`股票 ${stock.stockCode} 插入失败:`, singleError.message);
          }
        }
      }
    }
    
    console.log(`\n数据导入完成！`);
    console.log(`- 成功导入: ${insertedCount} 条`);
    
    // 验证导入结果
    const totalCount = await StockBasicInfo.countDocuments();
    console.log(`- 数据库中总数: ${totalCount} 条`);
    
    // 显示一些统计信息
    console.log('\n板块统计:');
    const boardStats = await StockBasicInfo.getBoardStatistics();
    boardStats.forEach(stat => {
      console.log(`  ${stat._id}: ${stat.count} 只股票`);
    });
    
    console.log('\n行业统计（前10）:');
    const industryStats = await StockBasicInfo.getIndustryStatistics();
    industryStats.slice(0, 10).forEach(stat => {
      console.log(`  ${stat._id}: ${stat.count} 只股票`);
    });
    
    return {
      totalRows,
      successRows,
      insertedCount,
      errors: parseResult.errors
    };
    
  } catch (error) {
    console.error('导入过程中发生错误:', error);
    throw error;
  } finally {
    // 关闭数据库连接
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
      console.log('数据库连接已关闭');
    }
  }
}

// 主函数
async function main() {
  try {
    const result = await importStockBasicInfo();
    console.log('\n导入成功完成!');
    console.log('结果摘要:', result);
    process.exit(0);
  } catch (error) {
    console.error('\n导入失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  importStockBasicInfo,
  removeBOM,
  parseDate,
  validateStockCode,
  cleanData
};
