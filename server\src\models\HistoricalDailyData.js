const mongoose = require('mongoose');

// 企业历史日级别数据模型 - 用于存储历史日级别交易数据
const historicalDailyDataSchema = new mongoose.Schema({
  // === 基本信息 ===
  // 证券代码
  stockCode: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  
  // 日期
  date: {
    type: Date,
    required: true,
    index: true
  },
  
  // === 价格信息 ===
  // 开盘价
  openPrice: {
    type: Number,
    required: true
  },
  
  // 收盘价
  closePrice: {
    type: Number,
    required: true
  },
  
  // 最高价
  highPrice: {
    type: Number,
    required: true
  },
  
  // 最低价
  lowPrice: {
    type: Number,
    required: true
  },
  
  // 昨收
  previousClose: {
    type: Number,
    default: null
  },
  
  // 涨停价
  limitUpPrice: {
    type: Number,
    default: null
  },
  
  // 跌停价
  limitDownPrice: {
    type: Number,
    default: null
  },
  
  // 均价
  averagePrice: {
    type: Number,
    default: null
  },
  
  // === 交易信息 ===
  // 成交量
  volume: {
    type: Number,
    required: true
  },
  
  // 成交额
  turnover: {
    type: Number,
    required: true
  },
  
  // 振幅(%)
  amplitude: {
    type: Number,
    default: null
  },
  
  // 涨跌幅(%)
  changePercent: {
    type: Number,
    default: null
  },
  
  // 涨跌额
  priceChange: {
    type: Number,
    default: null
  },
  
  // 换手率(%)
  turnoverRate: {
    type: Number,
    default: null
  },
  
  // === 52周信息 ===
  // 52周最高
  week52High: {
    type: Number,
    default: null
  },
  
  // 52周最低
  week52Low: {
    type: Number,
    default: null
  },
  
  // 今年以来涨幅(%)
  ytdGain: {
    type: Number,
    default: null
  },
  
  // 60日涨跌幅(%)
  sixtyDayChange: {
    type: Number,
    default: null
  },
  
  // === 每股指标 ===
  // 每股收益(静)
  earningsPerShare: {
    type: Number,
    default: null
  },
  
  // 每股收益(TTM)
  earningsPerShareTTM: {
    type: Number,
    default: null
  },
  
  // 每股净资产
  bookValuePerShare: {
    type: Number,
    default: null
  },
  
  // 股息(TTM)
  dividendTTM: {
    type: Number,
    default: null
  },
  
  // 股息率(TTM)(%)
  dividendYieldTTM: {
    type: Number,
    default: null
  },
  
  // === 估值指标 ===
  // 市盈率(静)
  peRatioStatic: {
    type: Number,
    default: null
  },
  
  // 市盈率(TTM)
  peRatioTTM: {
    type: Number,
    default: null
  },
  
  // 市净率
  pbRatio: {
    type: Number,
    default: null
  },
  
  // 创建时间
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  // 更新时间
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  // 自动添加 createdAt 和 updatedAt
  timestamps: true,
  
  // 设置集合名称
  collection: 'historical_daily_data'
});

// 复合索引 - 确保同一股票同一日期的唯一性
historicalDailyDataSchema.index({ 
  stockCode: 1, 
  date: 1 
}, { unique: true });

// 普通索引
historicalDailyDataSchema.index({ stockCode: 1, date: -1 }); // 按股票代码和日期排序
historicalDailyDataSchema.index({ date: -1 }); // 按日期排序
historicalDailyDataSchema.index({ volume: -1 }); // 按成交量排序
historicalDailyDataSchema.index({ turnover: -1 }); // 按成交额排序
historicalDailyDataSchema.index({ changePercent: -1 }); // 按涨跌幅排序

// === 字段映射 ===

// 获取中英文字段映射 - 保持与DailyStockData.js一致
historicalDailyDataSchema.statics.getFieldMapping = function() {
  return {
    '证券代码': 'stockCode',
    '日期': 'date',
    '开盘': 'openPrice',
    '收盘': 'closePrice',
    '最高': 'highPrice',
    '最低': 'lowPrice',
    '昨收': 'previousClose',
    '涨停价': 'limitUpPrice',
    '跌停价': 'limitDownPrice',
    '均价': 'averagePrice',
    '成交量': 'volume',
    '成交额': 'turnover',
    '振幅': 'amplitude',
    '涨跌幅': 'changePercent',
    '涨跌额': 'priceChange',
    '换手率': 'turnoverRate',
    '52周最高': 'week52High',
    '52周最低': 'week52Low',
    '今年以来涨幅': 'ytdGain',
    '60日涨跌幅': 'sixtyDayChange',
    '每股收益(静)': 'earningsPerShare',
    '每股收益(TTM)': 'earningsPerShareTTM',
    '每股净资产': 'bookValuePerShare',
    '股息(TTM)': 'dividendTTM',
    '股息率(TTM)': 'dividendYieldTTM',
    '市盈率(静)': 'peRatioStatic',
    '市盈率(TTM)': 'peRatioTTM',
    '市净率': 'pbRatio'
  };
};

// 获取字段的中文名称
historicalDailyDataSchema.statics.getChineseFieldName = function(englishField) {
  const mapping = this.getFieldMapping();
  for (const [chinese, english] of Object.entries(mapping)) {
    if (english === englishField) {
      return chinese;
    }
  }
  return englishField;
};

// 获取字段的英文名称
historicalDailyDataSchema.statics.getEnglishFieldName = function(chineseField) {
  const mapping = this.getFieldMapping();
  return mapping[chineseField] || chineseField;
};

// === 静态方法 ===

// 根据股票代码查找历史数据
historicalDailyDataSchema.statics.findByStockCode = function(stockCode, options = {}) {
  const query = { stockCode };
  if (options.startDate && options.endDate) {
    query.date = { $gte: new Date(options.startDate), $lte: new Date(options.endDate) };
  }
  
  return this.find(query).sort({ date: options.sortOrder || -1 });
};

// 根据日期范围查找
historicalDailyDataSchema.statics.findByDateRange = function(startDate, endDate, options = {}) {
  const query = {
    date: { $gte: new Date(startDate), $lte: new Date(endDate) }
  };
  
  if (options.stockCodes && options.stockCodes.length > 0) {
    query.stockCode = { $in: options.stockCodes };
  }
  
  return this.find(query)
    .sort({ date: -1, stockCode: 1 })
    .limit(options.limit || 0)
    .skip(options.skip || 0);
};

// 获取股票的最新历史数据
historicalDailyDataSchema.statics.findLatestByStockCode = function(stockCode) {
  return this.findOne({ stockCode }).sort({ date: -1 });
};

// 获取股票在特定日期的数据
historicalDailyDataSchema.statics.findByStockCodeAndDate = function(stockCode, date) {
  const startDate = new Date(date);
  const endDate = new Date(date);
  endDate.setDate(endDate.getDate() + 1);
  
  return this.findOne({
    stockCode,
    date: { $gte: startDate, $lt: endDate }
  });
};

// 批量查找多个股票的最新历史数据
historicalDailyDataSchema.statics.findLatestByStockCodes = function(stockCodes) {
  return this.aggregate([
    { $match: { stockCode: { $in: stockCodes } } },
    { $sort: { stockCode: 1, date: -1 } },
    { $group: {
        _id: '$stockCode',
        latestData: { $first: '$$ROOT' }
      }
    }
  ]);
};

// 计算股票在特定时间段的收益率
historicalDailyDataSchema.statics.calculateReturns = function(stockCode, startDate, endDate) {
  return this.aggregate([
    { 
      $match: { 
        stockCode,
        date: { $gte: new Date(startDate), $lte: new Date(endDate) }
      }
    },
    { $sort: { date: 1 } },
    { 
      $group: {
        _id: '$stockCode',
        firstPrice: { $first: '$closePrice' },
        lastPrice: { $last: '$closePrice' },
        count: { $sum: 1 }
      }
    },
    {
      $project: {
        stockCode: '$_id',
        startPrice: '$firstPrice',
        endPrice: '$lastPrice',
        totalReturn: { 
          $multiply: [
            { $divide: [{ $subtract: ['$lastPrice', '$firstPrice'] }, '$firstPrice'] },
            100
          ]
        },
        dataPoints: '$count'
      }
    }
  ]);
};

// 获取特定日期的涨幅排行榜
historicalDailyDataSchema.statics.findTopGainersByDate = function(date, limit = 10) {
  const startDate = new Date(date);
  const endDate = new Date(date);
  endDate.setDate(endDate.getDate() + 1);
  
  return this.find({
    date: { $gte: startDate, $lt: endDate },
    changePercent: { $ne: null }
  })
  .sort({ changePercent: -1 })
  .limit(limit);
};

// 获取特定日期的跌幅排行榜
historicalDailyDataSchema.statics.findTopLosersByDate = function(date, limit = 10) {
  const startDate = new Date(date);
  const endDate = new Date(date);
  endDate.setDate(endDate.getDate() + 1);
  
  return this.find({
    date: { $gte: startDate, $lt: endDate },
    changePercent: { $ne: null }
  })
  .sort({ changePercent: 1 })
  .limit(limit);
};

// 获取特定日期的成交额排行榜
historicalDailyDataSchema.statics.findTopVolumeByDate = function(date, limit = 10) {
  const startDate = new Date(date);
  const endDate = new Date(date);
  endDate.setDate(endDate.getDate() + 1);
  
  return this.find({
    date: { $gte: startDate, $lt: endDate },
    turnover: { $ne: null }
  })
  .sort({ turnover: -1 })
  .limit(limit);
};

// === 实例方法 ===

// 格式化显示
historicalDailyDataSchema.methods.toDisplayString = function() {
  return `${this.stockCode} - ${this.date.toISOString().split('T')[0]} 收盘:${this.closePrice}`;
};

// 获取涨跌状态
historicalDailyDataSchema.methods.getTrendStatus = function() {
  if (this.changePercent > 0) return 'up';
  if (this.changePercent < 0) return 'down';
  return 'flat';
};

// 获取格式化的价格变动
historicalDailyDataSchema.methods.getFormattedPriceChange = function() {
  if (this.priceChange === null || this.changePercent === null) return 'N/A';
  
  const sign = this.priceChange >= 0 ? '+' : '';
  return `${sign}${this.priceChange.toFixed(2)} (${sign}${this.changePercent.toFixed(2)}%)`;
};

// 获取格式化的成交额
historicalDailyDataSchema.methods.getFormattedTurnover = function() {
  if (this.turnover === null) return 'N/A';
  
  if (this.turnover >= 100000000) {
    return `${(this.turnover / 100000000).toFixed(2)}亿`;
  } else if (this.turnover >= 10000) {
    return `${(this.turnover / 10000).toFixed(2)}万`;
  }
  return this.turnover.toString();
};

// 计算当日收益率
historicalDailyDataSchema.methods.getDailyReturn = function() {
  if (this.changePercent !== null) {
    return this.changePercent / 100;
  }
  return null;
};

// 检查数据是否有效
historicalDailyDataSchema.methods.isValidData = function() {
  // 检查价格是否合理
  if (this.openPrice <= 0 || this.closePrice <= 0 || this.highPrice <= 0 || this.lowPrice <= 0) {
    return false;
  }
  
  // 检查最高价和最低价的逻辑关系
  if (this.highPrice < this.lowPrice) {
    return false;
  }
  
  // 检查开盘价、收盘价是否在最高最低价范围内
  if (this.openPrice > this.highPrice || this.openPrice < this.lowPrice ||
      this.closePrice > this.highPrice || this.closePrice < this.lowPrice) {
    return false;
  }
  
  // 检查成交量和成交额
  if (this.volume < 0 || this.turnover < 0) {
    return false;
  }
  
  return true;
};

// 获取技术指标摘要
historicalDailyDataSchema.methods.getTechnicalSummary = function() {
  return {
    priceChange: this.getFormattedPriceChange(),
    turnover: this.getFormattedTurnover(),
    pe: this.peRatioTTM ? this.peRatioTTM.toFixed(2) : 'N/A',
    pb: this.pbRatio ? this.pbRatio.toFixed(2) : 'N/A',
    turnoverRate: this.turnoverRate ? `${this.turnoverRate.toFixed(2)}%` : 'N/A',
    amplitude: this.amplitude ? `${this.amplitude.toFixed(2)}%` : 'N/A'
  };
};

// 中间件：保存前自动更新时间
historicalDailyDataSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// 导出模型
module.exports = mongoose.model('HistoricalDailyData', historicalDailyDataSchema);
