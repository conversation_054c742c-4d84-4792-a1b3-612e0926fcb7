const Joi = require('joi');

// 查询股票信息请求验证模式
const queryStockInfoSchema = Joi.object({
  stockCode: Joi.string()
    .pattern(/^\d{6}$/)
    .required()
    .messages({
      'string.pattern.base': '证券代码必须是6位数字',
      'any.required': '证券代码是必填项'
    })
});

/**
 * 验证查询股票信息请求
 */
const validateQueryStockInfoRequest = (req, res, next) => {
  const { error } = queryStockInfoSchema.validate(req.body);

  if (error) {
    return res.status(400).json({
      success: false,
      message: '请求参数验证失败',
      errors: error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    });
  }

  next();
};

module.exports = {
  validateQueryStockInfoRequest
};