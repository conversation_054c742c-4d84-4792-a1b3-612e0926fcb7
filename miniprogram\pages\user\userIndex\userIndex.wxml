<!--pages/user/userIndex/userIndex.wxml-->
<view class="user-page">
  <!-- 用户信息卡片 -->
  <view class="user-header card-base margin-bottom-lg">
    <view class="user-avatar-section flex-row align-center" bindtap="{{!isLoggedIn ? 'navigateToLogin' : ''}}">
      <view class="avatar-container">
        <image 
          class="user-avatar" 
          src="{{userInfo.avatarUrl || '/assets/icons/default-avatar.png'}}" 
          mode="aspectFill"
        />
        <view class="avatar-badge" wx:if="{{isLoggedIn}}">
          <text class="iconfont icon-vip"></text>
        </view>
      </view>
      <view class="user-info-text flex-col justify-center margin-left-lg">
        <view class="user-name text-lg font-bold" wx:if="{{isLoggedIn}}">
          {{userInfo.nickName || '用户'}}
        </view>
        <view class="user-name text-lg font-bold" wx:else>
          点击登录
        </view>
        <view class="user-description text-sm text-secondary margin-top-xs" wx:if="{{isLoggedIn}}">
          {{userInfo.description || 'AI量化投资用户'}}
        </view>
        <view class="login-hint text-sm text-tertiary margin-top-xs" wx:else>
          登录后享受更多服务
        </view>
      </view>
    </view>
    
    <!-- 用户统计信息 -->
    <view class="user-stats flex-row justify-between margin-top-lg" wx:if="{{isLoggedIn}}">
      <view class="stat-item text-center">
        <view class="stat-number text-xl font-bold text-primary">{{userStats.portfolioCount || 0}}</view>
        <view class="stat-label text-xs text-secondary">投资组合</view>
      </view>
      <view class="stat-item text-center">
        <view class="stat-number text-xl font-bold text-success">{{userStats.totalReturn || '0.00%'}}</view>
        <view class="stat-label text-xs text-secondary">总收益率</view>
      </view>
      <view class="stat-item text-center">
        <view class="stat-number text-xl font-bold text-warning">{{userStats.riskLevel || 'N/A'}}</view>
        <view class="stat-label text-xs text-secondary">风险等级</view>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <!-- 投资相关功能 -->
    <view class="menu-group card-base margin-bottom-lg">
      <view class="menu-group-title text-base font-medium text-primary margin-bottom-base">
        投资管理
      </view>
      
      <view class="menu-item list-item" bindtap="navigateToPortfolio">
        <view class="menu-icon">
          <text class="iconfont icon-portfolio"></text>
        </view>
        <view class="menu-content flex-col justify-center margin-left-base">
          <view class="menu-title text-base">我的投资组合</view>
          <view class="menu-desc text-xs text-secondary">查看和管理投资组合</view>
        </view>
        <view class="menu-arrow">
          <text class="iconfont icon-arrow-right text-tertiary"></text>
        </view>
      </view>

      <view class="menu-item list-item" bindtap="navigateToStrategy">
        <view class="menu-icon">
          <text class="iconfont icon-strategy"></text>
        </view>
        <view class="menu-content flex-col justify-center margin-left-base">
          <view class="menu-title text-base">AI策略</view>
          <view class="menu-desc text-xs text-secondary">智能投资策略推荐</view>
        </view>
        <view class="menu-arrow">
          <text class="iconfont icon-arrow-right text-tertiary"></text>
        </view>
      </view>

      <view class="menu-item list-item" bindtap="navigateToAnalysis">
        <view class="menu-icon">
          <text class="iconfont icon-analysis"></text>
        </view>
        <view class="menu-content flex-col justify-center margin-left-base">
          <view class="menu-title text-base">投资分析</view>
          <view class="menu-desc text-xs text-secondary">详细的投资分析报告</view>
        </view>
        <view class="menu-arrow">
          <text class="iconfont icon-arrow-right text-tertiary"></text>
        </view>
      </view>
    </view>

    <!-- 设置相关功能 -->
    <view class="menu-group card-base margin-bottom-lg">
      <view class="menu-group-title text-base font-medium text-primary margin-bottom-base">
        设置与帮助
      </view>
      
      <view class="menu-item list-item" bindtap="navigateToSettings">
        <view class="menu-icon">
          <text class="iconfont icon-settings"></text>
        </view>
        <view class="menu-content flex-col justify-center margin-left-base">
          <view class="menu-title text-base">账户设置</view>
          <view class="menu-desc text-xs text-secondary">个人信息和偏好设置</view>
        </view>
        <view class="menu-arrow">
          <text class="iconfont icon-arrow-right text-tertiary"></text>
        </view>
      </view>

      <view class="menu-item list-item" bindtap="navigateToHelp">
        <view class="menu-icon">
          <text class="iconfont icon-help"></text>
        </view>
        <view class="menu-content flex-col justify-center margin-left-base">
          <view class="menu-title text-base">帮助与反馈</view>
          <view class="menu-desc text-xs text-secondary">使用指南和问题反馈</view>
        </view>
        <view class="menu-arrow">
          <text class="iconfont icon-arrow-right text-tertiary"></text>
        </view>
      </view>

      <view class="menu-item list-item" bindtap="navigateToAbout">
        <view class="menu-icon">
          <text class="iconfont icon-about"></text>
        </view>
        <view class="menu-content flex-col justify-center margin-left-base">
          <view class="menu-title text-base">关于言策</view>
          <view class="menu-desc text-xs text-secondary">了解更多关于言策AI</view>
        </view>
        <view class="menu-arrow">
          <text class="iconfont icon-arrow-right text-tertiary"></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 退出登录按钮 -->
  <view class="action-section" wx:if="{{isLoggedIn}}">
    <button 
      class="action-button btn-base btn-secondary-style full-width" 
      bindtap="handleLogout"
    >
      退出登录
    </button>
  </view>
</view>
