# 条件选股API使用说明

## API概述

条件选股功能允许您根据当日数据和财务数据筛选股票，返回符合条件的证券代码列表。

## 接口信息

### 筛选股票
- **接口地址**: `POST /api/filter/stocks`
- **功能描述**: 根据筛选条件返回符合的证券代码列表

### 获取支持的筛选条件
- **接口地址**: `GET /api/filter/conditions`
- **功能描述**: 获取所有支持的筛选条件列表

## 筛选条件说明

### 当日数据筛选条件 (dailyConditions)

| 条件名称 | 字段名 | 数据类型 | 说明 |
|---------|--------|----------|------|
| 市盈率(动)范围 | peRatioDynamicRange | Range | 动态市盈率范围 |
| 市盈率(静)范围 | peRatioStaticRange | Range | 静态市盈率范围 |
| 市盈率(TTM)范围 | peRatioTTMRange | Range | TTM市盈率范围 |
| 市净率范围 | pbRatioRange | Range | 市净率范围 |
| 总市值范围 | totalMarketCapRange | Range | 总市值范围 |
| 流通市值范围 | circulatingMarketCapRange | Range | 流通市值范围 |
| 收盘是否涨停 | isLimitUp | Boolean | 收盘价是否达到涨停价 |
| 收盘是否跌停 | isLimitDown | Boolean | 收盘价是否达到跌停价 |
| 是否触及涨停 | touchedLimitUp | Boolean | 最高价是否达到涨停价 |
| 是否触及跌停 | touchedLimitDown | Boolean | 最低价是否达到跌停价 |
| 换手率范围 | turnoverRateRange | Range | 换手率范围(%) |
| 涨跌幅范围 | changePercentRange | Range | 涨跌幅范围(%) |
| 振幅范围 | amplitudeRange | Range | 振幅范围(%) |
| 今年以来涨幅范围 | ytdGainRange | Range | 今年以来涨幅范围(%) |
| 60日涨跌幅范围 | sixtyDayChangeRange | Range | 60日涨跌幅范围(%) |

### 财务数据筛选条件 (financialConditions)

| 条件名称 | 字段名 | 数据类型 | 说明 |
|---------|--------|----------|------|
| 净利润同比增长率范围 | netProfitGrowthRateRange | Range | 净利润同比增长率范围 |
| 扣非净利润同比增长率范围 | nonRecurringNetProfitGrowthRateRange | Range | 扣非净利润同比增长率范围 |
| 营业总收入同比增长率范围 | totalRevenueGrowthRateRange | Range | 营业总收入同比增长率范围 |
| 销售净利率范围 | netProfitMarginRange | Range | 销售净利率范围 |
| 销售毛利率范围 | grossProfitMarginRange | Range | 销售毛利率范围 |
| 净资产收益率范围 | returnOnEquityRange | Range | 净资产收益率范围 |
| 净资产收益率范围 | dilutedReturnOnEquityRange | Range | 净资产收益率-摊薄范围 |
| 流动比率范围 | currentRatioRange | Range | 流动比率范围 |
| 速动比率范围 | quickRatioRange | Range | 速动比率范围 |
| 产权比率范围 | equityRatioRange | Range | 产权比率范围 |
| 资产负债率范围 | debtToAssetRatioRange | Range | 资产负债率范围 |
| 存货周转率范围 | inventoryTurnoverRatioRange | Range | 存货周转率范围 |
| 存货周转天数范围 | inventoryTurnoverDaysRange | Range | 存货周转天数范围 |
| 应收账款周转天数范围 | accountsReceivableTurnoverDaysRange | Range | 应收账款周转天数范围 |

### Range 类型说明

Range 类型是一个对象，包含以下字段：
- `min`: 最小值（可为null）
- `max`: 最大值（可为null）

## 请求示例

### 示例1：基本筛选 - 只使用当日数据条件

```json
{
  "dailyConditions": {
    "peRatioTTMRange": {
      "min": 10,
      "max": 30
    },
    "pbRatioRange": {
      "min": 1,
      "max": 5
    },
    "totalMarketCapRange": {
      "min": **********,
      "max": null
    }
  },
  "targetDate": "2025-09-12"
}
```

### 示例2：综合筛选 - 同时使用当日数据和财务数据条件

```json
{
  "dailyConditions": {
    "peRatioTTMRange": {
      "min": 5,
      "max": 25
    },
    "changePercentRange": {
      "min": -5,
      "max": 5
    },
    "turnoverRateRange": {
      "min": 1,
      "max": null
    }
  },
  "financialConditions": {
    "netProfitGrowthRateRange": {
      "min": 10,
      "max": null
    },
    "returnOnEquityRange": {
      "min": 15,
      "max": null
    },
    "debtToAssetRatioRange": {
      "min": null,
      "max": 60
    }
  },
  "targetDate": "2025-09-12"
}
```

### 示例3：涨停股筛选

```json
{
  "dailyConditions": {
    "isLimitUp": true,
    "totalMarketCapRange": {
      "min": 500000000,
      "max": null
    }
  },
  "targetDate": "2025-09-12"
}
```

## 响应示例

### 成功响应

```json
{
  "success": true,
  "message": "股票筛选成功",
  "data": {
    "stockCodes": [
      "000001",
      "000002",
      "600036",
      "600519"
    ],
    "count": 4,
    "timestamp": "2025-09-12T10:30:00.000Z"
  }
}
```

### 错误响应

```json
{
  "success": false,
  "message": "请求参数验证失败",
  "errors": [
    {
      "field": "dailyConditions.peRatioTTMRange",
      "message": "最小值不能大于最大值",
      "value": {"min": 30, "max": 10}
    }
  ]
}
```

## 使用注意事项

1. **至少提供一个筛选条件**: 必须提供 `dailyConditions` 或 `financialConditions` 中的至少一个

2. **范围条件**: min 和 max 可以为 null，表示不限制该边界

3. **财务数据**: 财务筛选条件使用最新年度的财务数据

4. **日期格式**: targetDate 必须使用 YYYY-MM-DD 格式，如果不提供则使用默认逻辑

5. **数据类型**: 
   - 百分比字段（如涨跌幅、换手率）以小数形式表示，如 5% 输入为 5
   - 市值以元为单位
   - 比率类指标以小数形式表示，如 1.5 表示 1.5:1

6. **性能考虑**: 建议合理设置筛选条件范围，避免过于宽泛的查询

## 集成到主路由

在您的主路由文件中添加：

```javascript
const filterRoutes = require('./src/routes/filter.routes');
app.use('/api/filter', filterRoutes);
```
