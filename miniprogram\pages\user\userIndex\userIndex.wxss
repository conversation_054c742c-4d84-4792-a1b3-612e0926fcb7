/* pages/user/userIndex/userIndex.wxss */
@import '../../../styles/base.wxss';
@import '../../../styles/mixins.wxss';

.user-page {
  background-color: var(--bg-secondary);
  min-height: 100vh;
  padding: var(--spacing-lg);
  padding-bottom: calc(var(--spacing-lg) + env(safe-area-inset-bottom));
}

/* ==================== 用户头部信息 ==================== */
.user-header {
  background: var(--gradient-primary);
  color: var(--text-inverse);
  position: relative;
  overflow: hidden;
}

.user-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.user-avatar-section {
  position: relative;
  z-index: 2;
}

.avatar-container {
  position: relative;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  background-color: var(--bg-card);
}

.avatar-badge {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 32rpx;
  height: 32rpx;
  background: var(--accent-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid var(--text-inverse);
}

.avatar-badge .iconfont {
  font-size: 16rpx;
  color: var(--text-inverse);
}

.user-info-text {
  flex: 1;
}

.user-name {
  color: var(--text-inverse);
}

.user-description,
.login-hint {
  color: rgba(255, 255, 255, 0.8);
}

/* 用户统计信息 */
.user-stats {
  padding-top: var(--spacing-lg);
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
}

.stat-item {
  flex: 1;
}

.stat-number {
  color: var(--text-inverse);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  color: rgba(255, 255, 255, 0.7);
}

/* ==================== 菜单部分 ==================== */
.menu-section {
  flex: 1;
}

.menu-group {
  background-color: var(--bg-card);
}

.menu-group-title {
  border-bottom: 1rpx solid var(--border-light);
  padding-bottom: var(--spacing-sm);
  margin-bottom: var(--spacing-base);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg) 0;
  border-bottom: 1rpx solid var(--border-lightest);
  transition: var(--transition-base);
  cursor: pointer;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: var(--bg-secondary);
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: var(--radius-base);
  background: var(--gradient-light);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.menu-icon .iconfont {
  font-size: 36rpx;
  color: var(--primary-color);
}

.menu-content {
  flex: 1;
  min-width: 0;
}

.menu-title {
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.menu-desc {
  color: var(--text-secondary);
  line-height: 1.4;
}

.menu-arrow {
  margin-left: var(--spacing-base);
}

.menu-arrow .iconfont {
  font-size: 24rpx;
}

/* ==================== 操作按钮 ==================== */
.action-section {
  margin-top: var(--spacing-xl);
}

.action-button {
  height: 88rpx;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
}

/* 次要按钮样式 */
.btn-secondary-style {
  background-color: var(--bg-card);
  color: var(--text-secondary);
  border: 1rpx solid var(--border-base);
}

.btn-secondary-style:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--border-dark);
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 320px) {
  .user-avatar {
    width: 100rpx;
    height: 100rpx;
  }
  
  .menu-icon {
    width: 60rpx;
    height: 60rpx;
  }
  
  .menu-icon .iconfont {
    font-size: 28rpx;
  }
}

/* ==================== 动画效果 ==================== */
.menu-item {
  transform: translateX(0);
  transition: transform var(--transition-base), background-color var(--transition-base);
}

.menu-item:active {
  transform: translateX(4rpx);
}

/* 卡片入场动画 */
.user-header,
.menu-group {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 头像旋转效果 */
.user-avatar {
  transition: transform var(--transition-base);
}

.user-avatar:active {
  transform: rotate(5deg) scale(1.05);
}
