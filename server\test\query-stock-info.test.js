/**
 * 股票详情查询API测试脚本
 * 
 * 使用方法：
 * 1. 确保服务器正在运行
 * 2. 运行: node test/query-stock-info.test.js
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3000/api/v1/query';
const TEST_STOCK_CODE = '000001'; // 平安银行

// 测试用例
const testCases = [
  {
    name: '测试1：查询股票信息',
    url: `${BASE_URL}/stock-info`,
    method: 'POST',
    data: {
      stockCode: TEST_STOCK_CODE
    }
  },
  {
    name: '测试2：错误的证券代码格式',
    url: `${BASE_URL}/stock-info`,
    method: 'POST',
    data: {
      stockCode: '12345' // 错误格式：只有5位数字
    },
    expectError: true
  },
  {
    name: '测试3：缺少证券代码',
    url: `${BASE_URL}/stock-info`,
    method: 'POST',
    data: {
      // 缺少stockCode
    },
    expectError: true
  }
];

// 执行测试
async function runTests() {
  console.log('🚀 开始执行股票详情查询API测试...\n');
  
  let passedTests = 0;
  let totalTests = testCases.length;
  
  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`📋 ${testCase.name}`);
    
    try {
      let response;
      if (testCase.method === 'GET') {
        response = await axios.get(testCase.url);
      } else {
        response = await axios.post(testCase.url, testCase.data);
      }
      
      if (testCase.expectError) {
        console.log('❌ 测试失败：期望错误但请求成功');
        console.log('响应:', JSON.stringify(response.data, null, 2));
      } else {
        console.log('✅ 测试通过');
        console.log('响应状态:', response.status);
        console.log('响应消息:', response.data.message);
        console.log('响应数据:', JSON.stringify(response.data.data, null, 2));
        
        // 显示部分响应数据
        if (response.data.data) {
          console.log('股票代码:', response.data.data.stockCode);
          console.log('包含的数据表:', Object.keys(response.data.data).filter(key =>
            !['stockCode', 'timestamp'].includes(key)
          ));
        }
        passedTests++;
      }
      
    } catch (error) {
      if (testCase.expectError) {
        console.log('✅ 测试通过（正确返回错误）');
        console.log('错误状态:', error.response?.status);
        console.log('错误消息:', error.response?.data?.message);
        passedTests++;
      } else {
        console.log('❌ 测试失败：请求出错');
        console.log('错误:', error.response?.data || error.message);
      }
    }
    
    console.log(''); // 空行分隔
  }
  
  // 测试结果汇总
  console.log('📊 测试结果汇总');
  console.log('='.repeat(50));
  console.log(`总测试数: ${totalTests}`);
  console.log(`通过测试: ${passedTests}`);
  console.log(`失败测试: ${totalTests - passedTests}`);
  console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试都通过了！');
  } else {
    console.log('⚠️  有测试失败，请检查API实现');
  }
}

// 检查服务器连接
async function checkServerConnection() {
  try {
    const response = await axios.post(`${BASE_URL}/stock-info`, {
      stockCode: '000001'
    });
    console.log('✅ 服务器连接正常');
    return true;
  } catch (error) {
    console.log('❌ 无法连接到服务器');
    console.log('请确保服务器正在运行在 http://localhost:3000');
    console.log('错误:', error.message);
    return false;
  }
}

// 主函数
async function main() {
  console.log('股票详情查询API测试工具');
  console.log('='.repeat(50));
  
  // 检查服务器连接
  const isConnected = await checkServerConnection();
  if (!isConnected) {
    process.exit(1);
  }
  
  console.log(''); // 空行
  
  // 运行测试
  await runTests();
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { runTests, checkServerConnection };
