const mongoose = require('mongoose');
const csv = require('csv-parser');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

// 导入企业业务数据模型
const CompanyBusinessData = require('../src/models/CompanyBusinessData');

// 检测和移除BOM的函数
function removeBOM(content) {
  if (content.charCodeAt(0) === 0xFEFF) {
    return content.slice(1);
  }
  return content;
}

async function importBusinessData() {
  try {
    // 连接MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('MongoDB连接成功');

    // CSV文件路径
    const csvFilePath = path.join(__dirname, '..', '..', 'data_gp', '沪深上市公司列表_业务信息.csv');
    
    // 检查文件是否存在
    if (!fs.existsSync(csvFilePath)) {
      throw new Error(`CSV文件不存在: ${csvFilePath}`);
    }

    console.log('开始读取CSV文件...');

    // 读取文件内容并检测BOM
    let fileContent = fs.readFileSync(csvFilePath, 'utf8');
    fileContent = removeBOM(fileContent);
    
    // 检查是否检测到BOM
    if (fileContent !== fs.readFileSync(csvFilePath, 'utf8')) {
      console.log('检测到BOM，已自动移除');
    }

    // 解析CSV数据
    const data = [];
    
    await new Promise((resolve, reject) => {
      const stream = require('stream');
      const readable = new stream.Readable();
      readable.push(fileContent);
      readable.push(null);
      
      readable
        .pipe(csv())
        .on('data', (row) => {
          data.push(row);
        })
        .on('end', () => {
          console.log(`CSV文件读取完成，共 ${data.length} 行数据`);
          resolve();
        })
        .on('error', reject);
    });

    if (data.length === 0) {
      console.log('CSV文件中没有数据');
      return;
    }

    // 清空现有数据（可选）
    console.log('清空现有企业业务数据...');
    await CompanyBusinessData.deleteMany({});

    // 处理CSV数据并保存到数据库
    const promises = data.map(async (row) => {
      // 去除字段名的空格并获取数据
      const cleanRow = {};
      Object.keys(row).forEach(key => {
        cleanRow[key.trim()] = row[key];
      });

      const businessData = {
        stockCode: cleanRow['证券代码'] ? String(cleanRow['证券代码']).padStart(6, '0') : null,
        mainBusiness: cleanRow['主营业务'] || null,
        productType: cleanRow['产品类型'] || null,
        productName: cleanRow['产品名称'] || null,
        businessScope: cleanRow['经营范围'] || null
      };

      // 验证必需字段
      if (!businessData.stockCode) {
        console.warn('跳过缺少证券代码的记录:', cleanRow);
        return null;
      }

      try {
        // 使用upsert更新或插入数据
        const result = await CompanyBusinessData.findOneAndUpdate(
          { stockCode: businessData.stockCode },
          businessData,
          { 
            upsert: true, 
            new: true,
            setDefaultsOnInsert: true 
          }
        );
        return result;
      } catch (error) {
        console.error(`保存股票 ${businessData.stockCode} 的数据时出错:`, error.message);
        return null;
      }
    });

    console.log('开始批量保存数据到数据库...');
    const results = await Promise.all(promises);
    
    // 统计结果
    const successCount = results.filter(result => result !== null).length;
    const failCount = results.length - successCount;
    
    console.log(`数据导入完成！`);
    console.log(`成功导入: ${successCount} 条记录`);
    console.log(`失败: ${failCount} 条记录`);

    // 验证导入结果
    const totalCount = await CompanyBusinessData.countDocuments();
    console.log(`数据库中总共有 ${totalCount} 条企业业务记录`);

    // 显示一些示例数据
    console.log('\n=== 示例数据 ===');
    const sampleData = await CompanyBusinessData.find().limit(3);
    sampleData.forEach(item => {
      console.log(`${item.stockCode}: ${item.getFormattedMainBusiness(100)}`);
    });

  } catch (error) {
    console.error('导入数据时发生错误:', error);
  } finally {
    // 关闭数据库连接
    await mongoose.connection.close();
    console.log('数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  importBusinessData().catch(console.error);
}

module.exports = importBusinessData;
