import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def get_board_info(code_board_file):
    """读取板块信息"""
    df_board = pd.read_csv(code_board_file, dtype={'证券代码': str})
    return dict(zip(df_board['证券代码'], df_board['板块']))

def calculate_limit_prices(close_price, board):
    """计算涨停价和跌停价"""
    if board == "沪市主板" or board == "深市主板":
        limit_ratio = 0.10  # 10%
    elif board == "创业板" or board == "科创板":
        limit_ratio = 0.20  # 20%
    else:
        limit_ratio = 0.10  # 默认10%
    
    up_limit = close_price * (1 + limit_ratio)
    down_limit = close_price * (1 - limit_ratio)
    
    return round(up_limit, 2), round(down_limit, 2)

def calculate_new_fields(df, board):
    """计算所有新字段"""
    # 确保日期列是datetime类型
    df['日期'] = pd.to_datetime(df['日期'])
    df = df.sort_values('日期').reset_index(drop=True)
    
    # 初始化新字段
    df['昨收'] = np.nan
    df['涨停价'] = np.nan
    df['跌停价'] = np.nan
    df['均价'] = np.nan
    df['52周最高'] = np.nan
    df['52周最低'] = np.nan
    df['今年以来涨幅(%)'] = np.nan
    df['60日涨跌幅(%)'] = np.nan
    
    for i in range(len(df)):
        current_date = df.loc[i, '日期']
        current_close = df.loc[i, '收盘']
        
        # 1. 昨收 - 前一个交易日的收盘价
        if i > 0:
            df.loc[i, '昨收'] = df.loc[i-1, '收盘']
            
            # 计算涨停价和跌停价（基于昨收）
            up_limit, down_limit = calculate_limit_prices(df.loc[i, '昨收'], board)
            df.loc[i, '涨停价'] = up_limit
            df.loc[i, '跌停价'] = down_limit
        
        # 2. 均价 = 成交额 / 成交量
        if df.loc[i, '成交量'] > 0:
            df.loc[i, '均价'] = round(df.loc[i, '成交额'] / (df.loc[i, '成交量']*100), 2)
        
        # 3. 52周最高和最低（需要252个交易日的数据）
        if i >= 251:  # 至少需要252个交易日
            week_52_data = df.loc[i-251:i]
            df.loc[i, '52周最高'] = round(week_52_data['最高'].max(), 2)
            df.loc[i, '52周最低'] = round(week_52_data['最低'].min(), 2)
        
        # 4. 今年以来涨幅(%)
        current_year = current_date.year
        if current_year > df.loc[0, '日期'].year:  # 不是第一年
            # 找到去年最后一个交易日
            last_year_data = df[df['日期'].dt.year == current_year - 1]
            if not last_year_data.empty:
                last_year_close = last_year_data.iloc[-1]['收盘']
                ytd_return = ((current_close - last_year_close) / last_year_close) * 100
                df.loc[i, '今年以来涨幅(%)'] = round(ytd_return, 2)
        
        # 5. 60日涨跌幅(%)
        if i >= 59:  # 至少需要60个交易日
            days_60_ago_close = df.loc[i-59, '收盘']
            return_60d = ((current_close - days_60_ago_close) / days_60_ago_close) * 100
            df.loc[i, '60日涨跌幅(%)'] = round(return_60d, 2)
    
    return df

def process_all_files(input_folder, output_folder, board_file):
    """处理所有CSV文件"""
    # 创建输出文件夹
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    # 读取板块信息
    print("读取板块信息...")
    board_dict = get_board_info(board_file)
    
    # 获取所有CSV文件
    csv_files = [f for f in os.listdir(input_folder) if f.endswith('.csv')]
    
    print(f"找到 {len(csv_files)} 个CSV文件")
    
    processed_count = 0
    error_count = 0
    
    for csv_file in csv_files:
        try:
            print(f"处理文件: {csv_file} ({processed_count + 1}/{len(csv_files)})")
            
            # 读取CSV文件
            input_path = os.path.join(input_folder, csv_file)
            df = pd.read_csv(input_path, dtype={'证券代码': str})
            
            if df.empty:
                print(f"  警告: {csv_file} 为空文件，跳过")
                continue
            
            # 获取证券代码和对应板块
            stock_code = df.iloc[0]['证券代码']
            board = board_dict.get(stock_code, "沪市主板")  # 默认为沪市主板
            
            print(f"  证券代码: {stock_code}, 板块: {board}")
            
            # 计算新字段
            df_new = calculate_new_fields(df.copy(), board)
            
            # 保存到新文件夹
            output_path = os.path.join(output_folder, csv_file)
            df_new.to_csv(output_path, index=False, encoding='utf-8-sig')
            
            processed_count += 1
            print(f"  完成: {csv_file}")
            
        except Exception as e:
            error_count += 1
            print(f"  错误: 处理 {csv_file} 时出现错误: {str(e)}")
            continue
    
    print(f"\n处理完成!")
    print(f"成功处理: {processed_count} 个文件")
    print(f"处理失败: {error_count} 个文件")

def main():
    # 设置文件路径
    input_folder = "企业历史日级别价格数据_end20250904"
    output_folder = "企业历史日级别价格数据_带自身计算字段_end20250904"
    board_file = "沪深上市公司列表.csv"
    
    # 检查输入文件夹是否存在
    if not os.path.exists(input_folder):
        print(f"错误: 输入文件夹 '{input_folder}' 不存在!")
        return
    
    # 检查板块文件是否存在
    if not os.path.exists(board_file):
        print(f"错误: 板块文件 '{board_file}' 不存在!")
        return
    
    print("开始处理股票数据...")
    print(f"输入文件夹: {input_folder}")
    print(f"输出文件夹: {output_folder}")
    print(f"板块文件: {board_file}")
    print("-" * 50)
    
    # 处理所有文件
    process_all_files(input_folder, output_folder, board_file)

if __name__ == "__main__":
    main()
