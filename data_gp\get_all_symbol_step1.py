import akshare as ak
import os

if not os.path.exists("A股上市公司列表.csv"):
    stock_info_a_code_name_df = ak.stock_info_a_code_name()
    stock_info_a_code_name_df.to_csv("A股上市公司列表.csv", index=False, encoding="utf_8_sig")

if not os.path.exists("沪市主板A股.csv"):
    stock_info_sh_name_code_df = ak.stock_info_sh_name_code(symbol="主板A股")
    stock_info_sh_name_code_df.to_csv("沪市主板A股.csv", index=False, encoding="utf_8_sig")

if not os.path.exists("沪市科创板A股.csv"):
    stock_info_sh_name_code_df = ak.stock_info_sh_name_code(symbol="科创板")
    stock_info_sh_name_code_df.to_csv("沪市科创板A股.csv", index=False, encoding="utf_8_sig")

if not os.path.exists("深市主板A股.csv"):
    stock_info_sz_name_code_df = ak.stock_info_sz_name_code(symbol="A股列表")
    stock_info_sz_name_code_df.to_csv("深市主板A股.csv", index=False, encoding="utf_8_sig")

