import pandas as pd

def modify_csv_columns(input_file, output_file):
    """
    修改CSV文件的列名并保存到新文件
    
    Args:
        input_file (str): 输入CSV文件路径
        output_file (str): 输出CSV文件路径
    """
    try:
        # 读取CSV文件，指定证券代码为字符串类型，避免000001被读成1
        df = pd.read_csv(input_file, 
                        encoding='utf-8',
                        dtype={'证券代码': str})  # 确保证券代码保持为字符串
        
        # 定义需要修改的列名映射
        column_mapping = {
            '涨跌幅': '涨跌幅(%)',
            '换手率': '换手率(%)',
            '振幅': '振幅(%)',
            '今年以来涨幅': '今年以来涨幅(%)',
            '股息率(TTM)': '股息率(TTM)(%)',
            '60日涨跌幅': '60日涨跌幅(%)'
        }
        
        # 重命名列
        df.rename(columns=column_mapping, inplace=True)
        
        # 保存到新的CSV文件，确保证券代码不会丢失前导零
        df.to_csv(output_file, 
                 index=False, 
                 encoding='utf-8')  # 对所有字段加引号，确保格式正确
        
        print(f"文件处理完成！")
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file}")
        print(f"修改的列名: {list(column_mapping.keys())}")
        print(f"数据行数: {len(df)}")
        
        # 显示前几行数据以验证证券代码格式
        print("\n前5行数据预览:")
        print(df[['证券代码', '涨跌幅(%)', '换手率(%)']].head())
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 '{input_file}'")
    except Exception as e:
        print(f"处理文件时发生错误: {e}")

if __name__ == "__main__":
    # 定义输入和输出文件名
    input_filename = "企业当日数据_全字段_雪球为主_20250915.csv"
    output_filename = "企业当日数据_全字段_雪球为主_20250915_修改后.csv"
    
    # 执行列名修改
    modify_csv_columns(input_filename, output_filename)
