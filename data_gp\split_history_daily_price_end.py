"""
脚本作用：
将包含所有股票历史数据的大CSV文件按证券代码分割成多个小CSV文件
- 按证券代码分组，将同一证券代码的所有历史数据放到一个文件中
- 输出文件命名格式：证券代码.csv（如：000001.csv, 600000.csv等）
- 确保证券代码格式正确，避免前导0丢失问题
- 所有分割后的CSV文件保存到"企业历史日级别价格数据_end20250904"文件夹中
"""

import pandas as pd
import os
from datetime import datetime

def split_stock_data_by_code(input_file_path, output_folder):
    """
    按证券代码分割股票历史数据CSV文件
    
    Parameters:
    input_file_path: 输入的大CSV文件路径
    output_folder: 输出文件夹路径
    """
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file_path):
        print(f"错误: 找不到输入文件 {input_file_path}")
        return
    
    # 创建输出文件夹
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"创建输出文件夹: {output_folder}")
    
    print("开始读取数据文件...")
    
    # 读取CSV文件，确保证券代码保持字符串格式
    try:
        df = pd.read_csv(input_file_path, dtype={'证券代码': str})
        print(f"成功读取数据，共 {len(df)} 条记录")
    except Exception as e:
        print(f"读取文件失败: {e}")
        return
    
    # 获取所有唯一的证券代码
    unique_codes = df['证券代码'].unique()
    print(f"发现 {len(unique_codes)} 个不同的证券代码")
    
    # 按证券代码分组并保存
    success_count = 0
    error_count = 0
    
    for i, stock_code in enumerate(unique_codes, 1):
        try:
            # 筛选当前证券代码的所有数据
            stock_data = df[df['证券代码'] == stock_code].copy()
            
            # 按日期排序
            stock_data = stock_data.sort_values('日期')
            
            # 构建输出文件路径
            output_file_path = os.path.join(output_folder, f"{stock_code}.csv")
            
            # 保存到CSV文件
            stock_data.to_csv(output_file_path, index=False, encoding='utf-8-sig')
            
            success_count += 1
            print(f"进度 {i}/{len(unique_codes)}: 证券代码 {stock_code} - 保存 {len(stock_data)} 条记录")
            
        except Exception as e:
            error_count += 1
            print(f"进度 {i}/{len(unique_codes)}: 证券代码 {stock_code} - 保存失败: {e}")
    
    print(f"\n分割完成!")
    print(f"成功处理: {success_count} 个文件")
    print(f"处理失败: {error_count} 个文件")
    print(f"输出文件夹: {output_folder}")

def main():
    # 文件路径配置
    input_file = "企业历史日级别价格数据_end20250904.csv"
    output_folder = "企业历史日级别价格数据_end20250904"
    
    print("开始按证券代码分割股票历史数据...")
    print(f"输入文件: {input_file}")
    print(f"输出文件夹: {output_folder}")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行分割
    split_stock_data_by_code(input_file, output_folder)
    
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
