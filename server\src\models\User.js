const mongoose = require('mongoose');

const UserSchema = new mongoose.Schema(
  {
    // 基本信息
    nickname: {
      type: String,
      required: [true, '请输入昵称'],
      trim: true,
      maxlength: [20, '昵称不能超过20个字符']
    },
    phone: {
      type: String,
      unique: true,
      sparse: true,
      validate: {
        validator: function(v) {
          // 如果是微信用户且没有手机号，允许为空
          if (this.openId && (v === null || v === undefined || v === '')) {
            return true;
          }
          // 否则验证手机号格式
          return v && /^1[3-9]\d{9}$/.test(v);
        },
        message: '请输入有效的手机号'
      },
      index: true
    },
    avatar: {
      type: String,
      default: '/assets/images/default-avatar.png'
    },
    
    // 用户资料
    gender: {
      type: String,
      enum: ['male', 'female', 'secret'],
      default: 'secret'
    },
    age: {
      type: Number,
      min: [0, '年龄不能为负数'],
      max: [120, '年龄不合理'],
      default: 18
    },
    occupation: {
      type: String,
      default: ''
    },
    region: {
      type: String,
      default: ''
    },
    
    // 认证与安全
    refreshToken: {
      type: String,
      select: false
    },
    isActive: {
      type: Boolean,
      default: true,
      index: true
    },
    
    // 管理员权限
    isAdmin: {
      type: Boolean,
      default: false,
      index: true
    },
    
    // 微信小程序信息（预留）
    openId: {
      type: String,
      index: true
    },
    unionId: {
      type: String,
      index: true
    },
    
    // 最后登录时间
    lastLoginAt: {
      type: Date,
      default: Date.now
    }
  },
  {
    timestamps: true
  }
);

// 索引设置
// 移除单独的 createdAt 索引，timestamps: true 已自动创建

// 更新用户位置信息
UserSchema.methods.updateLocation = async function(longitude, latitude) {
  this.location = {
    type: 'Point',
    coordinates: [longitude, latitude],
    lastUpdated: new Date()
  };
  return this.save();
};

module.exports = mongoose.model('User', UserSchema);
