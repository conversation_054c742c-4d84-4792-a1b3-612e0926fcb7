/**
 * API服务模块
 */
const { post, get, del, patch, put, upload } = require('../utils/request');

/**
 * 认证相关API
 */
const auth = {

  /**
   * 微信小程序登录
   * @param {string} code 微信临时登录凭证
   * @returns {Promise} Promise对象
   */
  wxLogin: (code) => {
    return post('/auth/wx-login', { code });
  },

  /**
   * 退出登录
   * @returns {Promise} Promise对象
   */
  logout: () => {
    return post('/auth/logout', {}, true);
  },

  /**
   * 获取当前用户信息
   * @returns {Promise} Promise对象
   */
  getCurrentUser: () => {
    return get('/auth/me', {}, true);
  }
};

module.exports = auth;