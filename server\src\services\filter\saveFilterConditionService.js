const SavedFilterCondition = require('../../models/SavedFilterCondition');

/**
 * 保存筛选条件
 * @param {string} userId 用户ID
 * @param {string} name 筛选条件名称
 * @param {Object} filterConditions 筛选条件
 * @returns {Promise<Object>} 保存的筛选条件信息
 */
const saveFilterCondition = async (userId, name, filterConditions) => {
  try {
    // 检查用户是否已经有同名的筛选条件
    const existingCondition = await SavedFilterCondition.findOne({ userId, name });
    if (existingCondition) {
      throw new Error('该筛选条件名称已存在，请使用其他名称');
    }

    // 创建新的筛选条件
    const savedCondition = new SavedFilterCondition({
      userId,
      name,
      filterConditions
    });

    await savedCondition.save();

    return {
      id: savedCondition._id,
      name: savedCondition.name,
      createdAt: savedCondition.createdAt,
      updatedAt: savedCondition.updatedAt
    };
  } catch (error) {
    if (error.code === 11000) {
      throw new Error('该筛选条件名称已存在，请使用其他名称');
    }
    throw new Error(`保存筛选条件失败: ${error.message}`);
  }
};

/**
 * 获取用户所有保存的筛选条件列表（包含详情）
 * @param {string} userId 用户ID
 * @returns {Promise<Array>} 筛选条件列表
 */
const getUserFilterConditions = async (userId) => {
  try {
    const conditions = await SavedFilterCondition.find({ userId }).sort({ updatedAt: -1 });
    
    return conditions.map(condition => ({
      id: condition._id,
      name: condition.name,
      filterConditions: condition.filterConditions,
      createdAt: condition.createdAt,
      updatedAt: condition.updatedAt
    }));
  } catch (error) {
    throw new Error(`获取筛选条件列表失败: ${error.message}`);
  }
};

/**
 * 删除筛选条件
 * @param {string} conditionId 筛选条件ID
 * @param {string} userId 用户ID
 * @returns {Promise<Object>} 删除结果
 */
const deleteFilterCondition = async (conditionId, userId) => {
  try {
    const condition = await SavedFilterCondition.findOne({ _id: conditionId, userId });
    
    if (!condition) {
      throw new Error('筛选条件不存在或无权限访问');
    }

    await SavedFilterCondition.deleteOne({ _id: conditionId, userId });

    return {
      id: conditionId,
      name: condition.name,
      message: '筛选条件删除成功'
    };
  } catch (error) {
    throw new Error(`删除筛选条件失败: ${error.message}`);
  }
};

/**
 * 重命名筛选条件
 * @param {string} conditionId 筛选条件ID
 * @param {string} userId 用户ID
 * @param {string} newName 新名称
 * @returns {Promise<Object>} 重命名结果
 */
const renameFilterCondition = async (conditionId, userId, newName) => {
  try {
    // 检查新名称是否已存在
    const existingCondition = await SavedFilterCondition.findOne({ 
      userId, 
      name: newName, 
      _id: { $ne: conditionId } 
    });
    
    if (existingCondition) {
      throw new Error('该筛选条件名称已存在，请使用其他名称');
    }

    const condition = await SavedFilterCondition.findOne({ _id: conditionId, userId });
    
    if (!condition) {
      throw new Error('筛选条件不存在或无权限访问');
    }

    condition.name = newName;
    condition.updatedAt = new Date();
    await condition.save();

    return {
      id: condition._id,
      name: condition.name,
      updatedAt: condition.updatedAt
    };
  } catch (error) {
    throw new Error(`重命名筛选条件失败: ${error.message}`);
  }
};

module.exports = {
  saveFilterCondition,
  getUserFilterConditions,
  deleteFilterCondition,
  renameFilterCondition
};
