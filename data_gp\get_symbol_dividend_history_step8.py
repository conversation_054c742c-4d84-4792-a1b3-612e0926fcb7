"""
沪深上市公司分红配股数据采集脚本

功能描述:
1. 读取"沪深上市公司列表.csv"文件中的证券代码
2. 使用akshare API获取每个公司的分红配股详细数据
3. 将所有数据合并并分批保存到CSV文件中
4. 记录失败的股票代码到单独的文件中
5. 支持断点续传，避免因意外中断造成数据丢失
6. 失败重试机制：首次失败重试3次，全部完成后对失败股票再次重试

输入文件: 沪深上市公司列表.csv
输出文件: 分红配股数据汇总.csv, 失败记录.txt
"""

import pandas as pd
import akshare as ak
import time
import os
from datetime import datetime

def load_stock_list(csv_file):
    """加载股票列表，确保证券代码格式正确"""
    df = pd.read_csv(csv_file, dtype={'证券代码': str})  # 指定证券代码为字符串类型
    return df['证券代码'].tolist()

def get_stock_fhps_data(symbol, retry_count=3, delay=1.0):
    """获取单个股票的分红配股数据，支持重试机制"""
    for attempt in range(retry_count):
        try:
            data = ak.stock_fhps_detail_em(symbol=symbol)
            if not data.empty:
                # 在最前面添加证券代码列
                data.insert(0, '证券代码', symbol)
            else:
                # 即使数据为空，也创建一个包含证券代码的DataFrame
                data = pd.DataFrame({'证券代码': [symbol]})
            return data, True  # 返回数据和成功标志
        except Exception as e:
            if attempt < retry_count - 1:
                print(f"获取股票 {symbol} 数据失败 (第{attempt+1}次尝试): {str(e)}, {delay}秒后重试...")
                time.sleep(delay)
            else:
                print(f"获取股票 {symbol} 数据失败 (已重试{retry_count}次): {str(e)}")
    
    return None, False  # 返回None和失败标志

def save_data_batch(all_data, output_file):
    """批量保存数据到CSV文件"""
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        
        # 确保证券代码列在最前面
        if '证券代码' in combined_df.columns:
            cols = ['证券代码'] + [col for col in combined_df.columns if col != '证券代码']
            combined_df = combined_df[cols]
        
        combined_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"已保存 {len(all_data)} 个股票的数据到 {output_file}")
        print(f"数据总行数: {len(combined_df)}")

def save_failed_records(failed_stocks, failed_file):
    """保存失败记录"""
    if failed_stocks:
        with open(failed_file, 'w', encoding='utf-8') as f:
            f.write(f"数据采集失败记录 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 50 + "\n")
            for stock in failed_stocks:
                f.write(f"{stock}\n")
        print(f"已保存 {len(failed_stocks)} 个失败记录到 {failed_file}")

def retry_failed_stocks(failed_stocks, all_data, output_file):
    """对失败的股票进行二次重试"""
    if not failed_stocks:
        return []
    
    print(f"\n开始对 {len(failed_stocks)} 个失败的股票进行二次重试...")
    print("等待1分钟后开始重试...")
    time.sleep(60)  # 等待1分钟
    
    still_failed = []
    retry_success_count = 0
    
    for i, stock_code in enumerate(failed_stocks, 1):
        print(f"重试 ({i}/{len(failed_stocks)}): {stock_code}")
        
        # 使用更长的延时进行重试
        data, success = get_stock_fhps_data(stock_code, retry_count=3, delay=3.0)
        
        if success and data is not None:
            # 确保数据包含证券代码
            if '证券代码' not in data.columns:
                data.insert(0, '证券代码', stock_code)
            
            all_data.append(data)
            retry_success_count += 1
            print(f"重试成功: {stock_code}")
        else:
            still_failed.append(stock_code)
            print(f"重试仍失败: {stock_code}")
        
        # 重试时使用更长的间隔
        time.sleep(2.0)
    
    # 如果有重试成功的，保存数据
    if retry_success_count > 0:
        save_data_batch(all_data, output_file)
        print(f"重试成功 {retry_success_count} 个股票")
    
    return still_failed

def main():
    # 配置参数
    input_file = "沪深上市公司列表.csv"
    output_file = "分红配股数据汇总.csv"
    failed_file = "失败记录.txt"
    batch_size = 50  # 每50个股票保存一次
    delay = 0.5  # API调用间隔，避免请求过于频繁
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 找不到文件 {input_file}")
        return
    
    # 加载股票列表
    print("正在加载股票列表...")
    stock_codes = load_stock_list(input_file)
    print(f"共找到 {len(stock_codes)} 只股票")
    
    # 初始化变量
    all_data = []
    failed_stocks = []
    processed_count = 0
    
    # 如果输出文件已存在，询问是否继续
    if os.path.exists(output_file):
        response = input(f"输出文件 {output_file} 已存在，是否覆盖？(y/n): ")
        if response.lower() != 'y':
            print("操作已取消")
            return
    
    print("开始采集数据...")
    start_time = datetime.now()
    
    # 第一轮：遍历所有股票代码
    print("=" * 60)
    print("第一轮数据采集开始...")
    print("=" * 60)
    
    for i, stock_code in enumerate(stock_codes, 1):
        print(f"正在处理 ({i}/{len(stock_codes)}): {stock_code}")
        
        # 获取数据，首次失败时重试3次
        data, success = get_stock_fhps_data(stock_code, retry_count=3, delay=1.0)
        
        if success and data is not None:
            # 确保数据包含证券代码
            if '证券代码' not in data.columns:
                data.insert(0, '证券代码', stock_code)
            
            all_data.append(data)
            processed_count += 1
            print(f"成功获取 {stock_code} 的数据，共 {len(data)} 条记录")
        else:
            failed_stocks.append(stock_code)
        
        # 批量保存
        if i % batch_size == 0 or i == len(stock_codes):
            save_data_batch(all_data, output_file)
            print(f"进度: {i}/{len(stock_codes)} ({i/len(stock_codes)*100:.1f}%)")
            print(f"成功: {processed_count}, 失败: {len(failed_stocks)}")
            print("-" * 50)
        
        # 延时，避免请求过于频繁
        time.sleep(delay)
    
    print("第一轮采集完成!")
    print(f"成功: {processed_count}, 失败: {len(failed_stocks)}")
    
    # 第二轮：对失败的股票进行重试
    if failed_stocks:
        print("\n" + "=" * 60)
        print("第二轮重试开始...")
        print("=" * 60)
        
        still_failed = retry_failed_stocks(failed_stocks, all_data, output_file)
        
        # 计算重试成功的数量
        retry_success_count = len(failed_stocks) - len(still_failed)
        processed_count += retry_success_count
        
        print("第二轮重试完成!")
        print(f"重试成功: {retry_success_count}, 仍然失败: {len(still_failed)}")
        
        # 更新失败列表
        failed_stocks = still_failed
    
    # 保存最终的失败记录
    save_failed_records(failed_stocks, failed_file)
    
    # 输出统计信息
    end_time = datetime.now()
    duration = end_time - start_time
    
    print("\n" + "=" * 60)
    print("数据采集完成!")
    print(f"总耗时: {duration}")
    print(f"成功采集: {processed_count} 只股票")
    print(f"最终失败: {len(failed_stocks)} 只股票")
    print(f"最终成功率: {processed_count/(processed_count+len(failed_stocks))*100:.1f}%")
    print(f"输出文件: {output_file}")
    if failed_stocks:
        print(f"失败记录: {failed_file}")
    print("=" * 60)
    
    # 验证输出文件
    if os.path.exists(output_file):
        try:
            verify_df = pd.read_csv(output_file)
            print(f"\n文件验证:")
            print(f"总行数: {len(verify_df)}")
            print(f"列名: {list(verify_df.columns)}")
            if '证券代码' in verify_df.columns:
                print(f"证券代码列包含 {verify_df['证券代码'].nunique()} 个不同的股票代码")
                print(f"证券代码示例: {verify_df['证券代码'].head().tolist()}")
            else:
                print("警告: 输出文件中未找到证券代码列!")
        except Exception as e:
            print(f"验证输出文件时出错: {e}")

if __name__ == "__main__":
    main()
