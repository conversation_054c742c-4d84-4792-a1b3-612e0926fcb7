import pandas as pd
import akshare as ak
import time
import os
from typing import Set, List
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StockDataCollector:
    def __init__(self, input_csv="沪深上市公司列表.csv", output_csv="", 
                 progress_file="progress.txt", batch_size=10):
        self.input_csv = input_csv
        self.output_csv = output_csv
        self.progress_file = progress_file
        self.batch_size = batch_size
        self.all_columns = set()  # 存储所有可能的列名
        self.failed_stocks = []  # 存储失败的股票代码
        
    def load_stock_list(self) -> pd.DataFrame:
        """加载股票列表"""
        try:
            # 确保证券代码作为字符串读取，避免000001变成1
            df = pd.read_csv(self.input_csv, dtype={'证券代码': str})
            logger.info(f"成功加载股票列表，共{len(df)}只股票")
            return df
        except Exception as e:
            logger.error(f"加载股票列表失败: {e}")
            raise
    
    def load_progress(self) -> Set[str]:
        """加载已处理的股票代码"""
        processed = set()
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    processed = set(line.strip() for line in f if line.strip())
                logger.info(f"加载进度文件，已处理{len(processed)}只股票")
            except Exception as e:
                logger.error(f"加载进度文件失败: {e}")
        return processed
    
    def save_progress(self, stock_code: str):
        """保存进度"""
        try:
            with open(self.progress_file, 'a', encoding='utf-8') as f:
                f.write(f"{stock_code}\n")
        except Exception as e:
            logger.error(f"保存进度失败: {e}")
    
    def get_stock_data(self, stock_code: str, max_retries=3) -> pd.DataFrame:
        """获取单只股票的财务数据"""
        for attempt in range(max_retries):
            try:
                logger.info(f"正在获取股票 {stock_code} 的数据 (尝试 {attempt + 1}/{max_retries})")
                df = ak.stock_financial_abstract_ths(symbol=stock_code, indicator="按年度")
                if df is not None and not df.empty:
                    # 添加股票代码列
                    df['证券代码'] = stock_code
                    # 将证券代码列移到第一列
                    cols = ['证券代码'] + [col for col in df.columns if col != '证券代码']
                    df = df[cols]
                    logger.info(f"成功获取股票 {stock_code} 的数据，共{len(df)}条记录")
                    return df
                else:
                    logger.warning(f"股票 {stock_code} 返回空数据")
            except Exception as e:
                logger.error(f"获取股票 {stock_code} 数据失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # 重试前等待2秒
        
        logger.error(f"股票 {stock_code} 获取失败，已达到最大重试次数")
        return None
    
    def update_all_columns(self, df: pd.DataFrame):
        """更新所有可能的列名"""
        if df is not None:
            self.all_columns.update(df.columns.tolist())
    
    def standardize_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化DataFrame，确保所有列都存在"""
        if df is None:
            return None
        
        # 为缺失的列添加空值
        for col in self.all_columns:
            if col not in df.columns:
                df[col] = None
        
        # 按照固定顺序排列列
        ordered_columns = ['证券代码'] + sorted([col for col in self.all_columns if col != '证券代码'])
        df = df[ordered_columns]
        
        return df
    
    def save_batch_data(self, batch_data: List[pd.DataFrame]):
        """保存批次数据"""
        if not batch_data:
            return
        
        try:
            # 合并批次数据
            combined_df = pd.concat(batch_data, ignore_index=True)
            
            # 标准化DataFrame
            combined_df = self.standardize_dataframe(combined_df)
            
            # 检查输出文件是否存在
            file_exists = os.path.exists(self.output_csv)
            
            if file_exists:
                # 读取现有数据以获取列结构
                try:
                    existing_df = pd.read_csv(self.output_csv, dtype={'证券代码': str}, nrows=0)  # 只读取列名
                    existing_columns = set(existing_df.columns.tolist())
                    
                    # 更新所有列
                    self.all_columns.update(existing_columns)
                    
                    # 重新标准化
                    combined_df = self.standardize_dataframe(combined_df)
                    
                    # 如果列结构发生变化，需要重新整理现有文件
                    if existing_columns != set(combined_df.columns):
                        logger.info("检测到新列，正在更新现有文件结构...")
                        existing_full_df = pd.read_csv(self.output_csv, dtype={'证券代码': str})
                        existing_full_df = self.standardize_dataframe(existing_full_df)
                        existing_full_df.to_csv(self.output_csv, index=False, encoding='utf-8-sig')
                
                except Exception as e:
                    logger.error(f"读取现有文件失败: {e}")
                
                # 追加数据
                combined_df.to_csv(self.output_csv, mode='a', header=False, index=False, encoding='utf-8-sig')
            else:
                # 创建新文件
                combined_df.to_csv(self.output_csv, index=False, encoding='utf-8-sig')
            
            logger.info(f"成功保存批次数据，共{len(combined_df)}条记录")
            
        except Exception as e:
            logger.error(f"保存批次数据失败: {e}")
            raise
    
    def retry_failed_stocks(self):
        """重试失败的股票"""
        if not self.failed_stocks:
            return
        
        logger.info(f"开始重试失败的股票，共{len(self.failed_stocks)}只")
        retry_failed = []
        batch_data = []
        
        for i, stock_code in enumerate(self.failed_stocks):
            logger.info(f"重试股票 {stock_code} ({i+1}/{len(self.failed_stocks)})")
            
            # 每次重试前等待1分钟
            if i > 0:
                logger.info("等待1分钟后继续...")
                time.sleep(60)
            
            df = self.get_stock_data(stock_code, max_retries=3)
            
            if df is not None:
                self.update_all_columns(df)
                batch_data.append(df)
                self.save_progress(stock_code)
                
                # 每10只股票保存一次
                if len(batch_data) >= self.batch_size:
                    self.save_batch_data(batch_data)
                    batch_data = []
            else:
                retry_failed.append(stock_code)
        
        # 保存剩余数据
        if batch_data:
            self.save_batch_data(batch_data)
        
        # 更新失败列表
        self.failed_stocks = retry_failed
        
        if self.failed_stocks:
            logger.warning(f"仍有{len(self.failed_stocks)}只股票获取失败: {self.failed_stocks}")
        else:
            logger.info("所有失败的股票都已成功重试")
    
    def collect_data(self):
        """主要的数据收集函数"""
        # 加载股票列表
        stock_df = self.load_stock_list()
        
        # 加载已处理的进度
        processed_stocks = self.load_progress()
        
        # 筛选未处理的股票
        remaining_stocks = stock_df[~stock_df['证券代码'].isin(processed_stocks)]
        
        if remaining_stocks.empty:
            logger.info("所有股票都已处理完成")
            return
        
        logger.info(f"还需处理{len(remaining_stocks)}只股票")
        
        batch_data = []
        
        # 第一遍：获取所有列名
        logger.info("第一阶段：收集所有可能的列名...")
        sample_size = min(50, len(remaining_stocks))  # 先处理50只股票来收集列名
        sample_stocks = remaining_stocks.head(sample_size)
        
        for idx, row in sample_stocks.iterrows():
            stock_code = row['证券代码']
            df = self.get_stock_data(stock_code)
            if df is not None:
                self.update_all_columns(df)
            time.sleep(1)  # 避免请求过于频繁
        
        logger.info(f"收集到{len(self.all_columns)}个不同的列名")
        
        # 第二遍：正式处理所有股票
        logger.info("第二阶段：正式处理所有股票...")
        
        for idx, row in remaining_stocks.iterrows():
            stock_code = row['证券代码']
            
            if stock_code in processed_stocks:
                continue
            
            logger.info(f"处理股票 {stock_code} ({idx+1-len(stock_df)+len(remaining_stocks)}/{len(remaining_stocks)})")
            
            df = self.get_stock_data(stock_code)
            
            if df is not None:
                self.update_all_columns(df)
                batch_data.append(df)
                self.save_progress(stock_code)
            else:
                self.failed_stocks.append(stock_code)
            
            # 批量保存
            if len(batch_data) >= self.batch_size:
                self.save_batch_data(batch_data)
                batch_data = []
            
            # 避免请求过于频繁
            time.sleep(1)
        
        # 保存剩余数据
        if batch_data:
            self.save_batch_data(batch_data)
        
        # 重试失败的股票
        if self.failed_stocks:
            logger.info(f"第一轮完成，开始重试失败的{len(self.failed_stocks)}只股票")
            self.retry_failed_stocks()
        
        # 最终报告
        if self.failed_stocks:
            logger.warning(f"数据收集完成，但仍有{len(self.failed_stocks)}只股票失败: {self.failed_stocks}")
            # 将失败的股票保存到文件
            with open("failed_stocks.txt", "w", encoding='utf-8') as f:
                for stock in self.failed_stocks:
                    f.write(f"{stock}\n")
        else:
            logger.info("所有股票数据收集完成！")

def main():
    collector = StockDataCollector(
        input_csv="沪深上市公司列表.csv",
        output_csv="企业关键指标年度_修正后.csv",
        progress_file="progress.txt",
        batch_size=10
    )
    
    try:
        collector.collect_data()
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        raise

if __name__ == "__main__":
    main()
