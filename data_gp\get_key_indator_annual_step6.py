import pandas as pd
import akshare as ak
import time
import os
from typing import Set, List
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StockDataCollector:
    def __init__(self, input_csv="沪深上市公司列表.csv", output_dir="企业关键指标报告期_修正后", 
                 progress_file="progress.txt", batch_size=10, file_size=500):
        self.input_csv = input_csv
        self.output_dir = output_dir
        self.progress_file = progress_file
        self.batch_size = batch_size
        self.file_size = file_size  # 每个文件包含的企业数量
        self.all_columns = set()  # 存储所有可能的列名
        self.failed_stocks = []  # 存储失败的股票代码
        self.current_file_data = []  # 当前文件的数据
        self.current_file_count = 0  # 当前文件中的企业数量
        self.file_counter = 1  # 文件编号
        
        # 创建输出目录
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            logger.info(f"创建输出目录: {self.output_dir}")
        
    def load_stock_list(self) -> pd.DataFrame:
        """加载股票列表"""
        try:
            # 确保证券代码作为字符串读取，避免000001变成1
            df = pd.read_csv(self.input_csv, dtype={'证券代码': str})
            logger.info(f"成功加载股票列表，共{len(df)}只股票")
            return df
        except Exception as e:
            logger.error(f"加载股票列表失败: {e}")
            raise
    
    def load_progress(self) -> Set[str]:
        """加载已处理的股票代码"""
        processed = set()
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    processed = set(line.strip() for line in f if line.strip())
                logger.info(f"加载进度文件，已处理{len(processed)}只股票")
            except Exception as e:
                logger.error(f"加载进度文件失败: {e}")
        return processed
    
    def save_progress(self, stock_code: str):
        """保存进度"""
        try:
            with open(self.progress_file, 'a', encoding='utf-8') as f:
                f.write(f"{stock_code}\n")
        except Exception as e:
            logger.error(f"保存进度失败: {e}")
    
    def get_current_file_number(self) -> int:
        """获取当前应该使用的文件编号"""
        processed = self.load_progress()
        return (len(processed) // self.file_size) + 1
    
    def get_stock_data(self, stock_code: str, max_retries=3) -> pd.DataFrame:
        """获取单只股票的财务数据"""
        for attempt in range(max_retries):
            try:
                logger.info(f"正在获取股票 {stock_code} 的数据 (尝试 {attempt + 1}/{max_retries})")
                df = ak.stock_financial_abstract_ths(symbol=stock_code, indicator="按报告期")
                if df is not None and not df.empty:
                    # 添加股票代码列
                    df['证券代码'] = stock_code
                    # 将证券代码列移到第一列
                    cols = ['证券代码'] + [col for col in df.columns if col != '证券代码']
                    df = df[cols]
                    logger.info(f"成功获取股票 {stock_code} 的数据，共{len(df)}条记录")
                    return df
                else:
                    logger.warning(f"股票 {stock_code} 返回空数据")
            except Exception as e:
                logger.error(f"获取股票 {stock_code} 数据失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # 重试前等待2秒
        
        logger.error(f"股票 {stock_code} 获取失败，已达到最大重试次数")
        return None
    
    def update_all_columns(self, df: pd.DataFrame):
        """更新所有可能的列名"""
        if df is not None:
            self.all_columns.update(df.columns.tolist())
    
    def standardize_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化DataFrame，确保所有列都存在"""
        if df is None:
            return None
        
        # 为缺失的列添加空值
        for col in self.all_columns:
            if col not in df.columns:
                df[col] = None
        
        # 按照固定顺序排列列
        ordered_columns = ['证券代码'] + sorted([col for col in self.all_columns if col != '证券代码'])
        df = df[ordered_columns]
        
        return df
    
    def save_current_file(self):
        """保存当前文件的数据"""
        if not self.current_file_data:
            return
        
        try:
            # 合并当前文件的所有数据
            combined_df = pd.concat(self.current_file_data, ignore_index=True)
            
            # 标准化DataFrame
            combined_df = self.standardize_dataframe(combined_df)
            
            # 生成文件名
            filename = f"企业关键指标报告期_第{self.file_counter}批次_{self.file_size}家企业.csv"
            filepath = os.path.join(self.output_dir, filename)
            
            # 保存文件
            combined_df.to_csv(filepath, index=False, encoding='utf-8-sig')
            
            logger.info(f"成功保存文件: {filename}，包含{len(combined_df)}条记录，来自{self.current_file_count}家企业")
            
            # 重置当前文件数据
            self.current_file_data = []
            self.current_file_count = 0
            self.file_counter += 1
            
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            raise
    
    def add_stock_data(self, df: pd.DataFrame):
        """添加股票数据到当前文件"""
        if df is not None:
            self.current_file_data.append(df)
            self.current_file_count += 1
            
            # 如果达到文件大小限制，保存文件
            if self.current_file_count >= self.file_size:
                self.save_current_file()
    
    def save_batch_data(self, batch_data: List[pd.DataFrame]):
        """保存批次数据（现在是添加到当前文件）"""
        if not batch_data:
            return
        
        try:
            for df in batch_data:
                self.add_stock_data(df)
            
            logger.info(f"成功处理批次数据，当前文件已有{self.current_file_count}家企业")
            
        except Exception as e:
            logger.error(f"处理批次数据失败: {e}")
            raise
    
    def retry_failed_stocks(self):
        """重试失败的股票"""
        if not self.failed_stocks:
            return
        
        logger.info(f"开始重试失败的股票，共{len(self.failed_stocks)}只")
        retry_failed = []
        batch_data = []
        
        for i, stock_code in enumerate(self.failed_stocks):
            logger.info(f"重试股票 {stock_code} ({i+1}/{len(self.failed_stocks)})")
            
            # 每次重试前等待1分钟
            if i > 0:
                logger.info("等待1分钟后继续...")
                time.sleep(60)
            
            df = self.get_stock_data(stock_code, max_retries=3)
            
            if df is not None:
                self.update_all_columns(df)
                batch_data.append(df)
                self.save_progress(stock_code)
                
                # 每10只股票保存一次
                if len(batch_data) >= self.batch_size:
                    self.save_batch_data(batch_data)
                    batch_data = []
            else:
                retry_failed.append(stock_code)
        
        # 保存剩余数据
        if batch_data:
            self.save_batch_data(batch_data)
        
        # 更新失败列表
        self.failed_stocks = retry_failed
        
        if self.failed_stocks:
            logger.warning(f"仍有{len(self.failed_stocks)}只股票获取失败: {self.failed_stocks}")
        else:
            logger.info("所有失败的股票都已成功重试")
    
    def collect_data(self):
        """主要的数据收集函数"""
        # 加载股票列表
        stock_df = self.load_stock_list()
        
        # 加载已处理的进度
        processed_stocks = self.load_progress()
        
        # 设置当前文件编号
        self.file_counter = self.get_current_file_number()
        
        # 计算当前文件应该有多少企业
        processed_count = len(processed_stocks)
        self.current_file_count = processed_count % self.file_size
        
        logger.info(f"当前处理进度: 已处理{processed_count}家企业，当前文件编号: {self.file_counter}，当前文件已有{self.current_file_count}家企业")
        
        # 筛选未处理的股票
        remaining_stocks = stock_df[~stock_df['证券代码'].isin(processed_stocks)]
        
        if remaining_stocks.empty:
            logger.info("所有股票都已处理完成")
            # 保存最后一个文件（如果有数据的话）
            if self.current_file_count > 0:
                logger.info("保存最后一个文件...")
                # 这里需要重新加载当前文件的数据
                self.load_current_file_data(processed_stocks)
                self.save_current_file()
            return
        
        logger.info(f"还需处理{len(remaining_stocks)}只股票")
        
        # 如果有未完成的文件，先加载其数据
        if self.current_file_count > 0:
            self.load_current_file_data(processed_stocks)
        
        batch_data = []
        
        # 第一遍：获取所有列名（如果还没有的话）
        if not self.all_columns:
            logger.info("第一阶段：收集所有可能的列名...")
            sample_size = min(50, len(remaining_stocks))  # 先处理50只股票来收集列名
            sample_stocks = remaining_stocks.head(sample_size)
            
            for idx, row in sample_stocks.iterrows():
                stock_code = row['证券代码']
                df = self.get_stock_data(stock_code)
                if df is not None:
                    self.update_all_columns(df)
                time.sleep(1)  # 避免请求过于频繁
            
            logger.info(f"收集到{len(self.all_columns)}个不同的列名")
        
        # 第二遍：正式处理所有股票
        logger.info("开始处理所有股票...")
        
        for idx, row in remaining_stocks.iterrows():
            stock_code = row['证券代码']
            
            if stock_code in processed_stocks:
                continue
            
            current_progress = len(processed_stocks) + len([s for s in remaining_stocks['证券代码'][:idx-remaining_stocks.index[0]] if s not in processed_stocks])
            logger.info(f"处理股票 {stock_code} (总进度: {current_progress+1}/{len(stock_df)})")
            
            df = self.get_stock_data(stock_code)
            
            if df is not None:
                self.update_all_columns(df)
                batch_data.append(df)
                self.save_progress(stock_code)
            else:
                self.failed_stocks.append(stock_code)
            
            # 批量保存
            if len(batch_data) >= self.batch_size:
                self.save_batch_data(batch_data)
                batch_data = []
            
            # 避免请求过于频繁
            time.sleep(1)
        
        # 保存剩余数据
        if batch_data:
            self.save_batch_data(batch_data)
        
        # 保存最后一个文件（如果有数据的话）
        if self.current_file_count > 0:
            self.save_current_file()
        
        # 重试失败的股票
        if self.failed_stocks:
            logger.info(f"第一轮完成，开始重试失败的{len(self.failed_stocks)}只股票")
            self.retry_failed_stocks()
            
            # 保存重试后的最后一个文件
            if self.current_file_count > 0:
                self.save_current_file()
        
        # 最终报告
        if self.failed_stocks:
            logger.warning(f"数据收集完成，但仍有{len(self.failed_stocks)}只股票失败: {self.failed_stocks}")
            # 将失败的股票保存到文件
            with open("failed_stocks.txt", "w", encoding='utf-8') as f:
                for stock in self.failed_stocks:
                    f.write(f"{stock}\n")
        else:
            logger.info("所有股票数据收集完成！")
        
        # 显示文件统计
        self.show_file_statistics()
    
    def load_current_file_data(self, processed_stocks: Set[str]):
        """加载当前文件已有的数据"""
        try:
            # 计算当前文件应该包含哪些股票
            start_index = (self.file_counter - 1) * self.file_size
            end_index = start_index + self.current_file_count

            # 从已处理的股票中获取当前文件的股票
            processed_list = list(processed_stocks)
            current_file_stocks = processed_list[start_index:end_index]
            
            logger.info(f"正在加载当前文件已有的{len(current_file_stocks)}家企业数据...")
            
            # 重新获取这些股票的数据
            for stock_code in current_file_stocks:
                df = self.get_stock_data(stock_code)
                if df is not None:
                    self.current_file_data.append(df)
                    self.update_all_columns(df)
                time.sleep(0.5)  # 稍微快一点，因为是重新加载
            
            logger.info(f"成功加载当前文件的{len(self.current_file_data)}家企业数据")
            
        except Exception as e:
            logger.error(f"加载当前文件数据失败: {e}")

    def show_file_statistics(self):
        """显示文件统计信息"""
        try:
            files = [f for f in os.listdir(self.output_dir) if f.endswith('.csv')]
            files.sort()
            
            logger.info(f"\n=== 文件统计 ===")
            logger.info(f"输出目录: {self.output_dir}")
            logger.info(f"生成文件数量: {len(files)}")
            
            total_records = 0
            for file in files:
                filepath = os.path.join(self.output_dir, file)
                try:
                    df = pd.read_csv(filepath)
                    unique_companies = df['证券代码'].nunique() if '证券代码' in df.columns else 0
                    total_records += len(df)
                    logger.info(f"  {file}: {len(df)}条记录, {unique_companies}家企业")
                except Exception as e:
                    logger.error(f"  {file}: 读取失败 - {e}")
            
            logger.info(f"总记录数: {total_records}")
            logger.info(f"=== 统计完成 ===\n")
            
        except Exception as e:
            logger.error(f"显示文件统计失败: {e}")


def main():
    """主函数"""
    collector = StockDataCollector(
    input_csv="沪深上市公司列表.csv",
    output_dir="企业关键指标报告期",
    progress_file="progress.txt",
    batch_size=10, # 每10只股票保存一次进度
    file_size=500 # 每500家企业一个文件
    )

    try:
        collector.collect_data()
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        raise
if __name__ == "__main__":
    main()