/* 言策 AI量化投资小程序 - 全局样式文件 */
/* ================================================ */

/* ==================== 样式文件引入 ==================== */
@import "styles/variables.wxss";
@import "styles/base.wxss";
@import "styles/mixins.wxss";

/* ==================== 页面级通用样式 ==================== */

/* page 根元素样式 */
page {
  /* 基础设置 */
  height: 100vh;
  width: 100vw;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'SF Pro Text', 'SF Pro Display', SimSun, sans-serif;
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);

  /* 字体渲染优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;

  /* 性能优化 */
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;

  /* 禁用用户选择和缩放 */
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* 全局字体设置 */
.font-system {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
}

.font-mono {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-variant-numeric: tabular-nums;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8rpx;
  height: 8rpx;
}

::-webkit-scrollbar-track {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background-color: var(--border-medium);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--primary-light);
}

/* ==================== 全局组件样式 ==================== */

/* 导航栏样式 */
.navigation-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background: var(--gradient-primary);
  color: var(--text-inverse);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
  z-index: var(--z-index-fixed);
  box-shadow: var(--shadow-base);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
}

.navigation-bar.transparent {
  background: rgba(27, 79, 114, 0.9);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
}

.navigation-bar .back-button {
  position: absolute;
  left: var(--spacing-lg);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-round);
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--text-inverse);
  transition: var(--transition-fast);
}

.navigation-bar .back-button:active {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

/* TabBar 相关样式 */
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: var(--bg-card);
  border-top: 1rpx solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-around;
  z-index: var(--z-index-fixed);
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
}

.custom-tabbar .tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) 0;
  transition: var(--transition-fast);
}

.custom-tabbar .tab-item.active {
  color: var(--primary-color);
}

.custom-tabbar .tab-item .icon {
  font-size: 44rpx;
  margin-bottom: var(--spacing-xs);
  transition: var(--transition-fast);
}

.custom-tabbar .tab-item.active .icon {
  transform: scale(1.1);
}

.custom-tabbar .tab-item .text {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  transition: var(--transition-fast);
}

.custom-tabbar .tab-item.active .text {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}