/**
 * 获取支持的筛选条件列表
 * @returns {Object} 支持的筛选条件
 */
const getSupportedConditions = () => {
  return {
    dailyConditions: [
      'peRatioDynamicRange',
      'peRatioStaticRange', 
      'peRatioTTMRange',
      'pbRatioRange',
      'totalMarketCapRange',
      'circulatingMarketCapRange',
      'isLimitUp',
      'isLimitDown',
      'touchedLimitUp',
      'touchedLimitDown',
      'turnoverRateRange',
      'changePercentRange',
      'amplitudeRange',
      'ytdGainRange',
      'sixtyDayChangeRange'
    ],
    financialConditions: [
      'netProfitGrowthRateRange',
      'nonRecurringNetProfitGrowthRateRange',
      'totalRevenueGrowthRateRange',
      'netProfitMarginRange',
      'grossProfitMarginRange',
      'returnOnEquityRange',
      'dilutedReturnOnEquityRange',
      'currentRatioRange',
      'quickRatioRange',
      'equityRatioRange',
      'debtToAssetRatioRange',
      'inventoryTurnoverRatioRange',
      'inventoryTurnoverDaysRange',
      'accountsReceivableTurnoverDaysRange'
    ]
  };
};

module.exports = { getSupportedConditions };
