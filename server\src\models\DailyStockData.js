const mongoose = require('mongoose');

// 企业当日股票数据模型 - 用于存储当日实时交易数据
const dailyStockDataSchema = new mongoose.Schema({
  // === 基本信息 ===
  // 证券代码
  stockCode: {
    type: String,
    required: true,
    trim: true,
    // 移除这里的 index: true 和 unique: true
  },
  
  // 时间戳
  timestamp: {
    type: Date,
    required: true,
    // 移除这里的 index: true
  },
  
  
  // === 价格信息 ===
  // 收盘价
  closePrice: {
    type: Number,
    default: null
  },
  
  // 涨跌额
  priceChange: {
    type: Number,
    default: null
  },
  
  // 涨跌幅(%)
  changePercent: {
    type: Number,
    default: null
  },
  
  // 昨收
  previousClose: {
    type: Number,
    default: null
  },
  
  // 今开
  openPrice: {
    type: Number,
    default: null
  },
  
  // 最高
  highPrice: {
    type: Number,
    default: null
  },
  
  // 最低
  lowPrice: {
    type: Number,
    default: null
  },
  
  
  // === 交易信息 ===
  // 成交量
  volume: {
    type: Number,
    default: null
  },
  
  // 成交额
  turnover: {
    type: Number,
    default: null
  },
  
  // 换手率(%)
  turnoverRate: {
    type: Number,
    default: null
  },
  
  // 振幅(%)
  amplitude: {
    type: Number,
    default: null
  },
  
  // 涨停价
  limitUpPrice: {
    type: Number,
    default: null
  },
  
  // 跌停价
  limitDownPrice: {
    type: Number,
    default: null
  },
  
  // 均价
  averagePrice: {
    type: Number,
    default: null
  },
  
  
  // === 52周信息 ===
  // 52周最高
  week52High: {
    type: Number,
    default: null
  },
  
  // 52周最低
  week52Low: {
    type: Number,
    default: null
  },
  
  // 今年以来涨幅(%)
  ytdGain: {
    type: Number,
    default: null
  },
  
  
  // === 估值指标 ===
  // 市盈率(动)
  peRatioDynamic: {
    type: Number,
    default: null
  },
  
  // 市盈率(静)
  peRatioStatic: {
    type: Number,
    default: null
  },
  
  // 市盈率(TTM)
  peRatioTTM: {
    type: Number,
    default: null
  },
  
  // 市净率
  pbRatio: {
    type: Number,
    default: null
  },
  
  
  // === 每股指标 ===
  // 每股收益
  earningsPerShare: {
    type: Number,
    default: null
  },
  
  // 每股净资产
  bookValuePerShare: {
    type: Number,
    default: null
  },
  
  // 股息(TTM)
  dividendTTM: {
    type: Number,
    default: null
  },
  
  // 股息率(TTM)(%)
  dividendYieldTTM: {
    type: Number,
    default: null
  },
  
  
  // === 市值信息 ===
  // 总市值
  totalMarketCap: {
    type: Number,
    default: null
  },
  
  // 流通市值
  circulatingMarketCap: {
    type: Number,
    default: null
  },
  
  
  // === 技术指标 ===
  // 量比
  volumeRatio: {
    type: Number,
    default: null
  },
  
  // 涨速(%)
  priceVelocity: {
    type: Number,
    default: null
  },
  
  // 5分钟涨跌(%)
  fiveMinuteChange: {
    type: Number,
    default: null
  },
  
  // 60日涨跌幅(%)
  sixtyDayChange: {
    type: Number,
    default: null
  },
  
  
  // 创建时间
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  // 更新时间
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  // 自动添加 createdAt 和 updatedAt
  timestamps: true,
  
  // 设置集合名称
  collection: 'daily_stock_data'
});

// === 索引定义 ===
// 统一在这里定义所有索引，避免重复

// 唯一索引
dailyStockDataSchema.index({ stockCode: 1 }, { unique: true }); // 证券代码唯一性

// 复合索引
dailyStockDataSchema.index({ stockCode: 1, timestamp: -1 }); // 按股票代码和时间排序

// 普通索引
dailyStockDataSchema.index({ timestamp: -1 }); // 按时间排序
dailyStockDataSchema.index({ closePrice: 1 }); // 按价格查询
dailyStockDataSchema.index({ volume: -1 }); // 按成交量排序
dailyStockDataSchema.index({ turnover: -1 }); // 按成交额排序
dailyStockDataSchema.index({ totalMarketCap: -1 }); // 按总市值排序

// === 字段映射 ===

// 获取中英文字段映射
dailyStockDataSchema.statics.getFieldMapping = function() {
  return {
    '证券代码': 'stockCode',
    '时间': 'timestamp',
    '收盘': 'closePrice',
    '涨跌额': 'priceChange',
    '涨跌幅(%)': 'changePercent',
    '昨收': 'previousClose',
    '开盘': 'openPrice',
    '最高': 'highPrice',
    '最低': 'lowPrice',
    '成交量': 'volume',
    '成交额': 'turnover',
    '换手率(%)': 'turnoverRate',
    '振幅(%)': 'amplitude',
    '涨停价': 'limitUpPrice',
    '跌停价': 'limitDownPrice',
    '均价': 'averagePrice',
    '52周最高': 'week52High',
    '52周最低': 'week52Low',
    '今年以来涨幅(%)': 'ytdGain',
    '市盈率(动)': 'peRatioDynamic',
    '市盈率(静)': 'peRatioStatic',
    '市盈率(TTM)': 'peRatioTTM',
    '市净率': 'pbRatio',
    '每股收益(静)': 'earningsPerShare',
    '每股净资产': 'bookValuePerShare',
    '股息(TTM)': 'dividendTTM',
    '股息率(TTM)(%)': 'dividendYieldTTM',
    '总市值(亿元)': 'totalMarketCap',
    '流通市值(亿元)': 'circulatingMarketCap',
    '量比': 'volumeRatio',
    '涨速': 'priceVelocity',
    '5分钟涨跌': 'fiveMinuteChange',
    '60日涨跌幅(%)': 'sixtyDayChange'
  };
};

// 获取字段的中文名称
dailyStockDataSchema.statics.getChineseFieldName = function(englishField) {
  const mapping = this.getFieldMapping();
  for (const [chinese, english] of Object.entries(mapping)) {
    if (english === englishField) {
      return chinese;
    }
  }
  return englishField;
};

// 获取字段的英文名称
dailyStockDataSchema.statics.getEnglishFieldName = function(chineseField) {
  const mapping = this.getFieldMapping();
  return mapping[chineseField] || chineseField;
};

// === 静态方法 ===

// 根据股票代码查找当日数据
dailyStockDataSchema.statics.findByStockCode = function(stockCode, options = {}) {
  const query = { stockCode };
  if (options.startDate && options.endDate) {
    query.timestamp = { $gte: new Date(options.startDate), $lte: new Date(options.endDate) };
  }
  
  return this.find(query).sort({ timestamp: -1 });
};

// 获取最新的股票数据
dailyStockDataSchema.statics.findLatestByStockCode = function(stockCode) {
  return this.findOne({ stockCode }).sort({ timestamp: -1 });
};

// 根据时间范围查找
dailyStockDataSchema.statics.findByDateRange = function(startDate, endDate, options = {}) {
  const query = {
    timestamp: { $gte: new Date(startDate), $lte: new Date(endDate) }
  };
  
  if (options.stockCodes && options.stockCodes.length > 0) {
    query.stockCode = { $in: options.stockCodes };
  }
  
  return this.find(query)
    .sort({ timestamp: -1, stockCode: 1 })
    .limit(options.limit || 0)
    .skip(options.skip || 0);
};

// 批量查找多个股票的最新数据
dailyStockDataSchema.statics.findLatestByStockCodes = function(stockCodes) {
  return this.aggregate([
    { $match: { stockCode: { $in: stockCodes } } },
    { $sort: { stockCode: 1, timestamp: -1 } },
    { $group: {
        _id: '$stockCode',
        latestData: { $first: '$$ROOT' }
      }
    }
  ]);
};

// 查找涨幅排行榜
dailyStockDataSchema.statics.findTopGainers = function(date, limit = 10) {
  const startDate = new Date(date);
  const endDate = new Date(date);
  endDate.setDate(endDate.getDate() + 1);
  
  return this.find({
    timestamp: { $gte: startDate, $lt: endDate },
    changePercent: { $ne: null }
  })
  .sort({ changePercent: -1 })
  .limit(limit);
};

// 查找跌幅排行榜
dailyStockDataSchema.statics.findTopLosers = function(date, limit = 10) {
  const startDate = new Date(date);
  const endDate = new Date(date);
  endDate.setDate(endDate.getDate() + 1);
  
  return this.find({
    timestamp: { $gte: startDate, $lt: endDate },
    changePercent: { $ne: null }
  })
  .sort({ changePercent: 1 })
  .limit(limit);
};

// 查找成交额排行榜
dailyStockDataSchema.statics.findTopVolume = function(date, limit = 10) {
  const startDate = new Date(date);
  const endDate = new Date(date);
  endDate.setDate(endDate.getDate() + 1);
  
  return this.find({
    timestamp: { $gte: startDate, $lt: endDate },
    turnover: { $ne: null }
  })
  .sort({ turnover: -1 })
  .limit(limit);
};

// === 实例方法 ===

// 格式化显示
dailyStockDataSchema.methods.toDisplayString = function() {
  return `${this.stockCode} - ${this.timestamp.toISOString().split('T')[0]} ${this.closePrice}`;
};

// 获取涨跌状态
dailyStockDataSchema.methods.getTrendStatus = function() {
  if (this.changePercent > 0) return 'up';
  if (this.changePercent < 0) return 'down';
  return 'flat';
};

// 获取格式化的价格变动
dailyStockDataSchema.methods.getFormattedPriceChange = function() {
  if (this.priceChange === null || this.changePercent === null) return 'N/A';
  
  const sign = this.priceChange >= 0 ? '+' : '';
  return `${sign}${this.priceChange.toFixed(2)} (${sign}${this.changePercent.toFixed(2)}%)`;
};

// 获取格式化的成交额
dailyStockDataSchema.methods.getFormattedTurnover = function() {
  if (this.turnover === null) return 'N/A';
  
  if (this.turnover >= 100000000) {
    return `${(this.turnover / 100000000).toFixed(2)}亿`;
  } else if (this.turnover >= 10000) {
    return `${(this.turnover / 10000).toFixed(2)}万`;
  }
  return this.turnover.toString();
};

// 获取格式化的市值
dailyStockDataSchema.methods.getFormattedMarketCap = function() {
  if (this.totalMarketCap === null) return 'N/A';
  
  if (this.totalMarketCap >= 100000000) {
    return `${(this.totalMarketCap / 100000000).toFixed(2)}亿`;
  } else if (this.totalMarketCap >= 10000) {
    return `${(this.totalMarketCap / 10000).toFixed(2)}万`;
  }
  return this.totalMarketCap.toString();
};

// 获取技术指标摘要
dailyStockDataSchema.methods.getTechnicalSummary = function() {
  return {
    priceChange: this.getFormattedPriceChange(),
    turnover: this.getFormattedTurnover(),
    marketCap: this.getFormattedMarketCap(),
    pe: this.peRatioTTM ? this.peRatioTTM.toFixed(2) : 'N/A',
    pb: this.pbRatio ? this.pbRatio.toFixed(2) : 'N/A',
    turnoverRate: this.turnoverRate ? `${this.turnoverRate.toFixed(2)}%` : 'N/A',
    amplitude: this.amplitude ? `${this.amplitude.toFixed(2)}%` : 'N/A'
  };
};

// 检查是否为异常数据
dailyStockDataSchema.methods.isAnomalousData = function() {
  // 检查价格是否异常
  if (this.closePrice <= 0) return true;
  if (this.highPrice < this.lowPrice) return true;
  if (this.closePrice > this.highPrice || this.closePrice < this.lowPrice) return true;
  
  // 检查涨跌幅是否异常（超过20%可能是异常）
  if (Math.abs(this.changePercent) > 20) return true;
  
  return false;
};

// 中间件：保存前自动更新时间
dailyStockDataSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// 导出模型
module.exports = mongoose.model('DailyStockData', dailyStockDataSchema);
