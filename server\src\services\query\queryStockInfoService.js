const StockBasicInfo = require('../../models/StockBasicInfo');
const DailyStockData = require('../../models/DailyStockData');
const FinancialData = require('../../models/FinancialData');
const CompanyBusinessData = require('../../models/CompanyBusinessData');
const BusinessComposition = require('../../models/BusinessComposition');
const DividendData = require('../../models/DividendData');

/**
 * 获取需要返回的字段配置（开发者手动配置）
 * @returns {Object} 需要返回的字段配置
 */
const getReturnFields = () => {
  return {
    // 股票基本信息字段
    basicInfo: [
      'stockCode',      // 证券代码
      'stockName',      // 证券简称
      'board',          // 板块
      'industry',       // 行业
      'listingDate'     // 上市时间
    ],

    // 当日股票数据字段
    dailyData: [
      'timestamp',           // 时间
      'closePrice',          // 收盘价
      'openPrice',           // 开盘
      'highPrice',           // 最高
      'lowPrice',            // 最低
      'priceChange',         // 涨跌额
      'changePercent',       // 涨跌幅(%)
      'volume',              // 成交量
      'turnover',            // 成交额
      'turnoverRate',        // 换手率(%)
      'amplitude',           // 振幅(%)
      'averagePrice',        // 均价
      'week52High',          // 52周最高
      'week52Low',           // 52周最低
      'ytdGain',             // 今年以来涨幅(%)
      'circulatingMarketCap',// 流通市值
      'totalMarketCap',      // 总市值
      'peRatioDynamic',      // 市盈率(动)
      'peRatioStatic',       // 市盈率(静)
      'peRatioTTM',          // 市盈率(TTM)
      'pbRatio',              // 市净率
      'earningsPerShare',    // 每股收益(静)
      'bookValuePerShare',   // 每股净资产
      'dividendTTM',         // 股息(TTM)
      'dividendYieldTTM'     // 股息率(TTM)(%)
    ],

    // 财务数据字段
    financialData: [
      'fiscalYear',                             // 财政年度
      'reportDate',                             // 报告日期
      'data.keyMetrics.netProfit',              // 净利润
      'data.keyMetrics.netProfitGrowthRate',    // 净利润同比增长率(%)
      'data.keyMetrics.nonRecurringNetProfit',  // 扣非净利润
      'data.keyMetrics.nonRecurringNetProfitGrowthRate', // 扣非净利润同比增长率(%)
      'data.keyMetrics.totalRevenue',           // 营业总收入
      'data.keyMetrics.totalRevenueGrowthRate', // 营业总收入同比增长率(%)
      'data.keyMetrics.netProfitMargin',        // 销售净利率(%)
      'data.keyMetrics.grossProfitMargin',      // 销售毛利率(%)
      'data.keyMetrics.basicEarningsPerShare',  // 基本每股收益
      'data.keyMetrics.bookValuePerShare',      // 每股净资产
      'data.keyMetrics.capitalReservePerShare', // 每股资本公积金
      'data.keyMetrics.undistributedProfitPerShare', // 每股未分配利润
      'data.keyMetrics.operatingCashFlowPerShare', // 每股经营现金流
      'data.keyMetrics.returnOnEquity',          // 净资产收益率(%)
      'data.keyMetrics.dilutedReturnOnEquity',  // 净资产收益率-摊薄(%)
      'data.keyMetrics.currentRatio',           // 流动比率
      'data.keyMetrics.quickRatio',             // 速动比率
      'data.keyMetrics.conservativeQuickRatio', // 保守速动比率
      'data.keyMetrics.equityRatio',            // 产权比率
      'data.keyMetrics.debtToAssetRatio',       // 资产负债率(%)
      'data.keyMetrics.operatingCycle',        // 营业周期
      'data.keyMetrics.inventoryTurnoverRatio', // 存货周转率
      'data.keyMetrics.inventoryTurnoverDays',  // 存货周转天数
      'data.keyMetrics.accountsReceivableTurnoverDays' // 应收账款周转天数
    ],

    // 公司业务数据字段
    businessData: [
      'mainBusiness',   // 主营业务
      'productType',     // 产品类型
      'productName',    // 产品名称
      'businessScope'  // 经营范围
    ],

    // 业务构成数据字段
    businessComposition: [
      'reportDate',          // 报告日期
      'classificationType',  // 分类类型
      'businessComposition', // 主营构成
      'revenueRatio',        // 收入比例(%)
      'grossProfitMargin'    // 毛利率(%)
    ],

    // 分红数据字段
    dividendData: [
      'reportPeriod',                 // 报告期
      'performanceDisclosureDate',    // 业绩披露日期
      'totalStockTransferRatio',     // 送转比例(%)
      'stockBonusRatio',              //送转股份-送股比例
      'stockTransferRatio',             //送转股份-转股比例
      'cashDividendRatio',            // 现金分红比例
      'cashDividendDescription',      // 现金分红比例描述
      'proposalAnnouncementDate',    // 预案公告日
      'equityRegistrationDate',       // 股权登记日
      'exDividendDate',               // 除权除息日
      'implementationStatus',         // 方案进度
      'latestAnnouncementDate'       // 最新公告日期
    ]
  };
};

/**
 * 根据证券代码查询股票详细信息
 * @param {string} stockCode 证券代码
 * @returns {Promise<Object>} 股票详细信息
 */
const queryStockInfo = async (stockCode) => {
  try {
    const result = {
    };

    // 获取配置的返回字段
    const fields = getReturnFields();

    // 查询基本信息
    const basicInfo = await StockBasicInfo.findByStockCode(stockCode);
    if (basicInfo) {
      result.basicInfo = buildFieldResponse(basicInfo, fields.basicInfo, StockBasicInfo);
    } else {
      result.basicInfo = null;
    }

    // 查询当日数据（最新数据）
    const dailyData = await DailyStockData.findLatestByStockCode(stockCode);
    if (dailyData) {
      result.dailyData = buildFieldResponse(dailyData, fields.dailyData, DailyStockData);
    } else {
      result.dailyData = null;
    }

    // 查询财务数据（最新年度数据）
    const financialData = await FinancialData.findLatestByStockCode(stockCode);
    if (financialData) {
      result.financialData = buildFieldResponse(financialData, fields.financialData, FinancialData);
    } else {
      result.financialData = null;
    }

    // 查询公司业务数据
    const businessData = await CompanyBusinessData.findByStockCode(stockCode);
    if (businessData) {
      result.businessData = buildFieldResponse(businessData, fields.businessData, CompanyBusinessData);
    } else {
      result.businessData = null;
    }

    // 查询业务构成数据（最新数据）
    // const businessComposition = await BusinessComposition.findLatestByStockCode(stockCode);
    // if (businessComposition && businessComposition.length > 0) {
    //   result.businessComposition = businessComposition.map(item =>
    //     buildFieldResponse(item, fields.businessComposition, BusinessComposition)
    //   );
    // } else {
    //   result.businessComposition = [];
    // }

    // 查询分红数据（最新数据）
    const dividendData = await DividendData.findLatestByStockCode(stockCode);
    if (dividendData) {
      result.dividendData = buildFieldResponse(dividendData, fields.dividendData, DividendData);
    } else {
      result.dividendData = null;
    }

    return result;
  } catch (error) {
    throw new Error(`查询股票信息失败: ${error.message}`);
  }
};

/**
 * 构建字段响应对象，将英文字段名转换为中文字段名
 * @param {Object} data 原始数据对象
 * @param {Array} requestedFields 请求的字段列表
 * @param {Object} Model 数据模型，用于获取字段映射
 * @returns {Object} 包含中文字段名的响应对象
 */
const buildFieldResponse = (data, requestedFields, Model) => {
  const response = {};

  requestedFields.forEach(field => {
    // 获取字段值
    let value = getNestedValue(data, field);

    // 获取中文字段名
    let chineseFieldName;
    if (Model && typeof Model.getChineseFieldName === 'function') {
      chineseFieldName = Model.getChineseFieldName(field);
    } else {
      chineseFieldName = field; // 如果没有映射，使用原字段名
    }

    // 设置响应值
    response[chineseFieldName] = value;
  });

  return response;
};

/**
 * 获取嵌套对象的值
 * @param {Object} obj 对象
 * @param {string} path 路径，如 'data.keyMetrics.netProfit'
 * @returns {*} 字段值
 */
const getNestedValue = (obj, path) => {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : null;
  }, obj);
};



module.exports = {
  queryStockInfo,
  getReturnFields
};