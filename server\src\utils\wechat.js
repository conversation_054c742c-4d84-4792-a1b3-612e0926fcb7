const axios = require('axios');

/**
 * 微信小程序API工具类
 */
class WeChatAPI {
  constructor() {
    this.appId = process.env.WX_APP_ID;
    this.appSecret = process.env.WX_APP_SECRET;
    
    if (!this.appId || !this.appSecret) {
      console.warn('微信小程序配置未完整设置，请检查环境变量 WX_APP_ID 和 WX_APP_SECRET');
    }
  }

  /**
   * 通过code获取用户openid和session_key
   * @param {String} code 微信登录凭证
   * @returns {Object} 包含openid、session_key等信息
   */
  async code2Session(code) {
    try {
      const url = 'https://api.weixin.qq.com/sns/jscode2session';
      const params = {
        appid: this.appId,
        secret: this.appSecret,
        js_code: code,
        grant_type: 'authorization_code'
      };

      console.log('调用微信code2Session接口');
      
      const response = await axios.get(url, { params });
      const data = response.data;

      // 检查是否有错误
      if (data.errcode) {
        const errorMessages = {
          40029: '无效的code',
          45011: 'API调用过于频繁，请稍后再试',
          40013: '无效的AppID',
          40125: '无效的AppSecret'
        };
        
        const errorMsg = errorMessages[data.errcode] || `微信API错误: ${data.errmsg}`;
        throw new Error(errorMsg);
      }

      // 检查必要字段
      if (!data.openid) {
        throw new Error('微信API返回数据异常，未获取到openid');
      }

      console.log('微信code2Session调用成功');
      
      return {
        openid: data.openid,
        session_key: data.session_key,
        unionid: data.unionid || null
      };
    } catch (error) {
      console.error('微信code2Session调用失败:', error.message);
      
      // 区分不同类型的错误
      if (error.response) {
        // HTTP错误
        throw new Error('微信服务暂时不可用，请稍后再试');
      } else if (error.message.includes('微信API错误') || error.message.includes('无效的')) {
        // 微信API业务错误
        throw new Error(error.message);
      } else {
        // 其他错误
        throw new Error('微信登录失败，请稍后再试');
      }
    }
  }
}

module.exports = new WeChatAPI(); 