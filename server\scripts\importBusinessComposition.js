const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const mongoose = require('mongoose');
const BusinessComposition = require('../src/models/BusinessComposition');
require('dotenv').config();

// 检测并移除BOM
function removeBOM(content) {
  if (content.charCodeAt(0) === 0xFEFF) {
    return content.slice(1);
  }
  return content;
}

// 解析数值，处理空值和无效值
function parseNumber(value) {
  if (!value || value === '' || value === 'null' || value === 'undefined') {
    return null;
  }
  const num = parseFloat(value);
  return isNaN(num) ? null : num;
}

// 解析日期
function parseDate(dateString) {
  if (!dateString || dateString === '' || dateString === 'null') {
    return null;
  }
  const date = new Date(dateString);
  return isNaN(date.getTime()) ? null : date;
}

// 处理证券代码，确保前导零不被去掉
function formatStockCode(code) {
  if (!code) return null;
  // 转换为字符串并去除空格
  let stockCode = String(code).trim();
  
  // 如果是6位数字，且第一位是0，确保保留前导零
  if (/^\d{6}$/.test(stockCode)) {
    return stockCode.padStart(6, '0');
  }
  
  // 如果是数字但位数不足，补充前导零
  if (/^\d+$/.test(stockCode) && stockCode.length < 6) {
    return stockCode.padStart(6, '0');
  }
  
  return stockCode;
}

// 导入单个CSV文件
async function importCSVFile(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    const errors = [];
    let rowCount = 0;
    
    console.log(`开始处理文件: ${path.basename(filePath)}`);
    
    // 读取文件并检测BOM
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const cleanContent = removeBOM(fileContent);
    
    // 将清理后的内容写入临时缓冲区进行处理
    const stream = require('stream');
    const readable = new stream.Readable();
    readable.push(cleanContent);
    readable.push(null);
    
    readable
      .pipe(csv({
        skipEmptyLines: true,
        skipLinesWithError: true
      }))
      .on('data', (row) => {
        try {
          rowCount++;
          
          // 跳过标题行或无效行
          if (!row['证券代码'] || row['证券代码'] === '证券代码') {
            return;
          }
          
          // 格式化证券代码
          const stockCode = formatStockCode(row['证券代码']);
          if (!stockCode) {
            errors.push(`第${rowCount}行: 证券代码无效`);
            return;
          }
          
          // 解析日期
          const reportDate = parseDate(row['报告日期']);
          if (!reportDate) {
            errors.push(`第${rowCount}行: 报告日期无效 - ${row['报告日期']}`);
            return;
          }
          
          // 构建数据对象
          const businessData = {
            stockCode: stockCode,
            reportDate: reportDate,
            classificationType: row['分类类型'] || null,
            businessComposition: row['主营构成'] || null,
            mainRevenue: parseNumber(row['主营收入(亿元)']),
            revenueRatio: parseNumber(row['收入比例(%)']),
            mainCost: parseNumber(row['主营成本(亿元)']),
            costRatio: parseNumber(row['成本比例(%)']),
            mainProfit: parseNumber(row['主营利润(亿元)']),
            profitRatio: parseNumber(row['利润比例(%)']),
            grossProfitMargin: parseNumber(row['毛利率(%)'])
          };
          
          // 验证必填字段
          if (!businessData.classificationType || !businessData.businessComposition) {
            errors.push(`第${rowCount}行: 分类类型或主营构成为空`);
            return;
          }
          
          results.push(businessData);
          
        } catch (error) {
          errors.push(`第${rowCount}行解析错误: ${error.message}`);
        }
      })
      .on('end', () => {
        console.log(`文件 ${path.basename(filePath)} 解析完成:`);
        console.log(`  - 总行数: ${rowCount}`);
        console.log(`  - 有效数据: ${results.length}`);
        console.log(`  - 错误数据: ${errors.length}`);
        
        if (errors.length > 0) {
          console.log('错误详情:');
          errors.slice(0, 10).forEach(error => console.log(`  ${error}`));
          if (errors.length > 10) {
            console.log(`  ... 还有 ${errors.length - 10} 个错误`);
          }
        }
        
        resolve({ results, errors, fileName: path.basename(filePath) });
      })
      .on('error', (error) => {
        console.error(`文件 ${path.basename(filePath)} 解析失败:`, error);
        reject(error);
      });
  });
}

// 批量插入数据到数据库
async function batchInsertData(dataArray, fileName) {
  if (dataArray.length === 0) {
    console.log(`${fileName}: 没有有效数据需要插入`);
    return { inserted: 0, errors: 0 };
  }
  
  let insertedCount = 0;
  let errorCount = 0;
  const batchSize = 1000; // 每批插入1000条
  
  console.log(`${fileName}: 开始插入 ${dataArray.length} 条数据...`);
  
  for (let i = 0; i < dataArray.length; i += batchSize) {
    const batch = dataArray.slice(i, i + batchSize);
    
    try {
      // 使用 insertMany 批量插入，忽略重复数据
      const result = await BusinessComposition.insertMany(batch, { 
        ordered: false, // 遇到错误继续插入其他数据
        lean: true
      });
      insertedCount += result.length;
      
      console.log(`${fileName}: 批次 ${Math.floor(i/batchSize) + 1} - 插入了 ${result.length} 条数据`);
      
    } catch (error) {
      if (error.name === 'BulkWriteError') {
        // 处理批量写入错误
        const insertedInBatch = error.result.insertedCount || 0;
        const duplicateErrors = error.writeErrors.filter(err => err.code === 11000).length;
        const otherErrors = error.writeErrors.length - duplicateErrors;
        
        insertedCount += insertedInBatch;
        errorCount += otherErrors;
        
        console.log(`${fileName}: 批次 ${Math.floor(i/batchSize) + 1} - 插入了 ${insertedInBatch} 条, 重复 ${duplicateErrors} 条, 其他错误 ${otherErrors} 条`);
        
        // 如果有非重复的错误，打印详情
        if (otherErrors > 0) {
          const nonDuplicateErrors = error.writeErrors.filter(err => err.code !== 11000);
          nonDuplicateErrors.slice(0, 3).forEach(err => {
            console.log(`  错误: ${err.errmsg}`);
          });
        }
      } else {
        console.error(`${fileName}: 批次 ${Math.floor(i/batchSize) + 1} 插入失败:`, error.message);
        errorCount += batch.length;
      }
    }
  }
  
  console.log(`${fileName}: 插入完成 - 成功: ${insertedCount}, 错误: ${errorCount}`);
  return { inserted: insertedCount, errors: errorCount };
}

// 主导入函数
async function importBusinessCompositionData() {
  try {
    // 连接数据库
    console.log('正在连接数据库...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('数据库连接成功');
    
    // 获取CSV文件目录
    const csvDir = path.join(__dirname, '../../data_gp/企业主营业务构成_数据处理后');
    
    if (!fs.existsSync(csvDir)) {
      throw new Error(`目录不存在: ${csvDir}`);
    }
    
    // 获取所有CSV文件
    const files = fs.readdirSync(csvDir).filter(file => file.endsWith('.csv'));
    
    if (files.length === 0) {
      throw new Error(`在 ${csvDir} 中没有找到CSV文件`);
    }
    
    console.log(`找到 ${files.length} 个CSV文件待处理:`);
    files.forEach(file => console.log(`  - ${file}`));
    console.log('');
    
    let totalInserted = 0;
    let totalErrors = 0;
    let totalProcessed = 0;
    
    // 逐个处理文件
    for (const file of files) {
      const filePath = path.join(csvDir, file);
      
      try {
        console.log(`\n=== 处理文件: ${file} ===`);
        
        // 解析CSV文件
        const { results, errors } = await importCSVFile(filePath);
        totalProcessed += results.length;
        
        // 插入数据到数据库
        if (results.length > 0) {
          const { inserted, errors: insertErrors } = await batchInsertData(results, file);
          totalInserted += inserted;
          totalErrors += insertErrors;
        }
        
        if (errors.length > 0) {
          totalErrors += errors.length;
        }
        
      } catch (error) {
        console.error(`处理文件 ${file} 时发生错误:`, error.message);
        totalErrors++;
      }
    }
    
    // 输出汇总信息
    console.log('\n=== 导入汇总 ===');
    console.log(`处理文件数: ${files.length}`);
    console.log(`解析数据条数: ${totalProcessed}`);
    console.log(`成功插入: ${totalInserted}`);
    console.log(`错误数量: ${totalErrors}`);
    console.log(`成功率: ${totalProcessed > 0 ? ((totalInserted / totalProcessed) * 100).toFixed(2) : 0}%`);
    
    // 获取数据库统计信息
    const totalCount = await BusinessComposition.countDocuments();
    console.log(`数据库中总计: ${totalCount} 条业务构成数据`);
    
    // 获取一些统计信息
    const stats = await BusinessComposition.aggregate([
      {
        $group: {
          _id: null,
          uniqueStocks: { $addToSet: '$stockCode' },
          uniqueReportDates: { $addToSet: '$reportDate' },
          uniqueClassificationTypes: { $addToSet: '$classificationType' }
        }
      }
    ]);
    
    if (stats.length > 0) {
      console.log(`唯一股票数: ${stats[0].uniqueStocks.length}`);
      console.log(`唯一报告期数: ${stats[0].uniqueReportDates.length}`);
      console.log(`分类类型数: ${stats[0].uniqueClassificationTypes.length}`);
      console.log(`分类类型: ${stats[0].uniqueClassificationTypes.join(', ')}`);
    }
    
  } catch (error) {
    console.error('导入过程中发生错误:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await mongoose.connection.close();
    console.log('\n数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  console.log('开始导入企业主营业务构成数据...\n');
  importBusinessCompositionData()
    .then(() => {
      console.log('\n导入完成!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('导入失败:', error);
      process.exit(1);
    });
}

module.exports = {
  importBusinessCompositionData,
  importCSVFile,
  batchInsertData
};
