const Joi = require('joi');

// 数值范围验证模式
const rangeSchema = Joi.object({
  min: Joi.number().allow(null),
  max: Joi.number().allow(null)
}).custom((value, helpers) => {
  if (value.min !== null && value.max !== null && value.min > value.max) {
    return helpers.error('range.invalid');
  }
  return value;
}).messages({
  'range.invalid': '最小值不能大于最大值'
});

// 当日数据筛选条件验证
const dailyConditionsSchema = Joi.object({
  // 市盈率范围
  peRatioDynamicRange: rangeSchema,
  peRatioStaticRange: rangeSchema,
  peRatioTTMRange: rangeSchema,
  
  // 市净率范围
  pbRatioRange: rangeSchema,
  
  // 市值范围
  totalMarketCapRange: rangeSchema,
  circulatingMarketCapRange: rangeSchema,
  
  // 涨停跌停条件
  isLimitUp: Joi.boolean(),
  isLimitDown: Joi.boolean(),
  touchedLimitUp: Joi.boolean(),
  touchedLimitDown: Joi.boolean(),
  
  // 其他技术指标范围
  turnoverRateRange: rangeSchema,
  changePercentRange: rangeSchema,
  amplitudeRange: rangeSchema,
  ytdGainRange: rangeSchema,
  sixtyDayChangeRange: rangeSchema,
  
  // 股息率范围
  dividendYieldTTMRange: rangeSchema
});

// 财务数据筛选条件验证
const financialConditionsSchema = Joi.object({
  // 增长率指标
  netProfitGrowthRateRange: rangeSchema,
  nonRecurringNetProfitGrowthRateRange: rangeSchema,
  totalRevenueGrowthRateRange: rangeSchema,
  
  // 盈利能力指标
  netProfitMarginRange: rangeSchema,
  grossProfitMarginRange: rangeSchema,
  returnOnEquityRange: rangeSchema,
  dilutedReturnOnEquityRange: rangeSchema,
  
  // 偿债能力指标
  currentRatioRange: rangeSchema,
  quickRatioRange: rangeSchema,
  equityRatioRange: rangeSchema,
  debtToAssetRatioRange: rangeSchema,
  
  // 营运能力指标
  inventoryTurnoverRatioRange: rangeSchema,
  inventoryTurnoverDaysRange: rangeSchema,
  accountsReceivableTurnoverDaysRange: rangeSchema
});

// 基本信息筛选条件验证
const basicInfoConditionsSchema = Joi.object({
  // 板块筛选
  board: Joi.array().items(Joi.string().min(1)).min(1).optional(),
  
  // 行业筛选
  industry: Joi.array().items(Joi.string().min(1)).min(1).optional(),
  
  // 已上市年份范围
  listedYearsRange: rangeSchema.optional()
});

// 筛选条件验证模式（不包含targetDate）
const filterConditionsSchema = Joi.object({
  // 当日数据筛选条件
  dailyConditions: dailyConditionsSchema.optional(),
  
  // 财务数据筛选条件
  financialConditions: financialConditionsSchema.optional(),
  
  // 基本信息筛选条件
  basicInfoConditions: basicInfoConditionsSchema.optional()
}).custom((value, helpers) => {
  // 至少需要一个筛选条件
  if (!value.dailyConditions && !value.financialConditions && !value.basicInfoConditions) {
    return helpers.error('conditions.required');
  }
  
  // 检查筛选条件是否为空对象
  const hasDailyConditions = value.dailyConditions && 
    Object.keys(value.dailyConditions).length > 0;
  const hasFinancialConditions = value.financialConditions && 
    Object.keys(value.financialConditions).length > 0;
  const hasBasicInfoConditions = value.basicInfoConditions && 
    Object.keys(value.basicInfoConditions).length > 0;
    
  if (!hasDailyConditions && !hasFinancialConditions && !hasBasicInfoConditions) {
    return helpers.error('conditions.empty');
  }
  
  return value;
}).messages({
  'conditions.required': '至少需要一个筛选条件',
  'conditions.empty': '筛选条件不能为空'
});

// 主要的筛选请求验证模式
const filterRequestSchema = Joi.object({
  // 筛选条件
  filterConditions: filterConditionsSchema.required(),
  
  // 目标日期（格式：YYYY-MM-DD）
  targetDate: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .optional()
    .messages({
      'string.pattern.base': '日期格式应为 YYYY-MM-DD'
    })
}).custom((value, helpers) => {
  return value;
}).messages({
  'conditions.required': '必须提供至少一个筛选条件（dailyConditions、financialConditions 或 basicInfoConditions）',
  'conditions.empty': '筛选条件不能为空'
});

/**
 * 验证股票筛选请求
 * @param {Object} req Express请求对象
 * @param {Object} res Express响应对象
 * @param {Function} next Express下一个中间件
 */
const validateFilterRequest = (req, res, next) => {
  const { error, value } = filterRequestSchema.validate(req.body, {
    abortEarly: false,
    allowUnknown: false
  });

  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));

    return res.status(400).json({
      success: false,
      message: '请求参数验证失败',
      errors
    });
  }

  // 将验证后的数据赋值给 req.body
  req.body = value;
  next();
};

/**
 * 验证范围参数的辅助函数
 * @param {Object} range 范围对象
 * @param {string} fieldName 字段名称
 * @returns {Object} 验证结果
 */
const validateRange = (range, fieldName) => {
  if (!range) return { isValid: true };
  
  const { min, max } = range;
  
  if (min !== undefined && max !== undefined && min > max) {
    return {
      isValid: false,
      message: `${fieldName} 的最小值不能大于最大值`
    };
  }
  
  return { isValid: true };
};

/**
 * 获取验证错误的友好提示信息
 * @param {string} fieldPath 字段路径
 * @returns {string} 友好提示信息
 */
const getFieldFriendlyName = (fieldPath) => {
  const fieldNames = {
    'dailyConditions.peRatioDynamicRange': '市盈率(动)',
    'dailyConditions.peRatioStaticRange': '市盈率(静)',
    'dailyConditions.peRatioTTMRange': '市盈率(TTM)',
    'dailyConditions.pbRatioRange': '市净率',
    'dailyConditions.totalMarketCapRange': '总市值',
    'dailyConditions.circulatingMarketCapRange': '流通市值',
    'dailyConditions.turnoverRateRange': '换手率',
    'dailyConditions.changePercentRange': '涨跌幅',
    'dailyConditions.amplitudeRange': '振幅',
    'dailyConditions.ytdGainRange': '今年以来涨幅',
    'dailyConditions.sixtyDayChangeRange': '60日涨跌幅',
    'dailyConditions.dividendYieldTTMRange': '股息率(TTM)',
    'financialConditions.netProfitGrowthRateRange': '净利润同比增长率',
    'financialConditions.nonRecurringNetProfitGrowthRateRange': '扣非净利润同比增长率',
    'financialConditions.totalRevenueGrowthRateRange': '营业总收入同比增长率',
    'financialConditions.netProfitMarginRange': '销售净利率',
    'financialConditions.grossProfitMarginRange': '销售毛利率',
    'financialConditions.returnOnEquityRange': '净资产收益率',
    'financialConditions.dilutedReturnOnEquityRange': '净资产收益率-摊薄',
    'financialConditions.currentRatioRange': '流动比率',
    'financialConditions.quickRatioRange': '速动比率',
    'financialConditions.equityRatioRange': '产权比率',
    'financialConditions.debtToAssetRatioRange': '资产负债率',
    'financialConditions.inventoryTurnoverRatioRange': '存货周转率',
    'financialConditions.inventoryTurnoverDaysRange': '存货周转天数',
    'financialConditions.accountsReceivableTurnoverDaysRange': '应收账款周转天数',
    'basicInfoConditions.board': '板块',
    'basicInfoConditions.industry': '行业',
    'basicInfoConditions.listedYearsRange': '已上市年份'
  };
  
  return fieldNames[fieldPath] || fieldPath;
};

module.exports = {
  validateFilterRequest,
  filterRequestSchema,
  dailyConditionsSchema,
  financialConditionsSchema,
  basicInfoConditionsSchema,
  validateRange,
  getFieldFriendlyName
};