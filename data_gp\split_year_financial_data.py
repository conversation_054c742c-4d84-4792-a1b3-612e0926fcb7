import pandas as pd
import os
from pathlib import Path

def split_csv_by_companies(input_file, output_folder, companies_per_file=500):
    """
    将大型CSV文件按企业数量分拆成多个小文件
    
    Args:
        input_file: 输入CSV文件路径
        output_folder: 输出文件夹路径
        companies_per_file: 每个文件包含的企业数量
    """
    
    # 创建输出文件夹
    output_path = Path(output_folder)
    output_path.mkdir(exist_ok=True)
    
    # 读取CSV文件，指定证券代码列为字符串类型以保持原始格式
    print("正在读取CSV文件...")
    df = pd.read_csv(input_file, dtype={'证券代码': str})
    
    print(f"总共读取了 {len(df)} 行数据")
    
    # 获取唯一的证券代码
    unique_companies = df['证券代码'].unique()
    print(f"共有 {len(unique_companies)} 个不同的企业")
    
    # 计算需要分拆成多少个文件
    total_files = (len(unique_companies) + companies_per_file - 1) // companies_per_file
    print(f"将分拆成 {total_files} 个文件")
    
    # 分拆数据
    for i in range(0, len(unique_companies), companies_per_file):
        # 获取当前批次的企业代码
        batch_companies = unique_companies[i:i + companies_per_file]
        
        # 筛选当前批次的数据
        batch_data = df[df['证券代码'].isin(batch_companies)]
        
        # 生成输出文件名
        file_index = i // companies_per_file + 1
        output_filename = f"企业关键指标年度_part_{file_index:02d}.csv"
        output_filepath = output_path / output_filename
        
        # 保存到CSV文件
        batch_data.to_csv(output_filepath, index=False, encoding='utf-8-sig')
        
        print(f"已保存文件 {file_index}/{total_files}: {output_filename}")
        print(f"  - 包含企业数量: {len(batch_companies)}")
        print(f"  - 包含数据行数: {len(batch_data)}")
        print(f"  - 企业代码范围: {batch_companies[0]} 到 {batch_companies[-1]}")
        print()
    
    print("分拆完成！")
    
    # 验证分拆结果
    print("验证分拆结果:")
    total_rows_split = 0
    for i in range(1, total_files + 1):
        filename = f"企业关键指标年度_part_{i:02d}.csv"
        filepath = output_path / filename
        if filepath.exists():
            split_df = pd.read_csv(filepath, dtype={'证券代码': str})
            total_rows_split += len(split_df)
            print(f"  {filename}: {len(split_df)} 行, {len(split_df['证券代码'].unique())} 个企业")
    
    print(f"\n原始文件总行数: {len(df)}")
    print(f"分拆后总行数: {total_rows_split}")
    print(f"数据完整性: {'✓ 完整' if total_rows_split == len(df) else '✗ 有缺失'}")

if __name__ == "__main__":
    # 设置文件路径
    input_file = "企业关键指标年度/企业关键指标年度_修正后.csv"
    output_folder = "企业关键指标年度_分拆后"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 找不到输入文件 {input_file}")
        print("请确认文件路径是否正确")
    else:
        # 执行分拆
        split_csv_by_companies(input_file, output_folder, companies_per_file=500)
