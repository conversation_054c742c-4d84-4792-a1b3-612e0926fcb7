const { success } = require('../../utils/response');
const queryStockInfoService = require('../../services/query/queryStockInfoService');

/**
 * 根据证券代码查询股票详细信息
 * @route POST /api/v1/query/stock-info
 * @access Public
 */
const queryStockInfo = async (req, res, next) => {
  try {
    const { stockCode } = req.body;

    // 调用服务层查询股票信息
    const stockInfo = await queryStockInfoService.queryStockInfo(stockCode);

    // 返回成功响应
    return success(res, 200, '查询股票信息成功', stockInfo);

  } catch (err) {
    next(err);
  }
};

module.exports = {
  queryStockInfo
};