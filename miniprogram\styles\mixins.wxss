/* 言策 AI量化投资小程序 - 样式混入文件 */
/* ================================================ */

/* 引入变量文件 */
@import './variables.wxss';

/* ==================== 卡片样式混入 ==================== */

/* 基础卡片 - 用于一般内容展示 */
.card-base {
  background-color: var(--bg-card);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-card);
  border: 1rpx solid var(--border-light);
  padding: var(--spacing-lg);
  transition: var(--transition-base);
}

/* 可交互卡片 - 带悬停效果 */
.card-interactive {
  background-color: var(--bg-card);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-card);
  border: 1rpx solid var(--border-light);
  padding: var(--spacing-lg);
  transition: var(--transition-base);
  cursor: pointer;
}

/* 注意：微信小程序不支持hover伪类，此样式已注释 */
/*
.card-interactive:hover {
  box-shadow: var(--shadow-float);
  transform: translateY(-2rpx);
  border-color: var(--primary-light);
}
*/

.card-interactive:active {
  transform: translateY(0);
  box-shadow: var(--shadow-light);
}

/* 紧凑卡片 - 用于列表项 */
.card-compact {
  background-color: var(--bg-card);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-light);
  border: 1rpx solid var(--border-light);
  padding: var(--spacing-base);
  margin-bottom: var(--spacing-sm);
}

/* 强调卡片 - 用于重要信息 */
.card-highlight {
  background: var(--gradient-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-strong);
  padding: var(--spacing-xl);
  color: var(--text-inverse);
}

/* 无边框卡片 - 简洁风格 */
.card-borderless {
  background-color: var(--bg-card);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  border: none;
}

/* ==================== 按钮样式混入 ==================== */

/* 按钮基础样式 */
.btn-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: 1.5;
  text-align: center;
  cursor: pointer;
  transition: var(--transition-base);
  border: 1rpx solid transparent;
  user-select: none;
  position: relative;
  overflow: hidden;
}

/* 主按钮 - 最重要的操作 */
.btn-primary-style {
  background: var(--gradient-primary);
  color: var(--text-inverse);
  padding: var(--spacing-base) var(--spacing-lg);
  box-shadow: var(--shadow-light);
}

.btn-primary-style:hover {
  box-shadow: var(--shadow-base);
  transform: translateY(-1rpx);
}

.btn-primary-style:active {
  transform: translateY(1rpx);
  box-shadow: var(--shadow-light);
}

/* 次要按钮 - 次要操作 */
.btn-secondary-style {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1rpx solid var(--border-medium);
  padding: var(--spacing-base) var(--spacing-lg);
}

.btn-secondary-style:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--primary-light);
}

/* 轮廓按钮 - 不突出的操作 */
.btn-outline-style {
  background-color: transparent;
  color: var(--primary-color);
  border: 1rpx solid var(--primary-color);
  padding: var(--spacing-base) var(--spacing-lg);
}

.btn-outline-style:hover {
  background-color: var(--primary-color);
  color: var(--text-inverse);
}

/* 文本按钮 - 最不突出的操作 */
.btn-text-style {
  background-color: transparent;
  color: var(--primary-color);
  border: none;
  padding: var(--spacing-sm) var(--spacing-base);
}

.btn-text-style:hover {
  background-color: var(--color-info-light);
}

/* 危险按钮 - 删除等危险操作 */
.btn-danger-style {
  background-color: var(--color-danger);
  color: var(--text-inverse);
  padding: var(--spacing-base) var(--spacing-lg);
}

.btn-danger-style:hover {
  background-color: #C0392B;
}

/* 成功按钮 - 确认等积极操作 */
.btn-success-style {
  background-color: var(--color-success);
  color: var(--text-inverse);
  padding: var(--spacing-base) var(--spacing-lg);
}

.btn-success-style:hover {
  background-color: #229954;
}

/* 按钮尺寸变体 */
.btn-small {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn-large {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-lg);
}

.btn-extra-large {
  padding: var(--spacing-lg) var(--spacing-xxl);
  font-size: var(--font-size-xl);
  border-radius: var(--radius-xl);
}

/* 块级按钮 */
.btn-block {
  width: 100%;
  display: flex;
}

/* 按钮禁用状态 */
.btn-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* 按钮加载状态 */
.btn-loading {
  opacity: 0.8;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-loading::after {
  content: '';
  position: absolute;
  width: 16rpx;
  height: 16rpx;
  margin: auto;
  border: 2rpx solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ==================== 输入框样式混入 ==================== */

/* 基础输入框 */
.input-base {
  width: 100%;
  padding: var(--spacing-lg) var(--spacing-base);
  border: 1rpx solid var(--border-medium);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: var(--transition-base);
  line-height: 1.4;
  min-height: 80rpx;
  box-sizing: border-box;
}

.input-base:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2rpx rgba(27, 79, 114, 0.1);
  outline: none;
}

.input-base::placeholder {
  color: var(--text-placeholder);
}

/* 搜索框样式 */
.input-search {
  width: 100%;
  padding: var(--spacing-lg) var(--spacing-lg);
  border: 1rpx solid var(--border-light);
  border-radius: var(--radius-pill);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  transition: var(--transition-base);
  line-height: 1.4;
  min-height: 80rpx;
  box-sizing: border-box;
}

.input-search:focus {
  border-color: var(--primary-color);
  background-color: var(--bg-primary);
  box-shadow: var(--shadow-light);
}

/* 带图标的输入框 */
.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-with-icon input {
  width: 100%;
  padding: var(--spacing-lg) var(--spacing-base) var(--spacing-lg) 80rpx;
  border: 1rpx solid var(--border-medium);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  line-height: 1.4;
  min-height: 80rpx;
  box-sizing: border-box;
}

.input-with-icon .icon {
  position: absolute;
  left: var(--spacing-base);
  color: var(--text-tertiary);
  z-index: 1;
}

/* 错误状态输入框 */
.input-error {
  border-color: var(--color-danger);
  box-shadow: 0 0 0 2rpx rgba(231, 76, 60, 0.1);
}

/* 成功状态输入框 */
.input-success {
  border-color: var(--color-success);
  box-shadow: 0 0 0 2rpx rgba(39, 174, 96, 0.1);
}

/* 禁用状态输入框 */
.input-disabled {
  background-color: var(--bg-disabled);
  color: var(--text-disabled);
  cursor: not-allowed;
}

/* ==================== 列表项样式混入 ==================== */

/* 基础列表项 */
.list-item-base {
  display: flex;
  align-items: center;
  padding: var(--spacing-base) var(--spacing-lg);
  border-bottom: 1rpx solid var(--divider);
  background-color: var(--bg-primary);
  transition: var(--transition-fast);
}

.list-item-base:last-child {
  border-bottom: none;
}

/* 可点击列表项 */
.list-item-clickable {
  display: flex;
  align-items: center;
  padding: var(--spacing-base) var(--spacing-lg);
  border-bottom: 1rpx solid var(--divider);
  background-color: var(--bg-primary);
  transition: var(--transition-fast);
  cursor: pointer;
}

.list-item-clickable:hover {
  background-color: var(--bg-secondary);
}

.list-item-clickable:active {
  background-color: var(--bg-tertiary);
}

/* 带图标的列表项 */
.list-item-with-icon {
  display: flex;
  align-items: center;
  padding: var(--spacing-base) var(--spacing-lg);
  border-bottom: 1rpx solid var(--divider);
  background-color: var(--bg-primary);
}

.list-item-with-icon .icon {
  margin-right: var(--spacing-base);
  color: var(--text-tertiary);
  flex-shrink: 0;
}

/* 带箭头的列表项 */
.list-item-with-arrow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-base) var(--spacing-lg);
  border-bottom: 1rpx solid var(--divider);
  background-color: var(--bg-primary);
  cursor: pointer;
}

.list-item-with-arrow::after {
  content: '>';
  color: var(--text-tertiary);
  font-size: var(--font-size-lg);
  transform: rotate(0deg);
  transition: var(--transition-fast);
}

.list-item-with-arrow:hover::after {
  color: var(--primary-color);
  transform: translateX(4rpx);
}

/* ==================== 金融数据展示混入 ==================== */

/* 股票卡片 - 展示股票基本信息 */
.stock-card {
  background-color: var(--bg-card);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-card);
  padding: var(--spacing-lg);
  border: 1rpx solid var(--border-light);
  transition: var(--transition-base);
  cursor: pointer;
}

.stock-card:hover {
  box-shadow: var(--shadow-float);
  transform: translateY(-2rpx);
}

.stock-card .header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-base);
}

.stock-card .code {
  font-family: 'SF Mono', Monaco, monospace;
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.stock-card .name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stock-card .price {
  font-family: 'SF Mono', Monaco, monospace;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-xs);
}

.stock-card .change {
  font-family: 'SF Mono', Monaco, monospace;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

/* 数据行 - 用于展示键值对数据 */
.data-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1rpx solid var(--divider);
}

.data-row:last-child {
  border-bottom: none;
}

.data-row .label {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
}

.data-row .value {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  font-family: 'SF Mono', Monaco, monospace;
}

.data-row .value.rise {
  color: var(--color-rise);
}

.data-row .value.fall {
  color: var(--color-fall);
}

/* 图表容器 - 用于包装图表组件 */
.chart-container {
  background-color: var(--bg-card);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-light);
  border: 1rpx solid var(--border-light);
  margin-bottom: var(--spacing-lg);
}

.chart-container .title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-base);
  text-align: center;
}

.chart-container .subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

/* 指标卡片 - 展示关键指标 */
.metric-card {
  background: var(--gradient-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  color: var(--text-inverse);
  box-shadow: var(--shadow-base);
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  transform: rotate(45deg);
  transition: var(--transition-slow);
}

.metric-card:hover::before {
  animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.metric-card .value {
  font-size: var(--font-size-display);
  font-weight: var(--font-weight-bold);
  font-family: 'SF Mono', Monaco, monospace;
  margin-bottom: var(--spacing-xs);
}

.metric-card .label {
  font-size: var(--font-size-base);
  opacity: 0.9;
}

.metric-card .trend {
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
  opacity: 0.8;
}

/* 涨跌榜样式 */
.ranking-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-base) var(--spacing-lg);
  border-bottom: 1rpx solid var(--divider);
  background-color: var(--bg-primary);
  transition: var(--transition-fast);
}

.ranking-item:hover {
  background-color: var(--bg-secondary);
}

.ranking-item .rank {
  width: 60rpx;
  text-align: center;
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  margin-right: var(--spacing-base);
}

.ranking-item .rank.top3 {
  color: var(--accent-color);
}

.ranking-item .info {
  flex: 1;
  margin-right: var(--spacing-base);
}

.ranking-item .code {
  font-family: 'SF Mono', Monaco, monospace;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.ranking-item .name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-top: var(--spacing-xs);
}

.ranking-item .price {
  font-family: 'SF Mono', Monaco, monospace;
  font-weight: var(--font-weight-semibold);
  text-align: right;
}

.ranking-item .change {
  font-family: 'SF Mono', Monaco, monospace;
  font-size: var(--font-size-sm);
  text-align: right;
  margin-top: var(--spacing-xs);
}

/* 持仓卡片 */
.position-card {
  background-color: var(--bg-card);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-card);
  border-left: 4rpx solid var(--primary-color);
  margin-bottom: var(--spacing-base);
}

.position-card.profit {
  border-left-color: var(--color-rise);
}

.position-card.loss {
  border-left-color: var(--color-fall);
}

.position-card .header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-base);
}

.position-card .symbol {
  font-family: 'SF Mono', Monaco, monospace;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.position-card .status {
  font-size: var(--font-size-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
}

.position-card .metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-base);
}

.position-card .metric {
  text-align: center;
}

.position-card .metric .label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.position-card .metric .value {
  font-family: 'SF Mono', Monaco, monospace;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* ==================== 响应式设计混入 ==================== */

/* 小屏幕适配 (iPhone SE等) */
@media screen and (max-width: 375px) {
  .responsive-padding {
    padding-left: var(--spacing-base);
    padding-right: var(--spacing-base);
  }

  .responsive-text {
    font-size: var(--font-size-sm);
  }

  .responsive-card {
    margin: var(--spacing-sm);
    padding: var(--spacing-base);
  }

  .responsive-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
}

/* 中等屏幕适配 (iPhone 12等) */
@media screen and (min-width: 376px) and (max-width: 414px) {
  .responsive-padding {
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
  }

  .responsive-grid {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-base);
  }
}

/* 大屏幕适配 (iPhone 12 Pro Max等) */
@media screen and (min-width: 415px) {
  .responsive-padding {
    padding-left: var(--spacing-xl);
    padding-right: var(--spacing-xl);
  }

  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
  }

  .responsive-text-large {
    font-size: var(--font-size-lg);
  }
}

/* ==================== 性能优化混入 ==================== */

/* GPU加速 - 用于需要硬件加速的元素 */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000rpx;
}

/* 防止重绘 - 用于频繁变化的元素 */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-scroll {
  will-change: scroll-position;
}

/* 优化滚动 - 用于长列表 */
.optimized-scroll {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* 图片优化 - 用于图片容器 */
.image-optimized {
  object-fit: cover;
  object-position: center;
  image-rendering: -webkit-optimize-contrast;
}

/* 文本渲染优化 */
.text-optimized {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 动画性能优化 */
.animation-optimized {
  animation-fill-mode: both;
  animation-play-state: paused;
}

.animation-optimized.active {
  animation-play-state: running;
}

/* ==================== 通用工具混入 ==================== */

/* 居中容器 - 用于页面主要内容 */
.container {
  width: 100%;
  max-width: 750rpx;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* 安全区域适配 - 用于全面屏设备 */
.safe-area-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}

/* 加载状态 - 用于数据加载时的占位 */
.skeleton {
  background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.skeleton-text {
  height: 1em;
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-xs);
}

.skeleton-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: var(--radius-round);
}

.skeleton-button {
  height: 72rpx;
  border-radius: var(--radius-base);
}

/* 空状态 - 用于无数据时的展示 */
.empty-state {
  text-align: center;
  padding: var(--spacing-xxl) var(--spacing-lg);
  color: var(--text-secondary);
}

.empty-state .icon {
  font-size: 120rpx;
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-lg);
}

.empty-state .title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-base);
}

.empty-state .description {
  font-size: var(--font-size-base);
  color: var(--text-tertiary);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
}

/* 错误状态 - 用于错误信息展示 */
.error-state {
  text-align: center;
  padding: var(--spacing-xxl) var(--spacing-lg);
  background-color: var(--color-danger-light);
  border-radius: var(--radius-md);
  border: 1rpx solid var(--color-danger);
}

.error-state .icon {
  font-size: 100rpx;
  color: var(--color-danger);
  margin-bottom: var(--spacing-lg);
}

.error-state .title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-danger);
  margin-bottom: var(--spacing-base);
}

.error-state .message {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

/* 成功状态 - 用于成功信息展示 */
.success-state {
  text-align: center;
  padding: var(--spacing-xl) var(--spacing-lg);
  background-color: var(--color-success-light);
  border-radius: var(--radius-md);
  border: 1rpx solid var(--color-success);
}

.success-state .icon {
  font-size: 100rpx;
  color: var(--color-success);
  margin-bottom: var(--spacing-lg);
}

.success-state .title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-success);
  margin-bottom: var(--spacing-base);
}

/* 浮动操作按钮 - 用于主要操作 */
.fab {
  position: fixed;
  bottom: var(--spacing-xxl);
  right: var(--spacing-lg);
  width: 112rpx;
  height: 112rpx;
  border-radius: var(--radius-round);
  background: var(--gradient-primary);
  color: var(--text-inverse);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-strong);
  z-index: var(--z-index-fixed);
  transition: var(--transition-base);
}

.fab:hover {
  transform: scale(1.1);
  box-shadow: 0 12rpx 40rpx rgba(27, 79, 114, 0.3);
}

.fab:active {
  transform: scale(0.95);
}

/* 标签 - 用于状态标识 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-pill);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
}

.tag-primary {
  background-color: var(--color-info-light);
  color: var(--color-info);
}

.tag-success {
  background-color: var(--color-success-light);
  color: var(--color-success);
}

.tag-warning {
  background-color: var(--color-warning-light);
  color: var(--color-warning);
}

.tag-danger {
  background-color: var(--color-danger-light);
  color: var(--color-danger);
}

.tag-neutral {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
}

/* 分割线 - 用于内容分隔 */
.divider {
  height: 1rpx;
  background-color: var(--divider);
  margin: var(--spacing-lg) 0;
}

.divider-thick {
  height: 2rpx;
  background-color: var(--border-medium);
  margin: var(--spacing-xl) 0;
}

.divider-vertical {
  width: 1rpx;
  height: 100%;
  background-color: var(--divider);
  margin: 0 var(--spacing-base);
}

/* 徽章 - 用于数量提示 */
.badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 8rpx;
  background-color: var(--color-danger);
  color: var(--text-inverse);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  line-height: 32rpx;
  text-align: center;
  border-radius: var(--radius-pill);
  box-shadow: var(--shadow-light);
}

.badge-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: var(--radius-round);
  background-color: var(--color-danger);
  position: absolute;
  top: -4rpx;
  right: -4rpx;
}

/* 进度条 - 用于进度展示 */
.progress {
  width: 100%;
  height: 8rpx;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-pill);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--gradient-primary);
  border-radius: var(--radius-pill);
  transition: width var(--transition-base);
}

.progress-success .progress-bar {
  background: var(--gradient-success);
}

.progress-warning .progress-bar {
  background: var(--gradient-warning);
}

.progress-danger .progress-bar {
  background: var(--gradient-danger);
}
