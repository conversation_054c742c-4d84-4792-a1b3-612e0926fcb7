import pandas as pd
import os
import glob

def check_stock_codes():
    # 读取沪深上市公司列表，确保证券代码作为字符串读取
    company_list_file = "沪深上市公司列表.csv"
    
    try:
        # 使用dtype参数确保证券代码作为字符串读取
        company_df = pd.read_csv(company_list_file, dtype={'证券代码': str})
        print(f"成功读取公司列表文件，共{len(company_df)}家公司")
    except FileNotFoundError:
        print(f"错误：找不到文件 {company_list_file}")
        return
    except Exception as e:
        print(f"读取公司列表文件时出错：{e}")
        return
    
    # 获取所有证券代码
    all_stock_codes = set(company_df['证券代码'].astype(str))
    print(f"待检查的证券代码数量：{len(all_stock_codes)}")
    
    # 获取企业关键指标报告期文件夹中的所有csv文件
    report_folder = "企业关键指标年度"
    csv_files = glob.glob(os.path.join(report_folder, "*.csv"))
    
    if not csv_files:
        print(f"错误：在文件夹 {report_folder} 中没有找到csv文件")
        return
    
    print(f"找到{len(csv_files)}个报告文件")
    
    # 收集所有报告文件中的证券代码
    found_stock_codes = set()
    
    for i, csv_file in enumerate(csv_files, 1):
        try:
            # 使用dtype参数确保证券代码作为字符串读取
            df = pd.read_csv(csv_file, dtype={'证券代码': str})
            file_stock_codes = set(df['证券代码'].astype(str))
            found_stock_codes.update(file_stock_codes)
            print(f"处理进度：{i}/{len(csv_files)} - {os.path.basename(csv_file)}")
        except Exception as e:
            print(f"读取文件 {csv_file} 时出错：{e}")
            continue
    
    print(f"报告文件中找到的证券代码数量：{len(found_stock_codes)}")
    
    # 找出缺失的证券代码
    missing_codes = all_stock_codes - found_stock_codes
    
    if missing_codes:
        print(f"发现{len(missing_codes)}个缺失的证券代码")
        
        # 创建缺失代码的DataFrame
        missing_df = company_df[company_df['证券代码'].isin(missing_codes)].copy()
        
        # 保存到文件
        output_file = "缺失的证券代码.csv"
        missing_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"缺失的证券代码已保存到：{output_file}")
        
        # 显示前10个缺失的代码作为示例
        print("\n缺失的证券代码示例（前10个）：")
        print(missing_df.head(10).to_string(index=False))
        
        # 保存详细统计信息
        stats_file = "检查统计信息.txt"
        with open(stats_file, 'w', encoding='utf-8') as f:
            f.write(f"证券代码包含情况检查报告\n")
            f.write(f"="*50 + "\n")
            f.write(f"总公司数量：{len(company_df)}\n")
            f.write(f"报告文件数量：{len(csv_files)}\n")
            f.write(f"报告中包含的证券代码数量：{len(found_stock_codes)}\n")
            f.write(f"缺失的证券代码数量：{len(missing_codes)}\n")
            f.write(f"覆盖率：{(len(found_stock_codes)/len(all_stock_codes)*100):.2f}%\n")
            f.write(f"\n缺失的证券代码列表：\n")
            for code in sorted(missing_codes):
                company_name = company_df[company_df['证券代码'] == code]['证券简称'].iloc[0]
                board = company_df[company_df['证券代码'] == code]['板块'].iloc[0]
                f.write(f"{code} - {company_name} ({board})\n")
        
        print(f"详细统计信息已保存到：{stats_file}")
        
    else:
        print("太好了！所有证券代码都在报告文件中找到了！")
        
        # 即使没有缺失，也保存一个统计文件
        stats_file = "检查统计信息.txt"
        with open(stats_file, 'w', encoding='utf-8') as f:
            f.write(f"证券代码包含情况检查报告\n")
            f.write(f"="*50 + "\n")
            f.write(f"总公司数量：{len(company_df)}\n")
            f.write(f"报告文件数量：{len(csv_files)}\n")
            f.write(f"报告中包含的证券代码数量：{len(found_stock_codes)}\n")
            f.write(f"缺失的证券代码数量：0\n")
            f.write(f"覆盖率：100.00%\n")
            f.write(f"\n所有证券代码都已包含在报告文件中！\n")

if __name__ == "__main__":
    check_stock_codes()
