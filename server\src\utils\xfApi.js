const crypto = require('crypto');
const WebSocket = require('ws');

// 从环境变量读取配置
const XF_APP_ID = process.env.XF_APP_ID;
const XF_API_SECRET = process.env.XF_API_SECRET;
const XF_API_KEY = process.env.XF_API_KEY;

/**
 * 生成科大讯飞API认证参数
 * @returns {Object} 认证参数
 */
const generateAuthParams = () => {
  const date = new Date().toUTCString();
  const algorithm = 'hmac-sha256';
  
  // 构建签名字符串 - 修正host为ws-api.xfyun.cn
  const signatureOrigin = `host: ws-api.xfyun.cn\ndate: ${date}\nGET /v2/iat HTTP/1.1`;
  
  // 使用API Secret进行HMAC-SHA256签名
  const signature = crypto
    .createHmac('sha256', XF_API_SECRET)
    .update(signatureOrigin)
    .digest('base64');
  
  // 构建Authorization字符串 - 修正格式
  const authorizationOrigin = `hmac username="${XF_API_KEY}", algorithm="${algorithm}", headers="host date request-line", signature="${signature}"`;
  const authorization = Buffer.from(authorizationOrigin).toString('base64');
  
  return {
    authorization,
    date,
    host: 'ws-api.xfyun.cn'
  };
};

/**
 * 使用科大讯飞语音识别API将音频转换为文字
 * @param {string} audioBase64 Base64编码的音频数据
 * @param {string} audioFormat 音频格式 (wav, mp3, pcm等)
 * @returns {Promise<string>} 识别后的文字
 */
const speechToText = async (audioBase64, audioFormat = 'wav') => {
  return new Promise((resolve, reject) => {
    try {
      // 验证必要的配置
      if (!XF_APP_ID || !XF_API_SECRET || !XF_API_KEY) {
        reject(new Error('科大讯飞配置不完整，请检查环境变量 XF_APP_ID, XF_API_SECRET, XF_API_KEY'));
        return;
      }

      const authParams = generateAuthParams();
      
      // 构建WebSocket连接URL - 修正为正确的域名
      const wsUrl = `wss://ws-api.xfyun.cn/v2/iat?authorization=${encodeURIComponent(authParams.authorization)}&date=${encodeURIComponent(authParams.date)}&host=${encodeURIComponent(authParams.host)}`;
      
      console.log('连接科大讯飞语音识别API...');
      
      const ws = new WebSocket(wsUrl);
      let resultText = '';
      
      ws.on('open', () => {
        console.log('WebSocket连接已建立');
        
        // 提取纯Base64数据
        const base64Data = audioBase64.startsWith('data:audio/') 
          ? audioBase64.replace(/^data:audio\/[^;]+;base64,/, '')
          : audioBase64;
        
        // 根据格式设置正确的format参数
        let formatParam;
        switch (audioFormat.toLowerCase()) {
          case 'wav':
            formatParam = 'audio/L16;rate=16000';
            break;
          case 'mp3':
            formatParam = 'audio/mpeg';
            break;
          case 'pcm':
            formatParam = 'audio/L16;rate=16000';
            break;
          default:
            formatParam = 'audio/L16;rate=16000';
        }
        
        // 发送音频数据
        const params = {
          common: {
            app_id: XF_APP_ID
          },
          business: {
            language: 'zh_cn',      // 中文
            domain: 'iat',          // 通用语音识别
            accent: 'mandarin',     // 普通话
            vad_eos: 10000,        // 静音检测
            dwa: 'wpgs'            // 动态修正
          },
          data: {
            status: 2,             // 2表示一次性发送完整音频
            format: formatParam,   // 修正格式参数
            encoding: 'raw',
            audio: base64Data
          }
        };
        
        console.log('发送音频数据，格式:', formatParam);
        ws.send(JSON.stringify(params));
      });
      
      ws.on('message', (data) => {
        try {
          const result = JSON.parse(data.toString());
          console.log('收到响应:', JSON.stringify(result, null, 2));
          
          if (result.code !== 0) {
            reject(new Error(`科大讯飞API错误: ${result.message || '未知错误'} (code: ${result.code})`));
            return;
          }
          
          if (result.data && result.data.result) {
            const wsResult = result.data.result.ws;
            if (wsResult && wsResult.length > 0) {
              wsResult.forEach(item => {
                if (item.cw && item.cw.length > 0) {
                  item.cw.forEach(word => {
                    if (word.w) {
                      resultText += word.w;
                    }
                  });
                }
              });
            }
          }
          
          // 如果是最后一帧数据
          if (result.data && result.data.status === 2) {
            ws.close();
            console.log('识别完成，结果:', resultText);
            resolve(resultText.trim());
          }
        } catch (parseError) {
          reject(new Error(`解析响应数据失败: ${parseError.message}`));
        }
      });
      
      ws.on('error', (error) => {
        console.error('WebSocket错误:', error);
        reject(new Error(`WebSocket连接错误: ${error.message}`));
      });
      
      ws.on('close', (code, reason) => {
        console.log(`WebSocket连接已关闭，代码: ${code}, 原因: ${reason}`);
        if (code !== 1000 && !resultText) {
          reject(new Error(`WebSocket异常关闭: ${code} - ${reason}`));
        }
      });
      
      // 设置超时
      setTimeout(() => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.close();
          reject(new Error('语音识别超时'));
        }
      }, 30000); // 30秒超时
      
    } catch (error) {
      reject(new Error(`创建WebSocket连接失败: ${error.message}`));
    }
  });
};

/**
 * 验证音频格式是否支持
 * @param {string} format 音频格式 (wav, mp3, pcm等)
 * @returns {boolean} 是否支持
 */
const isSupportedAudioFormat = (format) => {
  const supportedFormats = ['wav', 'mp3', 'pcm', 'webm', 'ogg'];
  return supportedFormats.includes(format.toLowerCase());
};

module.exports = {
  speechToText,
  isSupportedAudioFormat,
  generateAuthParams
};
