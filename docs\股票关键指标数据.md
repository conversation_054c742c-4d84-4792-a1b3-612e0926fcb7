# 财务数据导入使用说明

## 概述

本脚本用于将 `data_gp/企业关键指标年度.csv` 文件中的财务数据导入到 MongoDB 数据库中。

## 文件说明

### 脚本文件
- `server/scripts/importFinancialData.js` - 主导入脚本
- `server/scripts/testFinancialData.js` - 数据验证和测试脚本

### 数据模型
- `server/models/FinancialData.js` - 财务数据的 MongoDB 模型

## 使用方法

### 1. 环境准备

确保已安装必要的依赖：
```bash
cd server
npm install
```

确保 MongoDB 服务正在运行，并且 `.env` 文件中的 `MONGODB_URI` 配置正确。

### 2. 导入数据

```bash
# 方法1: 使用 npm script
npm run import-financial

# 方法2: 直接运行脚本
node scripts/importFinancialData.js
```

### 3. 验证导入结果

```bash
# 方法1: 使用 npm script
npm run test-financial

# 方法2: 直接运行脚本
node scripts/testFinancialData.js
```

## 数据处理特性

### 1. 证券代码处理
- 自动将证券代码补零到6位（例如：1 → 000001）
- 保持字符串格式，避免前导零丢失

### 2. 数值处理
- **带单位数值**: "6.29亿" → 629000000 (转换为元)
- **百分比**: "14.92%" → 0.1492 (转换为小数)
- **False值**: "False" → null
- **空值**: "" → null

### 3. 字段映射
CSV文件的中文字段名会自动映射到模型的英文字段名：

| CSV字段名 | 模型字段名 | 数据类型 |
|-----------|------------|----------|
| 净利润 | data.keyMetrics.netProfit | FinancialValue |
| 净利润同比增长率 | data.keyMetrics.netProfitGrowthRate | Number |
| 营业总收入 | data.keyMetrics.totalRevenue | FinancialValue |
| 基本每股收益 | data.keyMetrics.basicEarningsPerShare | FinancialValue |
| 净资产收益率 | data.keyMetrics.returnOnEquity | Number |
| ... | ... | ... |

### 4. 批量导入
- 每批处理100条记录
- 支持部分失败继续导入
- 自动处理重复数据（基于股票代码+年度+报表类型的唯一索引）

## 数据验证

导入完成后，验证脚本会显示：

1. **基础统计**
   - 总记录数
   - 股票数量
   - 年份范围

2. **数据完整性**
   - 各关键字段的数据覆盖率
   - 数据最丰富的股票

3. **样本数据**
   - 显示几条示例记录
   - 展示格式化后的财务指标

## 错误处理

### 常见问题

1. **MongoDB连接失败**
   - 检查 MongoDB 服务是否启动
   - 验证 `.env` 文件中的 `MONGODB_URI` 配置

2. **CSV文件不存在**
   - 确保 `data_gp/企业关键指标年度.csv` 文件存在
   - 检查文件路径是否正确

3. **重复数据错误**
   - 系统会自动跳过重复记录（相同股票代码+年度+报表类型）
   - 这是正常现象，不影响导入

4. **数据格式错误**
   - 脚本会跳过无法解析的行
   - 详细错误信息会在控制台显示

## 性能说明

- 文件大小: ~88,000 行数据
- 预计导入时间: 5-15分钟（取决于硬件性能）
- 内存使用: 适中（批量处理）

## 数据库索引

模型自动创建以下索引以优化查询性能：
- 复合唯一索引: `stockCode + fiscalYear + reportType`
- 普通索引: `stockCode`, `fiscalYear`, `reportDate`, `reportType`

## 注意事项

1. **数据一致性**: 导入前建议备份现有数据
2. **重复运行**: 可以安全地重复运行导入脚本，重复数据会被跳过
3. **数据更新**: 如需更新现有数据，需要先删除对应记录再重新导入
4. **内存管理**: 大文件导入时建议确保足够的可用内存

## 故障排除

如果导入过程中遇到问题：

1. 查看控制台输出的详细错误信息
2. 检查 MongoDB 日志
3. 运行测试脚本验证已导入的数据
4. 如有必要，清空集合后重新导入

```bash
# 清空财务数据集合（谨慎操作）
# 在 MongoDB shell 中执行：
# use your_database_name
# db.financial_data.deleteMany({})
```
