const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const mongoose = require('mongoose');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// 导入财务数据模型
const FinancialData = require('../src/models/FinancialData');

/**
 * 连接MongoDB数据库
 */
async function connectDatabase() {
  try {
    const mongoUri = process.env.MONGODB_URI;
    if (!mongoUri) {
      throw new Error('MONGODB_URI 环境变量未设置');
    }
    
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('MongoDB数据库连接成功');
  } catch (error) {
    console.error('MongoDB数据库连接失败:', error);
    throw error;
  }
}

/**
 * 检查并去除BOM标记
 * @param {string} filePath - 文件路径
 * @returns {Promise<void>}
 */
async function removeBOMIfExists(filePath) {
  const buffer = fs.readFileSync(filePath);
  
  // 检查是否有UTF-8 BOM (EF BB BF)
  if (buffer.length >= 3 && buffer[0] === 0xEF && buffer[1] === 0xBB && buffer[2] === 0xBF) {
    console.log(`检测到BOM标记，正在处理文件: ${filePath}`);
    const contentWithoutBOM = buffer.slice(3);
    fs.writeFileSync(filePath, contentWithoutBOM);
    console.log(`BOM标记已移除: ${filePath}`);
  }
}

/**
 * 解析数值，处理中文单位
 * @param {string} value - 待解析的值
 * @returns {number|null} - 解析后的数值
 */
function parseFinancialValue(value) {
  if (!value || value === 'False' || value === '' || value === 'null') {
    return null;
  }

  // 如果是纯数字
  if (!isNaN(parseFloat(value)) && isFinite(value)) {
    return parseFloat(value);
  }

  // 处理带单位的数值
  const str = value.toString().trim();
  
  // 处理亿、万等单位
  if (str.includes('亿')) {
    const numStr = str.replace('亿', '').trim();
    if (!isNaN(parseFloat(numStr))) {
      return parseFloat(numStr);
    }
  }
  
  if (str.includes('万')) {
    const numStr = str.replace('万', '').trim();
    if (!isNaN(parseFloat(numStr))) {
      return parseFloat(numStr) * 0.0001;
    }
  }

  // 处理百分比
  if (str.includes('%')) {
    const numStr = str.replace('%', '').trim();
    if (!isNaN(parseFloat(numStr))) {
      return parseFloat(numStr);
    }
  }

  // 如果包含中文但不是亿万，可能是其他单位
  if (/[\u4e00-\u9fa5]/.test(str)) {
    return null;
  }

  // 尝试解析为数字
  const numValue = parseFloat(str);
  if (!isNaN(numValue) && isFinite(numValue)) {
    return numValue;
  }

  return null;
}

/**
 * 解析百分比数值
 * @param {string} value - 待解析的百分比值
 * @returns {number|null} - 解析后的小数值
 */
function parsePercentage(value) {
  if (!value || value === 'False' || value === '' || value === 'null') {
    return null;
  }

  const str = value.toString().trim();
  if (str.includes('%')) {
    const numStr = str.replace('%', '').trim();
    if (!isNaN(parseFloat(numStr))) {
      return parseFloat(numStr);
    }
  }

  // 直接是数字的情况
  if (!isNaN(parseFloat(str)) && isFinite(str)) {
    const num = parseFloat(str);
    // 如果数值大于1，可能是百分比形式但没有%符号
    if (num > 1) {
      return num;
    }
    return num;
  }

  return null;
}

/**
 * 解析报告期，提取年份
 * @param {string} reportPeriod - 报告期
 * @param {string} reportType - 报表类型
 * @returns {object} - { fiscalYear: number, reportDate: Date }
 */
function parseReportPeriod(reportPeriod, reportType) {
  if (!reportPeriod) {
    throw new Error('报告期不能为空');
  }

  const periodStr = reportPeriod.toString().trim();
  
  if (reportType === '按年度') {
    // 按年度：报告期只有年份，如"1996"
    const year = parseInt(periodStr);
    if (isNaN(year) || year < 1900 || year > 2100) {
      throw new Error(`无效的年份: ${periodStr}`);
    }
    return {
      fiscalYear: year,
      reportDate: new Date(year, 11, 31) // 12月31日
    };
  } else {
    // 按报告期、按单季度：报告期是完整日期，如"1996-12-31"
    let dateStr = periodStr;
    
    // 处理可能的日期格式
    if (dateStr.includes('-')) {
      const dateParts = dateStr.split('-');
      if (dateParts.length === 3) {
        const year = parseInt(dateParts[0]);
        const month = parseInt(dateParts[1]);
        const day = parseInt(dateParts[2]);
        
        if (isNaN(year) || isNaN(month) || isNaN(day)) {
          throw new Error(`无效的日期格式: ${dateStr}`);
        }
        
        return {
          fiscalYear: year,
          reportDate: new Date(year, month - 1, day)
        };
      }
    }
    
    throw new Error(`无法解析报告期: ${periodStr}`);
  }
}

/**
 * 转换CSV行数据为FinancialData文档
 * @param {object} row - CSV行数据
 * @returns {object} - FinancialData文档对象
 */
function transformRowToFinancialData(row) {
  try {
    // 确保证券代码格式正确（补齐6位，保持字符串格式）
    let stockCode = row['证券代码'];
    if (stockCode) {
      stockCode = stockCode.toString().padStart(6, '0');
    }

    const reportType = row['获取方式'] || '按年度';
    const { fiscalYear, reportDate } = parseReportPeriod(row['报告期'], reportType);

    // 构建财务数据对象
    const financialData = {
      stockCode: stockCode,
      reportType: reportType,
      fiscalYear: fiscalYear,
      reportDate: reportDate,
      data: {
        keyMetrics: {
          // 盈利能力指标
          netProfit: parseFinancialValue(row['净利润']),
          netProfitGrowthRate: parsePercentage(row['净利润同比增长率']),
          nonRecurringNetProfit: parseFinancialValue(row['扣非净利润']),
          nonRecurringNetProfitGrowthRate: parsePercentage(row['扣非净利润同比增长率']),
          totalRevenue: parseFinancialValue(row['营业总收入']),
          totalRevenueGrowthRate: parsePercentage(row['营业总收入同比增长率']),
          netProfitMargin: parsePercentage(row['销售净利率']),
          grossProfitMargin: parsePercentage(row['销售毛利率']),
          
          // 每股指标
          basicEarningsPerShare: parseFinancialValue(row['基本每股收益']),
          bookValuePerShare: parseFinancialValue(row['每股净资产']),
          capitalReservePerShare: parseFinancialValue(row['每股资本公积金']),
          undistributedProfitPerShare: parseFinancialValue(row['每股未分配利润']),
          operatingCashFlowPerShare: parseFinancialValue(row['每股经营现金流']),
          
          // 盈利质量指标
          returnOnEquity: parsePercentage(row['净资产收益率']),
          dilutedReturnOnEquity: parsePercentage(row['净资产收益率-摊薄']),
          
          // 偿债能力指标
          currentRatio: parseFloat(row['流动比率']) || null,
          quickRatio: parseFloat(row['速动比率']) || null,
          conservativeQuickRatio: parseFloat(row['保守速动比率']) || null,
          equityRatio: parseFloat(row['产权比率']) || null,
          debtToAssetRatio: parsePercentage(row['资产负债率']),
          
          // 营运能力指标
          operatingCycle: parseFloat(row['营业周期']) || null,
          inventoryTurnoverRatio: parseFloat(row['存货周转率']) || null,
          inventoryTurnoverDays: parseFloat(row['存货周转天数']) || null,
          accountsReceivableTurnoverDays: parseFloat(row['应收账款周转天数']) || null
        }
      }
    };

    return financialData;
  } catch (error) {
    console.error('转换行数据时出错:', error.message);
    console.error('问题行数据:', row);
    throw error;
  }
}

/**
 * 处理单个CSV文件
 * @param {string} filePath - CSV文件路径
 * @returns {Promise<number>} - 导入的记录数
 */
async function processCSVFile(filePath) {
  console.log(`开始处理文件: ${filePath}`);
  
  // 检查并移除BOM
  await removeBOMIfExists(filePath);
  
  return new Promise((resolve, reject) => {
    const results = [];
    let processedCount = 0;
    let errorCount = 0;

    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (row) => {
        try {
          const financialData = transformRowToFinancialData(row);
          results.push(financialData);
          processedCount++;
          
          if (processedCount % 1000 === 0) {
            console.log(`已处理 ${processedCount} 条记录...`);
          }
        } catch (error) {
          errorCount++;
          console.error(`第 ${processedCount + errorCount} 行数据处理失败:`, error.message);
        }
      })
      .on('end', async () => {
        try {
          console.log(`文件解析完成: ${filePath}`);
          console.log(`成功解析 ${results.length} 条记录，失败 ${errorCount} 条`);
          
          if (results.length > 0) {
            // 批量插入数据
            console.log('开始批量导入数据库...');
            
            // 使用 insertMany 的 ordered: false 选项，即使有重复也继续插入其他数据
            try {
              const insertResult = await FinancialData.insertMany(results, { 
                ordered: false,
                rawResult: true 
              });
              console.log(`成功导入 ${insertResult.insertedCount} 条记录到数据库`);
              resolve(insertResult.insertedCount);
            } catch (bulkError) {
              // 处理批量插入中的重复键错误
              if (bulkError.code === 11000) {
                const insertedCount = bulkError.result ? bulkError.result.nInserted : 0;
                const duplicateCount = results.length - insertedCount;
                console.log(`导入完成: 成功 ${insertedCount} 条，跳过重复 ${duplicateCount} 条`);
                resolve(insertedCount);
              } else {
                throw bulkError;
              }
            }
          } else {
            console.log('没有有效数据需要导入');
            resolve(0);
          }
        } catch (error) {
          reject(error);
        }
      })
      .on('error', (error) => {
        reject(error);
      });
  });
}

/**
 * 获取指定目录下的所有CSV文件
 * @param {string} dirPath - 目录路径
 * @returns {Array<string>} - CSV文件路径数组
 */
function getCSVFiles(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.warn(`目录不存在: ${dirPath}`);
    return [];
  }
  
  const files = fs.readdirSync(dirPath);
  return files
    .filter(file => file.endsWith('.csv'))
    .map(file => path.join(dirPath, file));
}

/**
 * 主导入函数
 */
async function importFinancialData() {
  try {
    console.log('=== 财务数据导入工具启动 ===');
    
    // 连接数据库
    await connectDatabase();
    
    // 定义数据源目录
    const dataRoot = path.join(__dirname, '../../data_gp');
    const directories = [
      { path: path.join(dataRoot, '企业关键指标年度_分拆后'), name: '企业关键指标年度_分拆后' },
      // { path: path.join(dataRoot, '企业关键指标季度'), name: '企业关键指标季度' },
      // { path: path.join(dataRoot, '企业关键指标报告期'), name: '企业关键指标报告期' }
    ];
    
    let totalImported = 0;
    
    // 处理每个目录
    for (const dir of directories) {
      console.log(`\n=== 处理 ${dir.name} 目录 ===`);
      
      const csvFiles = getCSVFiles(dir.path);
      console.log(`找到 ${csvFiles.length} 个CSV文件`);
      
      for (const filePath of csvFiles) {
        try {
          const importedCount = await processCSVFile(filePath);
          totalImported += importedCount;
          console.log(`文件 ${path.basename(filePath)} 处理完成，导入 ${importedCount} 条记录\n`);
        } catch (error) {
          console.error(`处理文件 ${filePath} 时出错:`, error.message);
        }
      }
    }
    
    console.log(`\n=== 导入完成 ===`);
    console.log(`总计导入 ${totalImported} 条财务数据记录`);
    
    // 显示数据库统计信息
    const totalCount = await FinancialData.countDocuments();
    const reportTypeCounts = await FinancialData.aggregate([
      { $group: { _id: '$reportType', count: { $sum: 1 } } }
    ]);
    
    console.log(`\n数据库统计:`);
    console.log(`- 总记录数: ${totalCount}`);
    reportTypeCounts.forEach(item => {
      console.log(`- ${item._id}: ${item.count} 条`);
    });
    
  } catch (error) {
    console.error('导入过程中发生错误:', error);
  } finally {
    // 关闭数据库连接
    await mongoose.connection.close();
    console.log('数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  importFinancialData()
    .then(() => {
      console.log('财务数据导入工具执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('财务数据导入工具执行失败:', error);
      process.exit(1);
    });
}

module.exports = {
  importFinancialData,
  processCSVFile,
  parseFinancialValue,
  parsePercentage,
  parseReportPeriod
};
