const { success, error } = require('../../utils/response');
const { parseFilterCondition } = require('../../services/filter/parseFilterConditionService');

/**
 * 解析用户自然语言筛选条件
 * @route POST /api/v1/filter/parse-condition
 * @access Private
 */
const parseFilterConditionController = async (req, res, next) => {
  try {
    const { userInput } = req.body;
    
    console.log('收到解析筛选条件请求:', userInput);
    
    // 调用解析服务
    const filterConditions = await parseFilterCondition(userInput);
    
    console.log('解析结果:', JSON.stringify(filterConditions, null, 2));
    
    return success(res, 200, '筛选条件解析成功', {
      userInput,
      filterConditions
    });
    
  } catch (err) {
    console.error('解析筛选条件失败:', err);
    next(err);
  }
};

module.exports = {
  parseFilterConditionController
};
