const mongoose = require('mongoose');
const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });
const DividendData = require('../src/models/DividendData');

// 连接MongoDB数据库
const connectDB = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI;
    if (!mongoUri) {
      throw new Error('MONGODB_URI 环境变量未设置');
    }
    
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ 成功连接到MongoDB数据库');
    console.log(`📊 数据库地址: ${mongoUri}`);
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  }
};

// 检测并移除BOM
const removeBOM = (filePath) => {
  const data = fs.readFileSync(filePath);
  if (data[0] === 0xEF && data[1] === 0xBB && data[2] === 0xBF) {
    console.log('检测到BOM，正在移除...');
    const cleanData = data.slice(3);
    fs.writeFileSync(filePath, cleanData);
    console.log('BOM已移除');
    return true;
  }
  return false;
};

// 解析日期字符串
const parseDate = (dateStr) => {
  if (!dateStr || dateStr.trim() === '' || dateStr === 'null') {
    return null;
  }
  
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) {
      return null;
    }
    return date;
  } catch (error) {
    return null;
  }
};

// 解析数值
const parseNumber = (numStr) => {
  if (!numStr || numStr.trim() === '' || numStr === 'null') {
    return null;
  }
  
  const num = parseFloat(numStr);
  return isNaN(num) ? null : num;
};

// 格式化股票代码（确保6位数字格式）
const formatStockCode = (code) => {
  if (!code) return null;
  
  // 转换为字符串并去除空格
  let formattedCode = String(code).trim();
  
  // 如果是纯数字，补齐到6位
  if (/^\d+$/.test(formattedCode)) {
    formattedCode = formattedCode.padStart(6, '0');
  }
  
  return formattedCode;
};

// 数据清洗和转换函数
const transformRowData = (row) => {
  return {
    stockCode: formatStockCode(row['证券代码']),
    reportPeriod: parseDate(row['报告期']),
    performanceDisclosureDate: parseDate(row['业绩披露日期']),
    totalStockTransferRatio: parseNumber(row['送转股份-送转总比例']),
    stockBonusRatio: parseNumber(row['送转股份-送股比例']),
    stockTransferRatio: parseNumber(row['送转股份-转股比例']),
    cashDividendRatio: parseNumber(row['现金分红-现金分红比例']),
    cashDividendDescription: row['现金分红-现金分红比例描述'] || null,
    dividendYield: parseNumber(row['现金分红-股息率']),
    earningsPerShare: parseNumber(row['每股收益']),
    bookValuePerShare: parseNumber(row['每股净资产']),
    reserveFundPerShare: parseNumber(row['每股公积金']),
    undistributedProfitPerShare: parseNumber(row['每股未分配利润']),
    netProfitGrowthYoY: parseNumber(row['净利润同比增长']),
    totalShareCapital: parseNumber(row['总股本']),
    proposalAnnouncementDate: parseDate(row['预案公告日']),
    equityRegistrationDate: parseDate(row['股权登记日']),
    exDividendDate: parseDate(row['除权除息日']),
    implementationStatus: row['方案进度'] || null,
    latestAnnouncementDate: parseDate(row['最新公告日期'])
  };
};

// 验证数据
const validateData = (data) => {
  const errors = [];
  
  if (!data.stockCode) {
    errors.push('证券代码不能为空');
  }
  
  if (!data.reportPeriod) {
    errors.push('报告期不能为空');
  }
  
  return errors;
};

// 读取CSV数据
const readCSVData = async (csvFilePath) => {
  return new Promise((resolve, reject) => {
    const dividendData = [];
    const errors = [];
    let processedCount = 0;
    
    console.log('🔄 开始读取CSV文件...');
    
    fs.createReadStream(csvFilePath)
      .pipe(csv())
      .on('data', (row) => {
        try {
          processedCount++;
          
          // 转换数据
          const transformedData = transformRowData(row);
          
          // 验证数据
          const validationErrors = validateData(transformedData);
          if (validationErrors.length > 0) {
            errors.push({
              row: processedCount,
              data: row,
              errors: validationErrors
            });
            return;
          }
          
          dividendData.push(transformedData);
          
          // 每处理1000条数据输出进度
          if (processedCount % 1000 === 0) {
            console.log(`已读取 ${processedCount} 条数据...`);
          }
          
        } catch (error) {
          errors.push({
            row: processedCount,
            data: row,
            error: error.message
          });
        }
      })
      .on('end', () => {
        console.log(`✅ CSV读取完成，共读取 ${processedCount} 条数据，有效数据 ${dividendData.length} 条`);
        resolve({ dividendData, errors });
      })
      .on('error', (error) => {
        reject(error);
      });
  });
};

// 批量插入数据到数据库
const insertDividendDataToDB = async (dividendDataList) => {
  try {
    console.log('🔄 开始插入数据到数据库...');
    
    let insertedCount = 0;
    let updatedCount = 0;
    let errorCount = 0;
    const batchSize = 100; // 批量处理大小
    
    for (let i = 0; i < dividendDataList.length; i += batchSize) {
      const batch = dividendDataList.slice(i, i + batchSize);
      
      try {
        // 批量处理每个数据项
        for (const data of batch) {
          try {
            // 检查是否已存在（防止重复导入）
            const existing = await DividendData.findOne({
              stockCode: data.stockCode,
              reportPeriod: data.reportPeriod
            });
            
            if (existing) {
              // 更新现有记录
              await DividendData.findOneAndUpdate(
                {
                  stockCode: data.stockCode,
                  reportPeriod: data.reportPeriod
                },
                data,
                { upsert: true }
              );
              updatedCount++;
            } else {
              // 创建新记录
              const dividendData = new DividendData(data);
              await dividendData.save();
              insertedCount++;
            }
          } catch (error) {
            console.warn(`⚠️ 处理数据失败 [${data.stockCode}-${data.reportPeriod}]: ${error.message}`);
            errorCount++;
          }
        }
        
        // 显示进度
        if ((i + batchSize) % 1000 === 0 || i + batchSize >= dividendDataList.length) {
          console.log(`已处理 ${Math.min(i + batchSize, dividendDataList.length)}/${dividendDataList.length} 条数据...`);
        }
        
      } catch (error) {
        console.error(`❌ 批量处理失败:`, error.message);
        errorCount += batch.length;
      }
    }
    
    console.log(`🎉 数据插入完成!`);
    console.log(`📊 新增: ${insertedCount} 条, 更新: ${updatedCount} 条, 失败: ${errorCount} 条`);
    
    return { insertedCount, updatedCount, errorCount };
    
  } catch (error) {
    console.error('❌ 数据插入失败:', error);
    throw error;
  }
};

// 导入数据主函数
const importDividendData = async () => {
  const csvFilePath = path.join(__dirname, '../../data_gp/分红配股数据汇总.csv');
  
  try {
    console.log('🚀 开始导入分红配股数据...');
    console.log(`📁 CSV文件路径: ${csvFilePath}`);
    
    // 检查文件是否存在
    if (!fs.existsSync(csvFilePath)) {
      throw new Error(`CSV文件不存在: ${csvFilePath}`);
    }
    
    // 检测并移除BOM
    removeBOM(csvFilePath);
    
    // 连接数据库
    await connectDB();
    
    // 查询现有数据条数
    const existingCount = await DividendData.countDocuments();
    console.log(`现有数据条数: ${existingCount}`);
    
    // 读取CSV数据
    const { dividendData, errors: readErrors } = await readCSVData(csvFilePath);
    
    if (dividendData.length === 0) {
      console.log('⚠️ 没有有效的分红数据可导入');
      return;
    }
    
    // 插入数据到数据库
    const { insertedCount, updatedCount, errorCount } = await insertDividendDataToDB(dividendData);
    
    // 保存错误日志
    if (readErrors.length > 0) {
      const errorLogPath = path.join(__dirname, '../logs/import_dividend_errors.json');
      
      // 确保logs目录存在
      const logsDir = path.dirname(errorLogPath);
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
      }
      
      fs.writeFileSync(errorLogPath, JSON.stringify(readErrors, null, 2));
      console.log(`\n📝 错误日志已保存到: ${errorLogPath}`);
      console.log('前5个错误:');
      readErrors.slice(0, 5).forEach((error, index) => {
        console.log(`${index + 1}. 第${error.row}行: ${error.errors || error.error}`);
      });
    }
    
    // 显示数据统计
    await showDataStatistics();
    
    const totalCount = await DividendData.countDocuments();
    
    console.log('\n🎉 数据导入任务完成!');
    
    return {
      totalProcessed: dividendData.length,
      newRecords: insertedCount,
      duplicates: updatedCount,
      errors: errorCount + readErrors.length,
      totalInDB: totalCount
    };
    
  } catch (error) {
    console.error('❌ 导入过程中发生错误:', error);
    throw error;
  } finally {
    // 关闭数据库连接
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.close();
      console.log('📴 数据库连接已关闭');
    }
  }
};

// 显示数据统计信息
const showDataStatistics = async () => {
  try {
    console.log('\n=== 数据统计 ===');
    
    // 按年份统计
    const yearStats = await DividendData.aggregate([
      {
        $group: {
          _id: { $year: '$reportPeriod' },
          count: { $sum: 1 },
          avgDividendYield: { $avg: '$dividendYield' },
          avgCashDividend: { $avg: '$cashDividendRatio' }
        }
      },
      { $sort: { _id: -1 } },
      { $limit: 5 }
    ]);
    
    console.log('最近5年数据统计:');
    yearStats.forEach(stat => {
      console.log(`${stat._id}年: ${stat.count}条记录, 平均股息率: ${(stat.avgDividendYield * 100 || 0).toFixed(2)}%, 平均现金分红: ${(stat.avgCashDividend || 0).toFixed(2)}`);
    });
    
    // 股票数量统计
    const stockCount = await DividendData.distinct('stockCode').then(codes => codes.length);
    console.log(`\n包含股票数量: ${stockCount}只`);
    
    // 分红类型统计
    const cashDividendCount = await DividendData.countDocuments({ 
      cashDividendRatio: { $gt: 0 } 
    });
    const stockTransferCount = await DividendData.countDocuments({ 
      totalStockTransferRatio: { $gt: 0 } 
    });
    
    console.log(`现金分红记录: ${cashDividendCount}条`);
    console.log(`送转股记录: ${stockTransferCount}条`);
    
  } catch (error) {
    console.error('统计信息获取失败:', error);
  }
};

// 如果直接运行此脚本
if (require.main === module) {
  importDividendData()
    .then((result) => {
      console.log('\n导入成功!');
      console.log('导入结果:', result);
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n导入失败:', error);
      process.exit(1);
    });
}

module.exports = { 
  importDividendData, 
  transformRowData, 
  validateData, 
  readCSVData, 
  insertDividendDataToDB,
  connectDB 
};
