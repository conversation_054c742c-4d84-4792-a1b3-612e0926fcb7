# -*- coding: utf-8 -*-
"""
股票数据完整处理脚本
功能：
1. 获取沪深A股实时数据并保存
2. 分析数据差异并更新板块信息
3. 通过雪球API获取详细财务指标并合并数据
"""

import sys
import os

# 设置标准输出编码为UTF-8
if sys.stdout.encoding != 'utf-8':
    sys.stdout.reconfigure(encoding='utf-8')
if sys.stderr.encoding != 'utf-8':
    sys.stderr.reconfigure(encoding='utf-8')

# 设置环境变量
os.environ['PYTHONIOENCODING'] = 'utf-8'

import akshare as ak
import pandas as pd
from datetime import datetime
import time

# 禁用akshare的进度条显示，减少stderr输出
import warnings
warnings.filterwarnings('ignore')

import akshare as ak
import pandas as pd
from datetime import datetime
import os
import time

class StockDataProcessor:
    def __init__(self):
        self.all_data = pd.DataFrame()
        self.listed_companies = pd.DataFrame()
        self.daily_stock_data = pd.DataFrame()
        # 修改文件路径为后端相对路径
        self.base_path = os.path.dirname(os.path.abspath(__file__))
        self.csv_path = os.path.join(self.base_path, '..', 'csv')
        
    def get_all_stock_data(self):
        """
        获取四个板块的股票数据并整合
        """
        print("开始获取股票数据...")
        
        # 初始化一个空的DataFrame来存储所有数据
        all_data = pd.DataFrame()
        
        # 定义板块信息
        board_info = [
            {"name": "沪市A股", "func": ak.stock_sh_a_spot_em},
            {"name": "深市A股", "func": ak.stock_sz_a_spot_em},
        ]
        
        # 逐个获取各板块数据
        for board in board_info:
            try:
                print(f"正在获取{board['name']}数据...")
                
                # 调用对应的API
                df = board['func']()
                
                if not df.empty:
                    # 添加板块字段
                    df['板块'] = board['name']
                    
                    # 删除序号字段
                    if '序号' in df.columns:
                        df = df.drop('序号', axis=1)
                    
                    # 重命名字段
                    df = df.rename(columns={
                        '代码': '证券代码',
                        '名称': '证券简称'
                    })
                    
                    # 调整字段顺序，将板块放在证券简称和最新价之间
                    cols = df.columns.tolist()
                    
                    # 找到证券简称和最新价的位置
                    name_idx = cols.index('证券简称')
                    price_idx = cols.index('最新价')
                    
                    # 移除板块字段从原位置
                    cols.remove('板块')
                    
                    # 在证券简称后插入板块字段
                    cols.insert(name_idx + 1, '板块')
                    
                    # 重新排列DataFrame
                    df = df[cols]
                    
                    # 添加到总数据中
                    all_data = pd.concat([all_data, df], ignore_index=True)
                    
                    print(f"{board['name']}数据获取成功，共{len(df)}条记录")
                    
                else:
                    print(f"警告：{board['name']}数据为空")
                    
            except Exception as e:
                print(f"获取{board['name']}数据时出错：{str(e)}")
                continue
        
        self.all_data = all_data
        return all_data

    def save_to_csv(self, data, filename="当日股票数据.csv"):
        """
        保存数据到CSV文件
        """
        try:
            # 使用csv路径
            filepath = os.path.join(self.csv_path, filename)
            data.to_csv(filepath, index=False, encoding='utf-8-sig')
            print(f"数据已成功保存到 {filepath}")
            print(f"总共保存了 {len(data)} 条股票数据")
            
            # 显示各板块的数据统计
            if '板块' in data.columns:
                board_counts = data['板块'].value_counts()
                print("\n各板块数据统计：")
                for board, count in board_counts.items():
                    print(f"  {board}: {count} 条")
                    
        except Exception as e:
            print(f"保存文件时出错：{str(e)}")

    def analyze_and_update_stock_data(self):
        """
        分析并更新股票数据的脚本
        """
        
        # 读取CSV文件
        try:
            listed_companies_path = os.path.join(self.csv_path, '沪深上市公司列表.csv')
            daily_stock_data_path = os.path.join(self.csv_path, '当日股票数据.csv')
            
            self.listed_companies = pd.read_csv(listed_companies_path)
            self.daily_stock_data = pd.read_csv(daily_stock_data_path)
            print("成功读取CSV文件")
        except FileNotFoundError as e:
            print(f"文件未找到: {e}")
            return None
        except Exception as e:
            print(f"读取文件时出错: {e}")
            return None
        
        # 确保证券代码列为字符串类型（避免前导零丢失）
        self.listed_companies['证券代码'] = self.listed_companies['证券代码'].astype(str).str.zfill(6)
        self.daily_stock_data['证券代码'] = self.daily_stock_data['证券代码'].astype(str).str.zfill(6)
        
        # 获取证券代码集合
        listed_codes = set(self.listed_companies['证券代码'])
        daily_codes = set(self.daily_stock_data['证券代码'])
        
        print("="*60)
        print("数据分析报告")
        print("="*60)
        
        # 1. 检查沪深上市公司列表中的代码是否都在当日股票数据中
        print("\n1. 沪深上市公司列表中但不在当日股票数据中的证券代码:")
        missing_in_daily = listed_codes - daily_codes
        if missing_in_daily:
            print(f"   共 {len(missing_in_daily)} 个:")
            for code in sorted(missing_in_daily):
                company_name = self.listed_companies[self.listed_companies['证券代码'] == code]['证券简称'].iloc[0]
                print(f"   {code} - {company_name}")
        else:
            print("   无缺失")
        
        # 2. 检查当日股票数据中的代码是否都在沪深上市公司列表中
        print("\n2. 当日股票数据中但不在沪深上市公司列表中的证券代码:")
        missing_in_listed = daily_codes - listed_codes
        if missing_in_listed:
            print(f"   共 {len(missing_in_listed)} 个:")
            for code in sorted(missing_in_listed):
                company_name = self.daily_stock_data[self.daily_stock_data['证券代码'] == code]['证券简称'].iloc[0]
                print(f"   {code} - {company_name}")
        else:
            print("   无缺失")
        
        # 3. 检查两个文件中都有的证券代码对应的证券简称是否一致
        print("\n3. 证券简称不一致的记录:")
        common_codes = listed_codes & daily_codes
        name_mismatch = []
        
        for code in common_codes:
            listed_name = self.listed_companies[self.listed_companies['证券代码'] == code]['证券简称'].iloc[0]
            daily_name = self.daily_stock_data[self.daily_stock_data['证券代码'] == code]['证券简称'].iloc[0]
            
            if listed_name != daily_name:
                name_mismatch.append({
                    '证券代码': code,
                    '上市公司列表中的简称': listed_name,
                    '当日数据中的简称': daily_name
                })
        
        if name_mismatch:
            print(f"   共 {len(name_mismatch)} 个不一致:")
            for item in name_mismatch:
                print(f"   {item['证券代码']}: 上市公司列表='{item['上市公司列表中的简称']}', 当日数据='{item['当日数据中的简称']}'")
        else:
            print("   所有证券简称都一致")
        
        # 4. 更新板块信息并生成清洗后的数据
        print("\n4. 更新板块信息并生成清洗后数据:")
        
        # 创建证券代码到板块的映射字典
        code_to_sector = dict(zip(self.listed_companies['证券代码'], self.listed_companies['板块']))
        
        # 备份原始数据并更新所有记录的板块信息
        daily_stock_data_updated = self.daily_stock_data.copy()
        
        # 统计更新情况
        updated_count = 0
        unchanged_count = 0
        not_found_count = 0
        
        for index, row in daily_stock_data_updated.iterrows():
            code = row['证券代码']
            if code in code_to_sector:
                old_sector = row['板块']
                new_sector = code_to_sector[code]
                
                if old_sector != new_sector:
                    daily_stock_data_updated.at[index, '板块'] = new_sector
                    updated_count += 1
                else:
                    unchanged_count += 1
            else:
                not_found_count += 1
        
        print(f"   板块信息更新统计:")
        print(f"   - 更新了 {updated_count} 条记录的板块信息")
        print(f"   - {unchanged_count} 条记录的板块信息无需更新")
        print(f"   - {not_found_count} 条记录因不在上市公司列表中而无法更新板块信息")
        
        # 保存更新后的完整文件
        output_filename_all = os.path.join(self.csv_path, '当日股票数据_已更新板块.csv')
        daily_stock_data_updated.to_csv(output_filename_all, index=False, encoding='utf-8-sig')
        print(f"\n   已将更新后的完整数据保存为: {output_filename_all}")
        
        # 生成清洗后的数据（只包含在沪深上市公司列表中存在的股票）
        print(f"\n   生成清洗后数据:")
        
        # 筛选出在沪深上市公司列表中存在的股票数据
        cleaned_data = daily_stock_data_updated[daily_stock_data_updated['证券代码'].isin(listed_codes)].copy()
        
        # 创建排序映射：根据沪深上市公司列表中的顺序
        listed_companies_reset = self.listed_companies.reset_index()
        code_to_order = dict(zip(listed_companies_reset['证券代码'], listed_companies_reset.index))
        
        # 为清洗后的数据添加排序列
        cleaned_data['_sort_order'] = cleaned_data['证券代码'].map(code_to_order)
        
        # 按照沪深上市公司列表的顺序排序
        cleaned_data_sorted = cleaned_data.sort_values('_sort_order').drop('_sort_order', axis=1)
        
        # 保存清洗后的数据
        output_filename_cleaned = os.path.join(self.csv_path, '当日股票数据_清洗后.csv')
        cleaned_data_sorted.to_csv(output_filename_cleaned, index=False, encoding='utf-8-sig')
        
        print(f"   - 原始当日股票数据总数: {len(self.daily_stock_data)}")
        print(f"   - 清洗后数据总数: {len(cleaned_data_sorted)}")
        print(f"   - 剔除的数据总数: {len(self.daily_stock_data) - len(cleaned_data_sorted)}")
        print(f"   - 已将清洗后的数据保存为: {output_filename_cleaned}")
        print(f"   - 清洗后数据已按沪深上市公司列表的证券代码顺序排序")
        
        # 显示清洗后数据的前几行作为预览
        if len(cleaned_data_sorted) > 0:
            print(f"\n   清洗后数据预览（前5行）:")
            print("   " + "-" * 50)
            preview_columns = ['证券代码', '证券简称', '板块']
            # 确保预览列存在
            available_columns = [col for col in preview_columns if col in cleaned_data_sorted.columns]
            if available_columns:
                for i, (_, row) in enumerate(cleaned_data_sorted[available_columns].head().iterrows()):
                    row_str = "   "
                    for col in available_columns:
                        row_str += f"{col}: {row[col]}  "
                    print(row_str)
            
            # 显示排序验证信息
            first_codes = cleaned_data_sorted['证券代码'].head(3).tolist()
            last_codes = cleaned_data_sorted['证券代码'].tail(3).tolist()
            print(f"\n   排序验证:")
            print(f"   - 前3个证券代码: {first_codes}")
            print(f"   - 后3个证券代码: {last_codes}")
        
        # 生成清洗统计报告
        self.generate_cleaning_report(self.daily_stock_data, cleaned_data_sorted, missing_in_listed, self.listed_companies)
        
        # 生成详细报告
        print("\n" + "="*60)
        print("统计摘要")
        print("="*60)
        print(f"沪深上市公司列表总数: {len(self.listed_companies)}")
        print(f"当日股票数据总数: {len(self.daily_stock_data)}")
        print(f"两个文件共同包含的证券代码数: {len(common_codes)}")
        print(f"仅在上市公司列表中的代码数: {len(missing_in_daily)}")
        print(f"仅在当日数据中的代码数: {len(missing_in_listed)}")
        print(f"证券简称不一致的数量: {len(name_mismatch)}")
        print(f"板块信息已更新的记录数: {updated_count}")
        print(f"清洗后数据记录数: {len(cleaned_data_sorted)}")
        print(f"数据清洗率: {len(cleaned_data_sorted)/len(self.daily_stock_data)*100:.2f}%")
        
        # 可选：生成详细的差异报告文件
        if missing_in_daily or missing_in_listed or name_mismatch:
            self.generate_detailed_report(missing_in_daily, missing_in_listed, name_mismatch, 
                                   self.listed_companies, self.daily_stock_data)
        
        return cleaned_data_sorted

    def generate_cleaning_report(self, original_data, cleaned_data, removed_codes, listed_companies):
        """
        生成数据清洗报告
        """
        report_path = os.path.join(self.csv_path, '数据清洗报告.txt')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("股票数据清洗报告\n")
            f.write("="*60 + "\n\n")
            
            f.write(f"清洗前数据总数: {len(original_data)}\n")
            f.write(f"清洗后数据总数: {len(cleaned_data)}\n")
            f.write(f"剔除数据总数: {len(removed_codes)}\n")
            f.write(f"数据保留率: {len(cleaned_data)/len(original_data)*100:.2f}%\n\n")
            
            f.write("被剔除的证券代码详情:\n")
            f.write("-"*40 + "\n")
            f.write("证券代码\t证券简称\t原板块信息\n")
            
            if removed_codes:
                for code in sorted(removed_codes):
                    row = original_data[original_data['证券代码'] == code].iloc[0]
                    f.write(f"{code}\t{row['证券简称']}\t{row.get('板块', 'N/A')}\n")
            else:
                f.write("无被剔除的数据\n")
            
            f.write(f"\n清洗后数据排序说明:\n")
            f.write("-"*40 + "\n")
            f.write("数据已按照'沪深上市公司列表.csv'中证券代码的原始顺序进行排序\n")
            
            if len(cleaned_data) > 0:
                f.write(f"排序后首个证券代码: {cleaned_data.iloc[0]['证券代码']}\n")
                f.write(f"排序后末个证券代码: {cleaned_data.iloc[-1]['证券代码']}\n")
        
        print(f"   数据清洗报告已保存为: {report_path}")

    def generate_detailed_report(self, missing_in_daily, missing_in_listed, name_mismatch, 
                               listed_companies, daily_stock_data):
        """
        生成详细的差异报告文件
        """
        report_path = os.path.join(self.csv_path, '股票数据差异报告.txt')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("股票数据差异详细报告\n")
            f.write("="*60 + "\n\n")
            
            f.write("1. 仅在沪深上市公司列表中存在的证券代码:\n")
            f.write("-"*40 + "\n")
            if missing_in_daily:
                for code in sorted(missing_in_daily):
                    company_name = listed_companies[listed_companies['证券代码'] == code]['证券简称'].iloc[0]
                    sector = listed_companies[listed_companies['证券代码'] == code]['板块'].iloc[0]
                    f.write(f"{code}\t{company_name}\t{sector}\n")
            else:
                f.write("无\n")
            
            f.write("\n2. 仅在当日股票数据中存在的证券代码:\n")
            f.write("-"*40 + "\n")
            if missing_in_listed:
                for code in sorted(missing_in_listed):
                    company_name = daily_stock_data[daily_stock_data['证券代码'] == code]['证券简称'].iloc[0]
                    sector = daily_stock_data[daily_stock_data['证券代码'] == code]['板块'].iloc[0]
                    f.write(f"{code}\t{company_name}\t{sector}\n")
            else:
                f.write("无\n")
            
            f.write("\n3. 证券简称不一致的记录:\n")
            f.write("-"*40 + "\n")
            if name_mismatch:
                for item in name_mismatch:
                    f.write(f"{item['证券代码']}\t上市公司列表: {item['上市公司列表中的简称']}\t当日数据: {item['当日数据中的简称']}\n")
            else:
                f.write("无\n")
        
        print(f"详细差异报告已保存为: {report_path}")

    def get_stock_prefix(self, board):
        """根据板块确定股票代码前缀"""
        if board in ["沪市主板", "科创板"]:
            return "SH"
        elif board in ["深市主板", "创业板"]:
            return "SZ"
        else:
            return "SH"  # 默认使用SH

    def process_stock_data(self, symbol, stock_code, max_retries=3, retry_delay=1):
        """处理单只股票数据，包含重试机制"""
        for attempt in range(max_retries):
            try:
                stock_data = ak.stock_individual_spot_xq(symbol=symbol)
                data_dict = dict(zip(stock_data['item'], stock_data['value']))
                
                required_fields = [
                    "证券代码", "时间", "收盘", "涨跌额", "涨跌幅(%)", "昨收", "开盘", "最高", "最低",
                    "成交量", "成交额", "换手率(%)", "振幅(%)", "涨停价", "跌停价", "均价",
                    "52周最高", "52周最低", "今年以来涨幅(%)", "市盈率(动)", "市盈率(静)", 
                    "市盈率(TTM)", "市净率", "每股收益(静)", "每股净资产", "股息(TTM)", 
                    "股息率(TTM)(%)", "总市值(亿元)", "流通市值(亿元)"
                ]
                
                result = {"证券代码": stock_code}  # 确保证券代码保持为字符串格式
                
                field_mapping = {
                    "收盘": "现价", "涨跌额": "涨跌", "涨跌幅(%)": "涨幅", "昨收": "昨收", 
                    "开盘": "今开", "最高": "最高", "最低": "最低", "成交量": "成交量",
                    "成交额": "成交额", "换手率(%)": "周转率", "振幅(%)": "振幅", "涨停价": "涨停",
                    "跌停价": "跌停", "均价": "均价", "52周最高": "52周最高", "52周最低": "52周最低",
                    "今年以来涨幅(%)": "今年以来涨幅", "市盈率(动)": "市盈率(动)", "市盈率(静)": "市盈率(静)",
                    "市盈率(TTM)": "市盈率(TTM)", "市净率": "市净率", "每股收益(静)": "每股收益",
                    "每股净资产": "每股净资产", "股息(TTM)": "股息(TTM)", "股息率(TTM)(%)": "股息率(TTM)",
                    "时间": "时间", "总市值(亿元)": "资产净值/总市值", "流通市值(亿元)": "流通值"
                }
                
                for field in required_fields[1:]:
                    result[field] = data_dict.get(field_mapping[field], None)
                
                return result
                
            except Exception as e:
                print(f"处理股票 {stock_code} 第 {attempt + 1} 次尝试失败: {str(e)}")
                if attempt < max_retries - 1:  # 如果不是最后一次尝试
                    print(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                else:
                    print(f"股票 {stock_code} 重试 {max_retries} 次后仍然失败")
        
        return None

    def retry_failed_stocks(self, failed_stocks_info, batch_data, output_file, batch_size):
        """对失败的股票进行最终重试"""
        print(f"\n开始对 {len(failed_stocks_info)} 只失败的股票进行最终重试...")
        print("等待60秒后开始重试...")
        time.sleep(60)
        
        final_failed_stocks = []
        retry_success_count = 0
        
        for i, (symbol, stock_code) in enumerate(failed_stocks_info):
            print(f"最终重试第 {i + 1}/{len(failed_stocks_info)} 只股票: {stock_code} ({symbol})")
            
            stock_data = self.process_stock_data(symbol, stock_code, max_retries=3, retry_delay=1)
            
            if stock_data:
                batch_data.append(stock_data)
                retry_success_count += 1
                print(f"重试成功: {stock_code}")
                
                # 如果批次数据达到指定大小，保存一次
                if len(batch_data) >= batch_size:
                    df_batch = pd.DataFrame(batch_data)
                    # 确保证券代码列保持字符串格式
                    df_batch['证券代码'] = df_batch['证券代码'].astype(str)
                    df_batch.to_csv(output_file, mode='a', header=False, index=False, encoding='utf-8-sig')
                    print(f"追加保存了 {len(batch_data)} 条重试成功的数据")
                    batch_data.clear()
            else:
                final_failed_stocks.append(stock_code)
                print(f"最终失败: {stock_code}")
            
            time.sleep(0.1)
        
        # 保存剩余的批次数据
        if batch_data:
            df_batch = pd.DataFrame(batch_data)
            # 确保证券代码列保持字符串格式
            df_batch['证券代码'] = df_batch['证券代码'].astype(str)
            df_batch.to_csv(output_file, mode='a', header=False, index=False, encoding='utf-8-sig')
            print(f"追加保存了 {len(batch_data)} 条重试成功的数据")
        
        print(f"最终重试完成: 成功 {retry_success_count} 只，失败 {len(final_failed_stocks)} 只")
        return final_failed_stocks

    def get_detailed_stock_data(self):
        """
        通过雪球API获取详细的股票数据
        """
        # 读取股票列表时指定证券代码列为字符串类型
        try:
            stock_list_path = os.path.join(self.csv_path, "沪深上市公司列表.csv")
            stock_list = pd.read_csv(stock_list_path, encoding='utf-8', dtype={'证券代码': str})
        except:
            try:
                stock_list = pd.read_csv(stock_list_path, encoding='gbk', dtype={'证券代码': str})
            except Exception as e:
                print(f"读取CSV文件失败: {str(e)}")
                return
        
        print(f"共找到 {len(stock_list)} 只股票")
        
        batch_size = 50
        output_file = os.path.join(self.csv_path, "企业当日数据.csv")
        failed_file = os.path.join(self.csv_path, "获取失败股票.csv")
        
        if os.path.exists(output_file):
            os.remove(output_file)
        
        processed_count = 0
        failed_stocks_info = []  # 存储失败股票的信息 (symbol, stock_code)
        batch_data = []
        
        # 第一轮处理
        for index, row in stock_list.iterrows():
            board = row['板块']
            stock_code = str(row['证券代码']).zfill(6)  # 确保为6位字符串
            
            prefix = self.get_stock_prefix(board)
            symbol = f"{prefix}{stock_code}"
            
            # print(f"正在处理第 {index + 1}/{len(stock_list)} 只股票: {stock_code} ({symbol})")
            
            stock_data = self.process_stock_data(symbol, stock_code, max_retries=3, retry_delay=1)
            
            if stock_data:
                batch_data.append(stock_data)
                processed_count += 1
            else:
                failed_stocks_info.append((symbol, stock_code))
            
            # 批量保存数据
            if len(batch_data) >= batch_size or index == len(stock_list) - 1:
                if batch_data:
                    df_batch = pd.DataFrame(batch_data)
                    # 确保证券代码列保持字符串格式
                    df_batch['证券代码'] = df_batch['证券代码'].astype(str)
                    
                    if not os.path.exists(output_file):
                        df_batch.to_csv(output_file, index=False, encoding='utf-8-sig')
                        print(f"创建文件并保存了 {len(batch_data)} 条数据")
                    else:
                        df_batch.to_csv(output_file, mode='a', header=False, index=False, encoding='utf-8-sig')
                        # print(f"追加保存了 {len(batch_data)} 条数据")
                    
                    batch_data = []
            
            time.sleep(0.1)
        
        # 如果有失败的股票，进行最终重试
        if failed_stocks_info:
            final_failed_stocks = self.retry_failed_stocks(failed_stocks_info, batch_data, output_file, batch_size)
            processed_count += len(failed_stocks_info) - len(final_failed_stocks)
            
            # 保存最终失败的股票代码
            if final_failed_stocks:
                failed_df = pd.DataFrame({"未成功获取的股票代码": final_failed_stocks})
                # 确保失败股票代码也保持字符串格式
                failed_df['未成功获取的股票代码'] = failed_df['未成功获取的股票代码'].astype(str)
                failed_df.to_csv(failed_file, index=False, encoding='utf-8-sig')
                print(f"最终未成功获取的股票代码已保存到: {failed_file}")
        
        print(f"\n数据获取完成！")
        print(f"总共处理了 {len(stock_list)} 只股票")
        print(f"成功获取了 {processed_count} 只股票的数据")
        print(f"数据已保存到: {output_file}")

        # 合并额外数据
        try:
            # 读取两个文件时都指定证券代码列为字符串类型
            clear_data_path = os.path.join(self.csv_path, "当日股票数据_清洗后.csv")
            clear_data = pd.read_csv(clear_data_path, encoding='utf-8', dtype={'证券代码': str})
            existing_data = pd.read_csv(output_file, encoding='utf-8-sig', dtype={'证券代码': str})
            
            # 确保两个DataFrame的证券代码列都是字符串格式并且长度一致
            clear_data['证券代码'] = clear_data['证券代码'].astype(str).str.zfill(6)
            existing_data['证券代码'] = existing_data['证券代码'].astype(str).str.zfill(6)
            
            print(f"清洗后数据中的证券代码示例: {clear_data['证券代码'].head().tolist()}")
            print(f"现有数据中的证券代码示例: {existing_data['证券代码'].head().tolist()}")
            
            merged_data = pd.merge(existing_data, clear_data[["证券代码", "量比", "涨速", "5分钟涨跌", "60日涨跌幅"]], on="证券代码", how="left")
            
            # 确保合并后的数据中证券代码仍为字符串格式
            merged_data['证券代码'] = merged_data['证券代码'].astype(str)

            # 重命名列名
            merged_data.rename(columns={'60日涨跌幅': '60日涨跌幅(%)'}, inplace=True)
            
            merged_output_file = os.path.join(self.csv_path, "企业当日数据_全字段_雪球为主_当日数据.csv")
            merged_data.to_csv(merged_output_file, index=False, encoding='utf-8-sig')
            print(f"合并数据已保存到: {merged_output_file}")
            print(f"合并后数据中的证券代码示例: {merged_data['证券代码'].head().tolist()}")
            
        except Exception as e:
            print(f"合并数据时出错: {str(e)}")

    def run_complete_process(self):
        """
        运行完整的股票数据处理流程
        """
        print("="*60)
        print("开始执行完整的股票数据处理流程")
        print("="*60)
        
        # 步骤1: 获取股票数据
        print("\n步骤1: 获取沪深A股实时数据")
        print("-" * 40)
        all_data = self.get_all_stock_data()
        if not all_data.empty:
            self.save_to_csv(all_data)
        else:
            print("获取股票数据失败，流程终止")
            return
        
        # 步骤2: 分析和更新数据
        print("\n步骤2: 分析数据差异并更新板块信息")
        print("-" * 40)
        cleaned_data = self.analyze_and_update_stock_data()
        if cleaned_data is None or cleaned_data.empty:
            print("数据分析和清洗失败，流程终止")
            return
        
        # 步骤3: 获取详细数据并合并
        print("\n步骤3: 获取详细财务指标并合并数据")
        print("-" * 40)
        self.get_detailed_stock_data()
        
        print("\n" + "="*60)
        print("完整流程执行完毕！")
        print("="*60)

def main():
    """
    主函数 - 用于定时任务调用
    """
    processor = StockDataProcessor()
    
    print("股票数据完整处理脚本")
    print("功能说明：")
    print("1. 获取沪深A股实时数据并保存")
    print("2. 分析数据差异并更新板块信息")
    print("3. 通过雪球API获取详细财务指标并合并数据")
    print(f"\n使用CSV路径: {processor.csv_path}")
    
    # 检查必要文件是否存在
    listed_companies_path = os.path.join(processor.csv_path, '沪深上市公司列表.csv')
    if not os.path.exists(listed_companies_path):
        print(f"\n错误：未找到 '沪深上市公司列表.csv' 文件")
        print(f"请确保该文件存在于 {processor.csv_path} 目录下")
        return
    
    start_time = datetime.now()
    print(f"\n开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        processor.run_complete_process()
        
        end_time = datetime.now()
        duration = end_time - start_time
        print(f"\n结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总耗时: {duration}")
        
    except KeyboardInterrupt:
        print("\n\n用户中断了程序执行")
    except Exception as e:
        print(f"\n程序执行过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
