const xfApi = require('../../utils/xfApi');
const { parseFilterCondition } = require('./parseFilterConditionService');

/**
 * 解析语音输入的筛选条件
 * @param {string} audioBase64 Base64编码的音频数据
 * @param {string} audioFormat 音频格式 (wav, mp3, pcm等)
 * @returns {Promise<Object>} 解析后的筛选条件对象
 */
const parseAudioFilterCondition = async (audioBase64, audioFormat = 'wav') => {
  try {
    console.log('开始处理语音输入...');
    
    // 验证Base64音频数据
    if (!validateBase64AudioData(audioBase64)) {
      throw new Error('无效的音频数据格式');
    }
    
    console.log('调用科大讯飞API进行语音识别...');
    
    // 使用科大讯飞API将语音转换为文字
    const recognizedText = await xfApi.speechToText(audioBase64, audioFormat);
    
    if (!recognizedText || recognizedText.trim().length === 0) {
      throw new Error('语音识别结果为空，请重新录音');
    }
    
    console.log(`语音识别结果: ${recognizedText}`);
    
    // 使用现有的文字解析服务解析筛选条件
    const filterConditions = await parseFilterCondition(recognizedText);
    
    console.log('语音筛选条件解析完成');
    
    return {
      recognizedText,
      filterConditions
    };
    
  } catch (error) {
    console.error('解析语音筛选条件失败:', error);
    throw new Error(`解析语音筛选条件失败: ${error.message}`);
  }
};

/**
 * 验证Base64音频数据
 * @param {string} audioBase64 Base64编码的音频数据
 * @returns {boolean} 是否有效
 */
const validateBase64AudioData = (audioBase64) => {
  if (!audioBase64 || typeof audioBase64 !== 'string') {
    return false;
  }
  
  // 检查是否是有效的Base64格式 (data:audio/...;base64,xxxx 或者 直接的base64字符串)
  const base64WithHeader = /^data:audio\/[^;]+;base64,/.test(audioBase64);
  const pureBase64 = /^[A-Za-z0-9+/]*={0,2}$/.test(audioBase64);
  
  if (!base64WithHeader && !pureBase64) {
    return false;
  }
  
  // 提取纯Base64数据
  const base64Data = base64WithHeader 
    ? audioBase64.replace(/^data:audio\/[^;]+;base64,/, '')
    : audioBase64;
  
  // 检查Base64数据长度（简单验证）
  if (base64Data.length === 0) {
    return false;
  }
  
  // 估算音频大小（Base64编码后大约是原数据的1.33倍），限制在10MB以内
  const estimatedSize = (base64Data.length * 3) / 4;
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (estimatedSize > maxSize) {
    return false;
  }
  
  return true;
};

module.exports = {
  parseAudioFilterCondition,
  validateBase64AudioData
};
