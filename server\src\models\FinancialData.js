const mongoose = require('mongoose');

// 财务数据模型 - 嵌套结构设计，支持多种财务报表类型
const financialDataSchema = new mongoose.Schema({
  // === 基本信息 ===
  // 证券代码
  stockCode: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  
  // 报表类型（按年度、按报告期、按单季度）
  reportType: {
    type: String,
    required: true,
    enum: ['按年度', '按报告期', '按单季度'],
    default: '按年度',
    index: true
  },
  
  // 财政年度
  fiscalYear: {
    type: Number,
    required: true,
    index: true
  },
  
  // 报告日期
  reportDate: {
    type: Date,
    required: true,
    index: true
  },
  
  
  // === 财务数据（嵌套结构） ===
  data: {
    // 关键指标表 - 与CSV文件完全对应
    keyMetrics: {
      // === 盈利能力指标 ===
      netProfit: { type: Number, default: null },                    // 净利润
      netProfitGrowthRate: { type: Number, default: null }, // 净利润同比增长率
      nonRecurringNetProfit: { type: Number, default: null },        // 扣非净利润
      nonRecurringNetProfitGrowthRate: { type: Number, default: null }, // 扣非净利润同比增长率
      totalRevenue: { type: Number, default: null },                 // 营业总收入
      totalRevenueGrowthRate: { type: Number, default: null }, // 营业总收入同比增长率
      netProfitMargin: { type: Number, default: null },   // 销售净利率
      grossProfitMargin: { type: Number, default: null }, // 销售毛利率
      
      // === 每股指标 ===
      basicEarningsPerShare: { type: Number, default: null },        // 基本每股收益
      bookValuePerShare: { type: Number, default: null },            // 每股净资产
      capitalReservePerShare: { type: Number, default: null },       // 每股资本公积金
      undistributedProfitPerShare: { type: Number, default: null },  // 每股未分配利润
      operatingCashFlowPerShare: { type: Number, default: null },    // 每股经营现金流
      
      // === 盈利质量指标 ===
      returnOnEquity: { type: Number, default: null },     // 净资产收益率
      dilutedReturnOnEquity: { type: Number, default: null }, // 净资产收益率-摊薄
      
      // === 偿债能力指标 ===
      currentRatio: { type: Number, default: null },       // 流动比率
      quickRatio: { type: Number, default: null },         // 速动比率
      conservativeQuickRatio: { type: Number, default: null }, // 保守速动比率
      equityRatio: { type: Number, default: null },        // 产权比率
      debtToAssetRatio: { type: Number, default: null },   // 资产负债率
      
      // === 营运能力指标 ===
      operatingCycle: { type: Number, default: null },     // 营业周期
      inventoryTurnoverRatio: { type: Number, default: null }, // 存货周转率
      inventoryTurnoverDays: { type: Number, default: null },  // 存货周转天数
      accountsReceivableTurnoverDays: { type: Number, default: null } // 应收账款周转天数
    },
    
    // 资产负债表（预留结构）
    balanceSheet: {
      totalAssets: { type: Number, default: null },           // 总资产
      currentAssets: { type: Number, default: null },         // 流动资产
      totalLiabilities: { type: Number, default: null },      // 总负债
      currentLiabilities: { type: Number, default: null },    // 流动负债
      shareholdersEquity: { type: Number, default: null },    // 股东权益
      // 可以根据需要添加更多资产负债表项目
      additionalFields: { type: Map, of: mongoose.Schema.Types.Mixed }
    },
    
    // 利润表（预留结构）
    incomeStatement: {
      operatingRevenue: { type: Number, default: null },      // 营业收入
      operatingCost: { type: Number, default: null },         // 营业成本
      operatingProfit: { type: Number, default: null },       // 营业利润
      totalProfit: { type: Number, default: null },           // 利润总额
      netProfit: { type: Number, default: null },             // 净利润
      // 可以根据需要添加更多利润表项目
      additionalFields: { type: Map, of: mongoose.Schema.Types.Mixed }
    },
    
    // 现金流量表（预留结构）
    cashFlow: {
      operatingCashFlow: { type: Number, default: null },     // 经营活动现金流
      investingCashFlow: { type: Number, default: null },     // 投资活动现金流
      financingCashFlow: { type: Number, default: null },     // 筹资活动现金流
      netCashFlow: { type: Number, default: null },           // 现金流量净额
      // 可以根据需要添加更多现金流量表项目
      additionalFields: { type: Map, of: mongoose.Schema.Types.Mixed }
    }
  },
  
  
  // 创建时间
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  // 更新时间
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  // 自动添加 createdAt 和 updatedAt
  timestamps: true,
  
  // 设置集合名称
  collection: 'financial_data'
});

// 复合索引 - 确保同一公司同一财政年度同一报表类型同一报告日期的唯一性
financialDataSchema.index({ 
  stockCode: 1, 
  fiscalYear: 1, 
  reportType: 1,
  reportDate: 1
}, { unique: true });

// 普通索引
financialDataSchema.index({ stockCode: 1, fiscalYear: -1 }); // 按股票代码和财政年度排序
financialDataSchema.index({ fiscalYear: -1 }); // 按财政年度排序
financialDataSchema.index({ reportDate: -1 }); // 按报告日期排序
financialDataSchema.index({ reportType: 1 }); // 按报表类型查询

// === 字段映射 ===

// 获取中英文字段映射
financialDataSchema.statics.getFieldMapping = function() {
  return {
    '净利润(亿元)': 'data.keyMetrics.netProfit',
    '净利润同比增长率(%)': 'data.keyMetrics.netProfitGrowthRate',
    '扣非净利润(亿元)': 'data.keyMetrics.nonRecurringNetProfit',
    '扣非净利润同比增长率(%)': 'data.keyMetrics.nonRecurringNetProfitGrowthRate',
    '营业总收入(亿元)': 'data.keyMetrics.totalRevenue',
    '营业总收入同比增长率(%)': 'data.keyMetrics.totalRevenueGrowthRate',
    '销售净利率(%)': 'data.keyMetrics.netProfitMargin',
    '销售毛利率(%)': 'data.keyMetrics.grossProfitMargin',
    '基本每股收益': 'data.keyMetrics.basicEarningsPerShare',
    '每股净资产': 'data.keyMetrics.bookValuePerShare',
    '每股资本公积金': 'data.keyMetrics.capitalReservePerShare',
    '每股未分配利润': 'data.keyMetrics.undistributedProfitPerShare',
    '每股经营现金流': 'data.keyMetrics.operatingCashFlowPerShare',
    '净资产收益率(%)': 'data.keyMetrics.returnOnEquity',
    '净资产收益率-摊薄(%)': 'data.keyMetrics.dilutedReturnOnEquity',
    '流动比率': 'data.keyMetrics.currentRatio',
    '速动比率': 'data.keyMetrics.quickRatio',
    '保守速动比率': 'data.keyMetrics.conservativeQuickRatio',
    '产权比率': 'data.keyMetrics.equityRatio',
    '资产负债率(%)': 'data.keyMetrics.debtToAssetRatio',
    '营业周期': 'data.keyMetrics.operatingCycle',
    '存货周转率': 'data.keyMetrics.inventoryTurnoverRatio',
    '存货周转天数': 'data.keyMetrics.inventoryTurnoverDays',
    '应收账款周转天数': 'data.keyMetrics.accountsReceivableTurnoverDays'
  };
};

// 获取字段的中文名称
financialDataSchema.statics.getChineseFieldName = function(englishField) {
  const mapping = this.getFieldMapping();
  for (const [chinese, english] of Object.entries(mapping)) {
    if (english === englishField || english.endsWith('.' + englishField)) {
      return chinese;
    }
  }
  return englishField;
};

// === 静态方法 ===

// 根据股票代码查找财务数据
financialDataSchema.statics.findByStockCode = function(stockCode, options = {}) {
  const query = { stockCode };
  if (options.reportType) query.reportType = options.reportType;
  if (options.fiscalYear) query.fiscalYear = options.fiscalYear;
  if (options.startYear && options.endYear) {
    query.fiscalYear = { $gte: options.startYear, $lte: options.endYear };
  }
  
  return this.find(query).sort({ fiscalYear: -1 });
};

// 获取最新的财务数据
financialDataSchema.statics.findLatestByStockCode = function(stockCode, reportType = '按年度') {
  return this.findOne({ 
    stockCode, 
    reportType 
  }).sort({ fiscalYear: -1 });
};

// 根据年度范围查找
financialDataSchema.statics.findByYearRange = function(stockCode, startYear, endYear, reportType = '按年度') {
  return this.find({
    stockCode,
    reportType,
    fiscalYear: { $gte: startYear, $lte: endYear }
  }).sort({ fiscalYear: 1 });
};

// 批量查找多个股票的最新数据
financialDataSchema.statics.findLatestByStockCodes = function(stockCodes, reportType = '按年度') {
  return this.aggregate([
    { $match: { stockCode: { $in: stockCodes }, reportType } },
    { $sort: { stockCode: 1, fiscalYear: -1 } },
    { $group: {
        _id: '$stockCode',
        latestData: { $first: '$$ROOT' }
      }
    }
  ]);
};

// 查找指定年度的所有股票数据
financialDataSchema.statics.findByYear = function(year, reportType = '按年度', options = {}) {
  const query = { fiscalYear: year, reportType };
  
  return this.find(query)
    .sort({ stockCode: 1 })
    .limit(options.limit || 0)
    .skip(options.skip || 0);
};

// === 实例方法 ===

// 格式化显示
financialDataSchema.methods.toDisplayString = function() {
  return `${this.stockCode} - ${this.fiscalYear}年 (${this.reportType})`;
};

// 获取净利润的格式化显示
financialDataSchema.methods.getFormattedNetProfit = function() {
  const netProfit = this.data?.keyMetrics?.netProfit;
  if (netProfit === null || netProfit === undefined) return 'N/A';
  
  return this.formatCurrency(netProfit);
};

// 获取营业收入的格式化显示
financialDataSchema.methods.getFormattedRevenue = function() {
  const revenue = this.data?.keyMetrics?.totalRevenue;
  if (revenue === null || revenue === undefined) return 'N/A';
  
  return this.formatCurrency(revenue);
};

// 获取每股收益的格式化显示
financialDataSchema.methods.getFormattedEarningsPerShare = function() {
  const eps = this.data?.keyMetrics?.basicEarningsPerShare;
  if (eps === null || eps === undefined) return 'N/A';
  
  return eps.toFixed(4);
};

// 获取ROE的格式化显示
financialDataSchema.methods.getFormattedROE = function() {
  const roe = this.data?.keyMetrics?.returnOnEquity;
  if (roe === null || roe === undefined) return 'N/A';
  
  return `${(roe * 100).toFixed(2)}%`;
};

// 获取关键指标摘要
financialDataSchema.methods.getKeyMetricsSummary = function() {
  const metrics = this.data?.keyMetrics;
  if (!metrics) return {};
  
  return {
    netProfit: this.getFormattedNetProfit(),
    revenue: this.getFormattedRevenue(),
    earningsPerShare: this.getFormattedEarningsPerShare(),
    roe: this.getFormattedROE(),
    netMargin: metrics.netProfitMargin ? `${(metrics.netProfitMargin * 100).toFixed(2)}%` : 'N/A',
    currentRatio: metrics.currentRatio ? metrics.currentRatio.toFixed(2) : 'N/A',
    debtRatio: metrics.debtToAssetRatio ? `${(metrics.debtToAssetRatio * 100).toFixed(2)}%` : 'N/A'
  };
};

// 格式化货币显示
financialDataSchema.methods.formatCurrency = function(value) {
  if (value >= 100000000) {
    return `${(value / 100000000).toFixed(2)}亿`;
  } else if (value >= 10000) {
    return `${(value / 10000).toFixed(2)}万`;
  }
  return value.toString();
};

// 计算数据完整性评分
financialDataSchema.methods.calculateCompletenessScore = function() {
  const keyFields = [
    'data.keyMetrics.netProfit',
    'data.keyMetrics.totalRevenue',
    'data.keyMetrics.basicEarningsPerShare',
    'data.keyMetrics.returnOnEquity',
    'data.keyMetrics.netProfitMargin',
    'data.keyMetrics.currentRatio',
    'data.keyMetrics.debtToAssetRatio'
  ];
  
  let completedFields = 0;
  keyFields.forEach(field => {
    const value = field.split('.').reduce((obj, key) => obj?.[key], this);
    if (value !== null && value !== undefined) {
      completedFields++;
    }
  });
  
  return completedFields / keyFields.length;
};

// 中间件：保存前自动更新时间
financialDataSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// 导出模型
module.exports = mongoose.model('FinancialData', financialDataSchema);
