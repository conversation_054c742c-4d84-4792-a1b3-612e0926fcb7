const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
const HistoricalDailyData = require('../src/models/HistoricalDailyData');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// 数据库连接配置
const MONGODB_URI = process.env.MONGODB_URI;

// CSV数据文件夹路径
const DATA_FOLDER = path.join(__dirname, '../../data_gp/企业历史日级别价格数据_全字段_end20250904');

// 日志记录
const logFile = path.join(__dirname, '../logs/import_historical_errors.json');
const importErrors = [];
const successLog = [];

/**
 * 记录错误信息
 */
function logError(fileName, error, data = null) {
  const errorInfo = {
    fileName,
    error: error.message,
    data,
    timestamp: new Date().toISOString()
  };
  importErrors.push(errorInfo);
  console.error(`❌ 文件 ${fileName} 导入错误:`, error.message);
}

/**
 * 记录成功信息
 */
function logSuccess(fileName, count) {
  const successInfo = {
    fileName,
    count,
    timestamp: new Date().toISOString()
  };
  successLog.push(successInfo);
  console.log(`✅ 文件 ${fileName} 导入成功: ${count} 条记录`);
}

/**
 * 检测并移除BOM
 */
function removeBOM(content) {
  if (content.charCodeAt(0) === 0xFEFF) {
    return content.slice(1);
  }
  return content;
}

/**
 * 解析CSV行数据
 */
function parseCSVLine(line, headers) {
  const values = line.split(',');
  const record = {};
  
  headers.forEach((header, index) => {
    let value = values[index];
    
    if (value !== undefined && value !== null) {
      value = value.trim();
      
      // 处理空值
      if (value === '' || value === 'null' || value === 'NULL') {
        record[header] = null;
        return;
      }
      
      // 处理证券代码 - 确保保持字符串格式，防止000001变成1
      if (header === '证券代码') {
        record[header] = value.toString().padStart(6, '0');
        return;
      }
      
      // 处理日期
      if (header === '日期') {
        record[header] = new Date(value);
        return;
      }
      
      // 处理数值字段（包括可能的负数）
      const numericFields = [
        '开盘', '收盘', '最高', '最低', '成交量', '成交额', '振幅', '涨跌幅', '涨跌额', '换手率',
        '昨收', '涨停价', '跌停价', '均价', '52周最高', '52周最低', '今年以来涨幅', '60日涨跌幅',
        '每股收益(静)', '每股收益(TTM)', '每股净资产', '市盈率(静)', '市盈率(TTM)', '市净率',
        '股息率(TTM)', '股息(TTM)'
      ];
      
      if (numericFields.includes(header)) {
        const numValue = parseFloat(value);
        record[header] = isNaN(numValue) ? null : numValue;
        return;
      }
      
      record[header] = value;
    }
  });
  
  return record;
}

/**
 * 将中文字段映射为英文字段
 */
function mapChineseToEnglish(chineseData) {
  const fieldMapping = HistoricalDailyData.getFieldMapping();
  const englishData = {};
  
  for (const [chineseField, value] of Object.entries(chineseData)) {
    const englishField = fieldMapping[chineseField];
    if (englishField) {
      englishData[englishField] = value;
    }
  }
  
  return englishData;
}

/**
 * 批量插入数据到数据库
 */
async function batchInsertData(records, fileName) {
  if (records.length === 0) {
    console.log(`⚠️  文件 ${fileName} 没有有效数据`);
    return 0;
  }
  
  try {
    // 使用 insertMany 进行批量插入，ordered: false 让其继续插入未重复的数据
    const result = await HistoricalDailyData.insertMany(records, {
      ordered: false,
      rawResult: true
    });
    
    return result.insertedCount || records.length;
  } catch (error) {
    // 处理重复键错误 - 这是正常的，因为可能已经导入过部分数据
    if (error.code === 11000 || error.name === 'BulkWriteError') {
      let insertedCount = 0;
      if (error.result && error.result.insertedCount) {
        insertedCount = error.result.insertedCount;
      } else if (error.insertedDocs) {
        insertedCount = error.insertedDocs.length;
      }
      
      console.log(`⚠️  文件 ${fileName} 部分数据已存在，新插入 ${insertedCount} 条记录`);
      return insertedCount;
    } else {
      throw error;
    }
  }
}

/**
 * 处理单个CSV文件
 */
async function processCSVFile(filePath) {
  const fileName = path.basename(filePath);
  
  try {
    console.log(`\n📂 开始处理文件: ${fileName}`);
    
    // 读取文件内容
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 移除BOM
    content = removeBOM(content);
    
    // 按行分割
    const lines = content.split('\n').filter(line => line.trim());
    
    if (lines.length < 2) {
      console.log(`⚠️  文件 ${fileName} 内容不足，跳过`);
      return;
    }
    
    // 解析表头
    const headers = lines[0].split(',').map(h => h.trim());
    console.log(`📋 表头字段: ${headers.join(', ')}`);
    
    // 验证必要字段
    const requiredFields = ['证券代码', '日期', '开盘', '收盘', '最高', '最低', '成交量', '成交额'];
    const missingFields = requiredFields.filter(field => !headers.includes(field));
    
    if (missingFields.length > 0) {
      logError(fileName, new Error(`缺少必要字段: ${missingFields.join(', ')}`));
      return;
    }
    
    // 解析数据行
    const records = [];
    
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;
      
      try {
        // 解析CSV行
        const chineseData = parseCSVLine(line, headers);
        
        // 验证必要数据
        if (!chineseData['证券代码'] || !chineseData['日期']) {
          continue;
        }
        
        // 转换为英文字段
        const englishData = mapChineseToEnglish(chineseData);
        
        // 验证数据完整性
        if (englishData.stockCode && englishData.date && 
            !isNaN(englishData.openPrice) && !isNaN(englishData.closePrice)) {
          records.push(englishData);
        }
        
      } catch (error) {
        console.warn(`⚠️  行 ${i + 1} 解析失败，跳过: ${error.message}`);
      }
    }
    
    console.log(`📊 解析完成，共 ${records.length} 条有效记录`);
    
    // 批量插入数据库
    const insertedCount = await batchInsertData(records, fileName);
    
    // 记录成功
    logSuccess(fileName, insertedCount);
    
  } catch (error) {
    logError(fileName, error);
  }
}

/**
 * 主函数 - 处理所有CSV文件
 */
async function importHistoricalDailyData() {
  console.log('🚀 开始导入企业历史日级别价格数据...');
  console.log(`📁 数据文件夹: ${DATA_FOLDER}`);
  console.log(`🗄️  数据库地址: ${MONGODB_URI}`);
  
  try {
    // 连接数据库
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ 数据库连接成功');
    
    // 获取所有CSV文件
    const files = fs.readdirSync(DATA_FOLDER)
      .filter(file => file.endsWith('.csv'))
      .sort();
    
    console.log(`📄 找到 ${files.length} 个CSV文件`);
    
    if (files.length === 0) {
      console.log('⚠️  没有找到CSV文件');
      return;
    }
    
    // 逐个处理文件
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const filePath = path.join(DATA_FOLDER, file);
      
      console.log(`\n📈 进度: ${i + 1}/${files.length}`);
      await processCSVFile(filePath);
      
      // 每处理10个文件暂停一下，避免内存压力
      if ((i + 1) % 10 === 0) {
        console.log('💤 暂停1秒...');
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    // 输出总结
    console.log('\n📊 导入完成统计:');
    console.log(`✅ 成功处理: ${successLog.length} 个文件`);
    console.log(`❌ 处理失败: ${importErrors.length} 个文件`);
    
    if (successLog.length > 0) {
      const totalRecords = successLog.reduce((sum, log) => sum + log.count, 0);
      console.log(`📈 总计导入: ${totalRecords} 条历史数据记录`);
    }
    
    // 保存错误日志
    if (importErrors.length > 0) {
      // 确保logs目录存在
      const logsDir = path.dirname(logFile);
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
      }
      
      fs.writeFileSync(logFile, JSON.stringify({
        importDate: new Date().toISOString(),
        totalFiles: files.length,
        successCount: successLog.length,
        errorCount: importErrors.length,
        errors: importErrors,
        successes: successLog
      }, null, 2));
      
      console.log(`📋 错误日志已保存到: ${logFile}`);
    }
    
    console.log('\n🎉 历史数据导入任务完成!');
    
  } catch (error) {
    console.error('💥 导入过程发生严重错误:', error.message);
    console.error(error.stack);
  } finally {
    // 关闭数据库连接
    await mongoose.disconnect();
    console.log('✅ 数据库连接已关闭');
  }
}

// 当直接运行此脚本时执行导入
if (require.main === module) {
  importHistoricalDailyData().catch(error => {
    console.error('💥 导入任务发生严重错误:', error);
    process.exit(1);
  });
}

module.exports = { importHistoricalDailyData };
