// 股票信息页面逻辑
const queryApi = require('../../../api/query');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 证券代码
    stockCode: '',
    
    // 股票信息
    stockInfo: {},
    
    // 基本信息列表（用于显示）
    basicInfoList: [],
    
    // 数据类型列表
    dataTypes: [],
    
    // 当前激活的标签页
    activeTab: '',
    
    // 当前显示的数据列表
    currentDataList: [],
    
    // 当前标签页信息
    currentTabInfo: {},
    
    // 加载状态
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取证券代码
    if (options.stockCode) {
      this.setData({
        stockCode: options.stockCode
      });
      
      // 加载股票信息
      this.loadStockInfo();
    } else {
      wx.showToast({
        title: '缺少证券代码参数',
        icon: 'none',
        duration: 2000
      });
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }
  },

  /**
   * 加载股票信息
   */
  async loadStockInfo() {
    this.setData({
      loading: true
    });

    try {
      const result = await queryApi.queryStockInfo(this.data.stockCode);
      
      if (result.success && result.data) {
        this.setData({
          stockInfo: result.data
        });
        
        // 处理基本信息
        this.processBasicInfo(result.data.basicInfo);
        
        // 处理数据类型
        this.processDataTypes(result.data);
        
      } else {
        throw new Error(result.message || '获取股票信息失败');
      }
      
    } catch (error) {
      console.error('加载股票信息失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none',
        duration: 2000
      });
    } finally {
      this.setData({
        loading: false
      });
    }
  },

  /**
   * 处理基本信息
   */
  processBasicInfo(basicInfo) {
    if (!basicInfo) return;
    
    const basicInfoList = [];
    
    // 定义基本信息字段的显示顺序和标签
    const basicInfoFields = [
      { key: '证券代码', label: '证券代码' },
      { key: '证券简称', label: '证券简称' },
      { key: '板块', label: '板块' },
      { key: '行业', label: '行业' },
      { key: '上市时间', label: '上市时间' }
    ];
    
    basicInfoFields.forEach(field => {
      if (basicInfo[field.key] !== undefined) {
        let value = basicInfo[field.key];
        
        // 特殊处理上市时间
        if (field.key === '上市时间' && value) {
          value = new Date(value).toLocaleDateString('zh-CN');
        }
        
        basicInfoList.push({
          label: field.label,
          value: value || '--'
        });
      }
    });
    
    this.setData({
      basicInfoList: basicInfoList
    });
  },

  /**
   * 处理数据类型
   */
  processDataTypes(stockInfo) {
    const dataTypes = [];
    
    // 定义数据类型映射
    const typeMapping = {
      'dailyData': { label: '日行情', icon: '📈' },
      'financialData': { label: '财务数据', icon: '💰' },
      'businessData': { label: '业务介绍', icon: '📊' },
      'businessComposition': { label: '主营构成', icon: '🔍' },
      'dividendData': { label: '分红情况', icon: '🏪' }
    };
    
    // 遍历股票信息，找出除基本信息外的其他数据类型
    for (let key in stockInfo) {
      if (key !== 'basicInfo' && stockInfo[key] && typeof stockInfo[key] === 'object') {
        const mapping = typeMapping[key] || { label: key, icon: '📋' };
        dataTypes.push({
          key: key,
          label: mapping.label,
          icon: mapping.icon
        });
      }
    }
    
    this.setData({
      dataTypes: dataTypes
    });
    
    // 默认选择第一个数据类型
    if (dataTypes.length > 0) {
      this.switchTab({ currentTarget: { dataset: { tab: dataTypes[0].key } } });
    }
  },

  /**
   * 切换标签页
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    
    this.setData({
      activeTab: tab
    });
    
    // 处理当前标签页数据
    this.processCurrentTabData(tab);
  },

  /**
   * 处理当前标签页数据
   */
  processCurrentTabData(tabKey) {
    const stockInfo = this.data.stockInfo;
    const tabData = stockInfo[tabKey];
    
    if (!tabData) {
      this.setData({
        currentDataList: [],
        currentTabInfo: {}
      });
      return;
    }
    
    const dataList = [];
    let tabInfo = {};
    
    // 获取标签页信息
    const dataType = this.data.dataTypes.find(type => type.key === tabKey);
    if (dataType) {
      tabInfo.label = dataType.label;
    }
    
    // 处理不同类型的数据
    if (tabKey === 'dailyData') {
      this.processDailyData(tabData, dataList, tabInfo);
    } else if (tabKey === 'financialData') {
      this.processFinancialData(tabData, dataList, tabInfo);
    } else {
      // 通用处理方式
      this.processGenericData(tabData, dataList, tabInfo);
    }
    
    this.setData({
      currentDataList: dataList,
      currentTabInfo: tabInfo
    });
  },

  /**
   * 处理日行情数据
   */
  processDailyData(data, dataList, tabInfo) {
    // 添加副标题
    if (data['时间']) {
      tabInfo.subtitle = `数据时间：${new Date(data['时间']).toLocaleString('zh-CN')}`;
    }
    
    // 遍历数据字段
    for (let key in data) {
      if (key === '时间') continue; // 时间已在副标题显示
      
      let value = data[key];
      let valueClass = '';
      
      // 特殊处理涨跌相关字段
      if (key.includes('涨跌') && typeof value === 'number') {
        valueClass = value > 0 ? 'rise' : value < 0 ? 'fall' : '';
        if (value > 0) {
          value = `+${value}`;
        }
      }
      
      // 格式化数值
      if (typeof value === 'number') {
        if (value > 100000000) {
          value = `${(value / 100000000).toFixed(2)}亿`;
        } else if (value > 10000) {
          value = `${(value / 10000).toFixed(2)}万`;
        } else {
          value = value.toFixed(2);
        }
      }
      
      dataList.push({
        label: key,
        value: value || '--',
        valueClass: valueClass
      });
    }
  },

  /**
   * 处理财务数据
   */
  processFinancialData(data, dataList, tabInfo) {
    // 添加副标题
    if (data['fiscalYear']) {
      tabInfo.subtitle = `财年：${data['fiscalYear']}`;
    }
    if (data['reportDate']) {
      const reportDate = new Date(data['reportDate']).toLocaleDateString('zh-CN');
      tabInfo.subtitle = (tabInfo.subtitle || '') + ` 报告日期：${reportDate}`;
    }
    
    // 遍历数据字段
    for (let key in data) {
      if (key === 'fiscalYear' || key === 'reportDate') continue;
      
      let value = data[key];
      
      // 格式化数值
      if (typeof value === 'number') {
        if (Math.abs(value) > 100000000) {
          value = `${(value / 100000000).toFixed(2)}亿`;
        } else if (Math.abs(value) > 10000) {
          value = `${(value / 10000).toFixed(2)}万`;
        } else {
          value = value.toFixed(2);
        }
      }
      
      dataList.push({
        label: key,
        value: value || '--'
      });
    }
  },

  /**
   * 通用数据处理
   */
  processGenericData(data, dataList, tabInfo) {
    // 遍历数据字段
    for (let key in data) {
      let value = data[key];
      
      // 格式化不同类型的值
      if (typeof value === 'number') {
        value = value.toFixed(2);
      } else if (value instanceof Date) {
        value = value.toLocaleString('zh-CN');
      } else if (typeof value === 'string' && value.includes('T') && value.includes('Z')) {
        // 尝试解析日期字符串
        try {
          value = new Date(value).toLocaleString('zh-CN');
        } catch (e) {
          // 解析失败，保持原值
        }
      }
      
      dataList.push({
        label: key,
        value: value || '--'
      });
    }
  },

  /**
   * 刷新数据
   */
  refresh() {
    this.loadStockInfo();
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时的逻辑
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 页面隐藏时的逻辑
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 页面卸载时的逻辑
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadStockInfo();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 暂不实现分页功能
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: `言策AI量化 - ${this.data.stockInfo.basicInfo['证券简称'] || '股票信息'}`,
      path: `/pages/query/stockInfo/stockInfo?stockCode=${this.data.stockCode}`,
      imageUrl: ''
    };
  }
});
