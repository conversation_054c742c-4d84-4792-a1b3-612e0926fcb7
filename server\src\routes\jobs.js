const express = require('express');
const router = express.Router();

// 全局jobScheduler实例 - 需要在主服务器启动时设置
let jobScheduler = null;

// 设置jobScheduler实例
const setJobScheduler = (scheduler) => {
  jobScheduler = scheduler;
};

/**
 * @route GET /api/jobs/status
 * @desc 获取所有定时任务状态
 * @access Public
 */
router.get('/status', (req, res) => {
  try {
    if (!jobScheduler) {
      return res.status(500).json({
        success: false,
        message: '定时任务调度器未初始化'
      });
    }

    const status = jobScheduler.getTasksStatus();
    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    console.error('获取任务状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取任务状态失败',
      error: error.message
    });
  }
});

/**
 * @route POST /api/jobs/run-daily-stock-data
 * @desc 手动执行每日股票数据任务
 * @access Public
 */
router.post('/run-daily-stock-data', async (req, res) => {
  try {
    if (!jobScheduler) {
      return res.status(500).json({
        success: false,
        message: '定时任务调度器未初始化'
      });
    }

    const result = await jobScheduler.runDailyStockDataJob();
    
    if (result.success) {
      res.json({
        success: true,
        message: result.message
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.message
      });
    }
  } catch (error) {
    console.error('手动执行任务失败:', error);
    res.status(500).json({
      success: false,
      message: '手动执行任务失败',
      error: error.message
    });
  }
});

module.exports = { router, setJobScheduler };
