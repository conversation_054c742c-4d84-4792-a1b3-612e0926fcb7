# 基础筛选页面日期选择配置说明

## 功能说明

基础筛选页面的目标日期功能已优化，现在支持开发者配置控制用户是否可以选择日期。

## 配置方式

在 `miniprogram/pages/filter/baseFilter/baseFilter.js` 文件中，修改 `data` 对象中的 `enableDateSelection` 字段：

```javascript
data: {
  // 日期选择控制（开发者配置：true-允许用户选择日期，false-锁定为昨天）
  enableDateSelection: true, // 控制用户是否可以选择日期
  // ...其他配置
}
```

## 配置选项

### enableDateSelection: true（默认）
- 用户可以通过日期选择器选择任意日期
- 默认日期为昨天
- 界面显示正常的日期选择器

### enableDateSelection: false
- 用户无法选择日期，日期选择器被禁用
- 日期锁定为昨天
- 界面显示灰色的禁用状态日期选择器
- 显示"已锁定为昨日数据"提示文字

## 功能特点

1. **默认日期优化**：无论是否允许用户选择，默认日期都设置为昨天（而非今天）
2. **禁用状态样式**：当 `enableDateSelection` 为 false 时，日期选择器会显示禁用状态
3. **自动日期重置**：当禁用日期选择时，会自动将日期重置为昨天
4. **用户友好提示**：禁用状态下会显示提示信息

## 使用场景

- **生产环境**：设置为 `false`，确保用户只查询昨天的数据
- **测试环境**：设置为 `true`，允许开发和测试人员选择任意日期进行测试
- **特定业务需求**：根据实际业务需求灵活配置

## 注意事项

- 修改配置后需要重新编译小程序
- 该配置是开发时配置，不是运行时用户可控制的功能
- 建议在不同环境中使用不同的配置值
