#!/bin/bash

# 言策 AI量化投资平台 PM2 管理脚本
# 使用方法: ./pm2-scripts.sh [command]

case "$1" in
  start)
    echo "🚀 启动言策AI量化服务器..."
    pm2 start ecosystem.config.js --env production
    ;;
  stop)
    echo "⏹️  停止言策AI量化服务器..."
    pm2 stop yance-ai-server
    ;;
  restart)
    echo "🔄 重启言策AI量化服务器..."
    pm2 restart yance-ai-server
    ;;
  reload)
    echo "🔄 重新加载言策AI量化服务器（零停机）..."
    pm2 reload yance-ai-server
    ;;
  delete)
    echo "🗑️  删除言策AI量化服务器进程..."
    pm2 delete yance-ai-server
    ;;
  status)
    echo "📊 查看言策AI量化服务器状态..."
    pm2 status yance-ai-server
    ;;
  logs)
    echo "📋 查看言策AI量化服务器日志..."
    pm2 logs yance-ai-server
    ;;
  monit)
    echo "📊 打开PM2监控面板..."
    pm2 monit
    ;;
  setup)
    echo "🔧 初始化言策AI量化环境..."
    # 创建必要的目录
    mkdir -p logs pids data
    echo "✅ 目录创建完成"
    
    # 安装依赖
    echo "📦 安装项目依赖..."
    npm install --production
    echo "✅ 依赖安装完成"
    
    # 启动应用
    pm2 start ecosystem.config.js --env production
    echo "✅ 言策AI量化应用启动完成"
    ;;
  data-sync)
    echo "📊 同步股票数据..."
    pm2 logs yance-ai-server --lines 100 | grep -i "data"
    ;;
  monitor)
    echo "📈 监控系统资源使用情况..."
    pm2 monit
    ;;
  backup)
    echo "💾 备份重要数据..."
    DATE=$(date +%Y%m%d_%H%M%S)
    mkdir -p ./backups/$DATE
    cp -r ./data ./backups/$DATE/ 2>/dev/null || echo "⚠️  数据目录不存在"
    cp -r ./logs ./backups/$DATE/
    echo "✅ 备份完成: ./backups/$DATE"
    ;;
  clean-logs)
    echo "🧹 清理旧日志文件..."
    find ./logs -name "*.log" -mtime +7 -delete
    echo "✅ 7天前的日志文件已清理"
    ;;
  health)
    echo "🏥 检查服务健康状态..."
    pm2 jlist | jq '.[] | select(.name=="yance-ai-server") | {name: .name, status: .pm2_env.status, cpu: .monit.cpu, memory: .monit.memory, uptime: .pm2_env.pm_uptime}'
    ;;
  update)
    echo "🔄 更新应用..."
    git pull origin main
    npm install --production
    pm2 reload yance-ai-server
    echo "✅ 应用更新完成"
    ;;
  *)
    echo "使用方法: $0 {start|stop|restart|reload|delete|status|logs|monit|setup|data-sync|monitor|backup|clean-logs|health|update}"
    echo ""
    echo "命令说明:"
    echo "  start      - 启动言策AI量化应用"
    echo "  stop       - 停止应用"
    echo "  restart    - 重启应用"
    echo "  reload     - 重新加载应用（零停机）"
    echo "  delete     - 删除应用进程"
    echo "  status     - 查看应用状态"
    echo "  logs       - 查看应用日志"
    echo "  monit      - 打开监控面板"
    echo "  setup      - 初始化环境并启动"
    echo "  data-sync  - 查看数据同步日志"
    echo "  monitor    - 监控系统资源"
    echo "  backup     - 备份重要数据"
    echo "  clean-logs - 清理旧日志文件"
    echo "  health     - 检查服务健康状态"
    echo "  update     - 更新应用代码"
    exit 1
esac 