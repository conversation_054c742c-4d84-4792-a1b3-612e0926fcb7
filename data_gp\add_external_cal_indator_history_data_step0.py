import pandas as pd
import os
from datetime import datetime
import numpy as np
from tqdm import tqdm
from collections import defaultdict

def load_quarterly_data(quarterly_folder):
    """加载所有季度数据 - 优化版本"""
    quarterly_data = defaultdict(list)
    
    print("正在加载季度数据...")
    for filename in tqdm(os.listdir(quarterly_folder)):
        if filename.endswith('.csv'):
            filepath = os.path.join(quarterly_folder, filename)
            try:
                # 一次性读取整个文件
                df = pd.read_csv(filepath, dtype={'证券代码': str})
                
                # 预处理：转换日期和数值
                df['报告期_dt'] = pd.to_datetime(df['报告期'])
                df['基本每股收益_float'] = pd.to_numeric(df['基本每股收益'], errors='coerce')
                
                # 按股票代码分组，避免逐行迭代
                for stock_code, group in df.groupby('证券代码'):
                    # 转换为字典列表，但保留预处理的列
                    records = group.to_dict('records')
                    quarterly_data[stock_code].extend(records)
                    
            except Exception as e:
                print(f"加载季度文件 {filename} 时出错: {e}")
                continue
    
    # 对每个股票的季度数据按报告期排序，并转换为DataFrame以提高查询效率
    print("正在排序季度数据...")
    for stock_code in tqdm(quarterly_data):
        # 转换为DataFrame并排序
        df = pd.DataFrame(quarterly_data[stock_code])
        df = df.sort_values('报告期_dt')
        quarterly_data[stock_code] = df
    
    return dict(quarterly_data)

def load_annual_data(annual_file):
    """加载年度数据 - 优化版本"""
    print("正在加载年度数据...")
    try:
        df = pd.read_csv(annual_file, dtype={'证券代码': str})
        
        # 预处理数值列
        numeric_columns = ['基本每股收益', '每股净资产']
        for col in numeric_columns:
            if col in df.columns:
                df[f'{col}_float'] = pd.to_numeric(df[col], errors='coerce')
        
        df['报告期_int'] = pd.to_numeric(df['报告期'], errors='coerce')
        
        annual_data = {}
        
        # 按股票代码分组
        for stock_code, group in df.groupby('证券代码'):
            # 排序并存储为DataFrame
            group_sorted = group.sort_values('报告期_int')
            annual_data[stock_code] = group_sorted
        
        return annual_data
    except Exception as e:
        print(f"加载年度文件时出错: {e}")
        return {}

def get_latest_annual_data(annual_data, stock_code, current_date):
    """获取指定日期前最新的年度数据 - 优化版本"""
    if stock_code not in annual_data:
        return None
    
    current_year = current_date.year
    df = annual_data[stock_code]
    
    # 使用向量化操作筛选
    valid_data = df[df['报告期_int'] <= current_year]
    
    if valid_data.empty:
        return None
    
    # 返回最新的一条记录
    return valid_data.iloc[-1]

def get_ttm_eps_vectorized(quarterly_df, current_date):
    """向量化计算TTM每股收益"""
    if quarterly_df is None or quarterly_df.empty:
        return None
    
    # 筛选当前日期之前的数据
    valid_data = quarterly_df[quarterly_df['报告期_dt'] <= current_date]
    
    if valid_data.empty:
        return None
    
    # 获取最近4个季度的有效数据
    valid_eps = valid_data['基本每股收益_float'].dropna()
    
    if len(valid_eps) >= 4:
        return valid_eps.tail(4).sum()
    else:
        return None

def calculate_new_fields_vectorized(price_data, quarterly_data, annual_data):
    """向量化计算新字段"""
    # 预处理价格数据
    price_data = price_data.copy()
    price_data['证券代码'] = price_data['证券代码'].astype(str).str.zfill(6)
    
    # 创建临时列用于计算（不会保存到最终结果）
    price_data['_temp_日期_dt'] = pd.to_datetime(price_data['日期'])
    price_data['_temp_收盘_float'] = pd.to_numeric(price_data['收盘'], errors='coerce')
    
    # 初始化新列（只包含需要的字段）
    new_columns = ['每股收益(静)', '每股收益(TTM)', '每股净资产', '市盈率(静)', '市盈率(TTM)', '市净率']
    for col in new_columns:
        price_data[col] = np.nan
    
    print("正在计算新字段...")
    
    # 按股票代码分组处理，减少重复查找
    for stock_code, group in tqdm(price_data.groupby('证券代码')):
        try:
            # 获取该股票的基础数据
            annual_df = annual_data.get(stock_code)
            quarterly_df = quarterly_data.get(stock_code)
            
            # 为该股票的每一行计算指标
            for idx in group.index:
                row = price_data.loc[idx]
                current_date = row['_temp_日期_dt']  # 使用临时列
                close_price = row['_temp_收盘_float']  # 使用临时列
                
                if pd.isna(close_price):
                    continue
                
                # 获取年度数据
                annual_info = get_latest_annual_data({stock_code: annual_df} if annual_df is not None else {}, 
                                                   stock_code, current_date)
                
                # 每股收益(静)
                eps_static = None
                book_value_per_share = None
                if annual_info is not None:
                    eps_static = annual_info.get('基本每股收益_float')
                    book_value_per_share = annual_info.get('每股净资产_float')
                
                price_data.loc[idx, '每股收益(静)'] = eps_static
                price_data.loc[idx, '每股净资产'] = book_value_per_share
                
                # 每股收益(TTM)
                eps_ttm = get_ttm_eps_vectorized(quarterly_df, current_date)
                price_data.loc[idx, '每股收益(TTM)'] = eps_ttm
                
                # 计算比率
                if pd.notna(eps_static) and eps_static != 0:
                    price_data.loc[idx, '市盈率(静)'] = close_price / eps_static
                
                if pd.notna(eps_ttm) and eps_ttm != 0:
                    price_data.loc[idx, '市盈率(TTM)'] = close_price / eps_ttm
                
                if pd.notna(book_value_per_share) and book_value_per_share != 0:
                    price_data.loc[idx, '市净率'] = close_price / book_value_per_share
                    
        except Exception as e:
            print(f"处理股票 {stock_code} 时出错: {e}")
            continue
    
    # 删除临时列
    price_data = price_data.drop(columns=['_temp_日期_dt', '_temp_收盘_float'])
    
    return price_data

def process_single_file(args):
    """处理单个文件的函数，用于并行处理"""
    filename, price_folder, quarterly_data, annual_data, output_folder = args
    
    try:
        # 读取价格数据
        price_file_path = os.path.join(price_folder, filename)
        price_data = pd.read_csv(price_file_path, dtype={'证券代码': str})
        
        # 计算新字段
        enhanced_data = calculate_new_fields_vectorized(price_data, quarterly_data, annual_data)
        
        # 保存到新文件夹
        output_file_path = os.path.join(output_folder, filename)
        enhanced_data.to_csv(output_file_path, index=False, encoding='utf-8-sig')
        
        return True, filename
        
    except Exception as e:
        return False, f"处理文件 {filename} 时出错: {str(e)}"

def main():
    # 文件夹路径
    price_folder = "企业历史日级别价格数据_带自身计算字段_end20250904"
    quarterly_folder = "企业关键指标季度"
    annual_file = "企业关键指标年度.csv"
    output_folder = "企业历史日级别价格数据_带外部计算字段_end20250904"
    
    # 检查输入文件夹是否存在
    if not os.path.exists(price_folder):
        print(f"错误：找不到文件夹 {price_folder}")
        return
    
    if not os.path.exists(quarterly_folder):
        print(f"错误：找不到文件夹 {quarterly_folder}")
        return
    
    if not os.path.exists(annual_file):
        print(f"错误：找不到文件 {annual_file}")
        return
    
    # 创建输出文件夹
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    # 加载基础数据
    quarterly_data = load_quarterly_data(quarterly_folder)
    annual_data = load_annual_data(annual_file)
    
    print(f"加载了 {len(quarterly_data)} 个股票的季度数据")
    print(f"加载了 {len(annual_data)} 个股票的年度数据")
    
    # 处理每个价格数据文件
    price_files = [f for f in os.listdir(price_folder) if f.endswith('.csv')]
    
    print(f"开始处理 {len(price_files)} 个价格数据文件...")
    
    success_count = 0
    error_count = 0
    
    # 顺序处理（如果需要并行处理，可以使用multiprocessing）
    for filename in tqdm(price_files):
        success, result = process_single_file((filename, price_folder, quarterly_data, annual_data, output_folder))
        
        if success:
            success_count += 1
        else:
            print(result)
            error_count += 1
    
    print(f"处理完成！成功: {success_count}, 失败: {error_count}")

if __name__ == "__main__":
    main()
