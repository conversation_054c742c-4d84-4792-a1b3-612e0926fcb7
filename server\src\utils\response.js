/**
 * API响应工具类
 */

/**
 * 成功响应
 * @param {object} res Express响应对象
 * @param {number} statusCode HTTP状态码
 * @param {string} message 响应消息
 * @param {object} data 响应数据
 */
exports.success = (res, statusCode = 200, message = '操作成功', data = {}) => {
  return res.status(statusCode).json({
    success: true,
    code: statusCode,
    message,
    data,
    timestamp: Date.now()
  });
};

/**
 * 错误响应
 * @param {object} res Express响应对象
 * @param {number} statusCode HTTP状态码
 * @param {string} message 错误消息
 * @param {array} errors 错误详情
 */
exports.error = (res, statusCode = 400, message = '操作失败', errors = []) => {
  const response = {
    success: false,
    code: statusCode,
    message,
    timestamp: Date.now()
  };

  if (errors && errors.length > 0) {
    response.errors = errors;
  }

  return res.status(statusCode).json(response);
};

/**
 * 400 Bad Request - 请求参数错误
 * @param {object} res Express响应对象
 * @param {string} message 错误消息
 * @param {array} errors 错误详情
 */
exports.badRequest = (res, message = '请求参数错误', errors = []) => {
  return exports.error(res, 400, message, errors);
};

/**
 * 401 Unauthorized - 未认证
 * @param {object} res Express响应对象
 * @param {string} message 错误消息
 */
exports.unauthorized = (res, message = '请先登录') => {
  return exports.error(res, 401, message);
};

/**
 * 403 Forbidden - 无权限
 * @param {object} res Express响应对象
 * @param {string} message 错误消息
 */
exports.forbidden = (res, message = '您没有权限执行此操作') => {
  return exports.error(res, 403, message);
};

/**
 * 404 Not Found - 资源不存在
 * @param {object} res Express响应对象
 * @param {string} message 错误消息
 */
exports.notFound = (res, message = '资源不存在') => {
  return exports.error(res, 404, message);
};

/**
 * 409 Conflict - 资源冲突
 * @param {object} res Express响应对象
 * @param {string} message 错误消息
 * @param {object} data 冲突数据
 */
exports.conflict = (res, message = '资源冲突', data = {}) => {
  return exports.error(res, 409, message, [], data);
};

/**
 * 413 Payload Too Large - 请求体过大
 * @param {object} res Express响应对象
 * @param {string} message 错误消息
 */
exports.payloadTooLarge = (res, message = '上传文件过大') => {
  return exports.error(res, 413, message);
};

/**
 * 415 Unsupported Media Type - 不支持的媒体类型
 * @param {object} res Express响应对象
 * @param {string} message 错误消息
 */
exports.unsupportedMediaType = (res, message = '不支持的文件类型') => {
  return exports.error(res, 415, message);
};

/**
 * 422 Unprocessable Entity - 无法处理的实体
 * @param {object} res Express响应对象
 * @param {string} message 错误消息
 * @param {object} data 错误数据
 */
exports.unprocessableEntity = (res, message = '无法处理请求', data = {}) => {
  return res.status(422).json({
    success: false,
    code: 422,
    message,
    data,
    timestamp: Date.now()
  });
};

/**
 * 500 Internal Server Error - 服务器内部错误
 * @param {object} res Express响应对象
 * @param {string} message 错误消息
 */
exports.internalServerError = (res, message = '服务器内部错误') => {
  return exports.error(res, 500, message);
}; 