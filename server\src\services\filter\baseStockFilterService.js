const DailyStockData = require('../../models/DailyStockData');
const FinancialData = require('../../models/FinancialData');
const StockBasicInfo = require('../../models/StockBasicInfo');

/**
 * 根据筛选条件筛选股票
 * @param {Object} filterConditions 筛选条件
 * @param {string} targetDate 目标日期
 * @returns {Promise<Array>} 符合条件的股票详细信息列表
 */
const baseFilterStocks = async (filterConditions, targetDate) => {
  try {
    const {
      dailyConditions = {},
      financialConditions = {},
      basicInfoConditions = {}
    } = filterConditions;

    // 获取当日数据符合条件的股票代码
    let dailyStockCodes = [];
    if (Object.keys(dailyConditions).length > 0) {
      dailyStockCodes = await filterByDailyData(dailyConditions, targetDate);
      // 如果设置了每日条件但没有找到任何股票，直接返回空结果
      if (dailyStockCodes.length === 0) {
        return [];
      }
    }

    // 获取基本信息符合条件的股票代码
    let basicInfoStockCodes = [];
    if (Object.keys(basicInfoConditions).length > 0) {
      basicInfoStockCodes = await filterByBasicInfo(basicInfoConditions);
      // 如果设置了基本信息条件但没有找到任何股票，直接返回空结果
      if (basicInfoStockCodes.length === 0) {
        return [];
      }
    }

    // 获取财务数据符合条件的股票代码
    let financialStockCodes = [];
    if (Object.keys(financialConditions).length > 0) {
      financialStockCodes = await filterByFinancialData(financialConditions);
      // 如果设置了财务条件但没有找到任何股票，直接返回空结果
      if (financialStockCodes.length === 0) {
        return [];
      }
    }

    // 计算交集 - 所有有数据的条件都必须满足
    let resultStockCodes = [];

    // 收集所有有效的股票代码数组
    const validStockCodeArrays = [];
    if (dailyStockCodes.length > 0) {
      validStockCodeArrays.push(dailyStockCodes);
    }
    if (basicInfoStockCodes.length > 0) {
      validStockCodeArrays.push(basicInfoStockCodes);
    }
    if (financialStockCodes.length > 0) {
      validStockCodeArrays.push(financialStockCodes);
    }

    // 如果没有任何条件，返回空结果
    if (validStockCodeArrays.length === 0) {
      return [];
    }

    // 计算所有数组的交集
    resultStockCodes = validStockCodeArrays.reduce((intersection, currentArray) => {
      return intersection.filter(code => currentArray.includes(code));
    });


    // 获取详细信息
    const detailedResults = await getStockDetailedInfo(resultStockCodes, filterConditions, targetDate);
    
    return detailedResults;
  } catch (error) {
    throw new Error(`股票筛选失败: ${error.message}`);
  }
};

/**
 * 根据当日数据筛选股票
 * @param {Object} conditions 当日数据筛选条件
 * @param {string} targetDate 目标日期
 * @returns {Promise<Array>} 符合条件的股票代码列表
 */
const filterByDailyData = async (conditions, targetDate) => {
  const query = {};
  
  // 设置日期条件
  // if (targetDate) {
  //   const date = new Date(targetDate);
  //   const nextDate = new Date(date);
  //   nextDate.setDate(nextDate.getDate() + 1);
  //   query.timestamp = { $gte: date, $lt: nextDate };
  // } else {
  //   // 如果没有指定日期，获取最新数据
  //   // 这里可以根据需要调整逻辑
  // }

  // 数值范围筛选条件映射
  const rangeFields = {
    peRatioDynamicRange: 'peRatioDynamic',
    peRatioStaticRange: 'peRatioStatic',
    peRatioTTMRange: 'peRatioTTM',
    pbRatioRange: 'pbRatio',
    totalMarketCapRange: 'totalMarketCap',
    circulatingMarketCapRange: 'circulatingMarketCap',
    turnoverRateRange: 'turnoverRate',
    changePercentRange: 'changePercent',
    amplitudeRange: 'amplitude',
    ytdGainRange: 'ytdGain',
    sixtyDayChangeRange: 'sixtyDayChange',
    dividendYieldTTMRange: 'dividendYieldTTM'
  };

  // 处理数值范围条件
  Object.entries(rangeFields).forEach(([conditionKey, fieldName]) => {
    if (conditions[conditionKey]) {
      const { min, max } = conditions[conditionKey];
      const rangeQuery = {};
      
      if (min !== undefined && min !== null) {
        rangeQuery.$gte = min;
      }
      if (max !== undefined && max !== null) {
        rangeQuery.$lte = max;
      }
      
      if (Object.keys(rangeQuery).length > 0) {
        query[fieldName] = rangeQuery;
      }
    }
  });

  // 处理涨停跌停条件
  if (conditions.isLimitUp === true) {
    query.$expr = { $gte: ['$closePrice', '$limitUpPrice'] };
  }
  if (conditions.isLimitDown === true) {
    query.$expr = { $lte: ['$closePrice', '$limitDownPrice'] };
  }
  if (conditions.touchedLimitUp === true) {
    query.$expr = { $gte: ['$highPrice', '$limitUpPrice'] };
  }
  if (conditions.touchedLimitDown === true) {
    query.$expr = { $lte: ['$lowPrice', '$limitDownPrice'] };
  }

  const results = await DailyStockData.find(query, { stockCode: 1, _id: 0 });
  console.log('符合条件的股票数量:', results.length);
  return results.map(item => item.stockCode);
};

/**
 * 根据基本信息筛选股票
 * @param {Object} conditions 基本信息筛选条件
 * @returns {Promise<Array>} 符合条件的股票代码列表
 */
const filterByBasicInfo = async (conditions) => {
  const query = {};

  // 处理板块筛选
  if (conditions.board && conditions.board.length > 0) {
    query.board = { $in: conditions.board };
  }

  // 处理行业筛选
  if (conditions.industry && conditions.industry.length > 0) {
    query.industry = { $in: conditions.industry };
  }

  // 处理已上市年份范围
  if (conditions.listedYearsRange) {
    const { min, max } = conditions.listedYearsRange;
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    
    // 计算上市日期范围
    let startDate = null;
    let endDate = null;
    
    if (max !== undefined && max !== null) {
      // 最多上市max年，即上市时间不早于 (currentYear - max) 年
      startDate = new Date(`${currentYear - max}-01-01`);
    }
    
    if (min !== undefined && min !== null) {
      // 最少上市min年，即上市时间不晚于 (currentYear - min) 年
      endDate = new Date(`${currentYear - min + 1}-01-01`);
    }
    
    if (startDate || endDate) {
      const dateQuery = {};
      if (startDate) {
        dateQuery.$gte = startDate;
      }
      if (endDate) {
        dateQuery.$lt = endDate;
      }
      query.listingDate = dateQuery;
    }
  }

  const results = await StockBasicInfo.find(query, { stockCode: 1, _id: 0 });
  return results.map(item => item.stockCode);
};

/**
 * 根据财务数据筛选股票
 * @param {Object} conditions 财务数据筛选条件
 * @returns {Promise<Array>} 符合条件的股票代码列表
 */
const filterByFinancialData = async (conditions) => {
  // 获取最新年度的财务数据
  const latestYear = await getLatestFinancialYear();
  
  const query = {
    reportType: '按年度',
    fiscalYear: latestYear
  };

  // 数值范围筛选条件映射
  const rangeFields = {
    netProfitGrowthRateRange: 'data.keyMetrics.netProfitGrowthRate',
    nonRecurringNetProfitGrowthRateRange: 'data.keyMetrics.nonRecurringNetProfitGrowthRate',
    totalRevenueGrowthRateRange: 'data.keyMetrics.totalRevenueGrowthRate',
    netProfitMarginRange: 'data.keyMetrics.netProfitMargin',
    grossProfitMarginRange: 'data.keyMetrics.grossProfitMargin',
    returnOnEquityRange: 'data.keyMetrics.returnOnEquity',
    dilutedReturnOnEquityRange: 'data.keyMetrics.dilutedReturnOnEquity',
    currentRatioRange: 'data.keyMetrics.currentRatio',
    quickRatioRange: 'data.keyMetrics.quickRatio',
    equityRatioRange: 'data.keyMetrics.equityRatio',
    debtToAssetRatioRange: 'data.keyMetrics.debtToAssetRatio',
    inventoryTurnoverRatioRange: 'data.keyMetrics.inventoryTurnoverRatio',
    inventoryTurnoverDaysRange: 'data.keyMetrics.inventoryTurnoverDays',
    accountsReceivableTurnoverDaysRange: 'data.keyMetrics.accountsReceivableTurnoverDays'
  };

  // 处理数值范围条件
  Object.entries(rangeFields).forEach(([conditionKey, fieldPath]) => {
    if (conditions[conditionKey]) {
      const { min, max } = conditions[conditionKey];
      const rangeQuery = {};
      
      if (min !== undefined && min !== null) {
        rangeQuery.$gte = min;
      }
      if (max !== undefined && max !== null) {
        rangeQuery.$lte = max;
      }
      
      if (Object.keys(rangeQuery).length > 0) {
        query[fieldPath] = rangeQuery;
      }
    }
  });

  const results = await FinancialData.find(query, { stockCode: 1, _id: 0 });
  return results.map(item => item.stockCode);
};

/**
 * 获取最新的财务数据年度
 * @returns {Promise<number>} 最新年度
 */
const getLatestFinancialYear = async () => {
  const result = await FinancialData.findOne(
    { reportType: '按年度' },
    { fiscalYear: 1, _id: 0 }
  ).sort({ fiscalYear: -1 });
  
  return result ? result.fiscalYear : new Date().getFullYear() - 1;
};

/**
 * 获取股票详细信息
 * @param {Array} stockCodes 股票代码列表
 * @param {Object} filterConditions 筛选条件
 * @param {string} targetDate 目标日期
 * @returns {Promise<Array>} 股票详细信息列表
 */
const getStockDetailedInfo = async (stockCodes, filterConditions, targetDate) => {
  if (stockCodes.length === 0) {
    return [];
  }

  // 获取基本信息
  const basicInfoData = await StockBasicInfo.find(
    { stockCode: { $in: stockCodes } },
    { stockCode: 1, stockName: 1, board: 1, industry: 1, _id: 0 }
  );

  // 创建基本信息映射
  const basicInfoMap = basicInfoData.reduce((map, item) => {
    map[item.stockCode] = item;
    return map;
  }, {});

  // 获取当日数据
  let dailyDataMap = {};
  if (Object.keys(filterConditions.dailyConditions || {}).length > 0) {
    const dailyData = await getDailyDataForStocks(stockCodes, targetDate);
    dailyDataMap = dailyData.reduce((map, item) => {
      map[item.stockCode] = item;
      return map;
    }, {});
  }

  // 获取财务数据
  let financialDataMap = {};
  if (Object.keys(filterConditions.financialConditions || {}).length > 0) {
    const financialData = await getFinancialDataForStocks(stockCodes);
    financialDataMap = financialData.reduce((map, item) => {
      map[item.stockCode] = item;
      return map;
    }, {});
  }

  // 获取字段的中文名称（使用各个模型中的映射方法）
  const getChineseFieldName = (englishField) => {
    // 尝试从 DailyStockData 模型获取映射
    let chineseName = DailyStockData.getChineseFieldName(englishField);
    if (chineseName !== englishField) {
      return chineseName;
    }
    
    // 尝试从 StockBasicInfo 模型获取映射
    chineseName = StockBasicInfo.getChineseFieldName(englishField);
    if (chineseName !== englishField) {
      return chineseName;
    }
    
    // 尝试从 FinancialData 模型获取映射
    chineseName = FinancialData.getChineseFieldName(englishField);
    if (chineseName !== englishField) {
      return chineseName;
    }
    
    // 如果都没找到，返回原字段名
    return englishField;
  };

  // 组装结果
  const results = stockCodes.map(stockCode => {
    const result = {
      [getChineseFieldName('stockCode')]: stockCode,
      [getChineseFieldName('stockName')]: basicInfoMap[stockCode]?.stockName || '',
    };

    // 添加基本信息字段（如果筛选条件中有相关条件）
    const basicInfoConditions = filterConditions.basicInfoConditions || {};
    if (basicInfoConditions.board || basicInfoConditions.industry) {
      if (basicInfoMap[stockCode]?.board) result[getChineseFieldName('board')] = basicInfoMap[stockCode].board;
      if (basicInfoMap[stockCode]?.industry) result[getChineseFieldName('industry')] = basicInfoMap[stockCode].industry;
    }

    // 添加当日数据（只返回数据表中存在的字段）
    if (dailyDataMap[stockCode]) {
      const dailyData = dailyDataMap[stockCode];
      const dailyConditions = filterConditions.dailyConditions || {};
      
      // 数值范围条件对应的字段
      if (dailyConditions.peRatioDynamicRange) result[getChineseFieldName('peRatioDynamic')] = dailyData.peRatioDynamic;
      if (dailyConditions.peRatioStaticRange) result[getChineseFieldName('peRatioStatic')] = dailyData.peRatioStatic;
      if (dailyConditions.peRatioTTMRange) result[getChineseFieldName('peRatioTTM')] = dailyData.peRatioTTM;
      if (dailyConditions.pbRatioRange) result[getChineseFieldName('pbRatio')] = dailyData.pbRatio;
      if (dailyConditions.totalMarketCapRange) result[getChineseFieldName('totalMarketCap')] = dailyData.totalMarketCap;
      if (dailyConditions.circulatingMarketCapRange) result[getChineseFieldName('circulatingMarketCap')] = dailyData.circulatingMarketCap;
      if (dailyConditions.turnoverRateRange) result[getChineseFieldName('turnoverRate')] = dailyData.turnoverRate;
      if (dailyConditions.changePercentRange) result[getChineseFieldName('changePercent')] = dailyData.changePercent;
      if (dailyConditions.amplitudeRange) result[getChineseFieldName('amplitude')] = dailyData.amplitude;
      if (dailyConditions.ytdGainRange) result[getChineseFieldName('ytdGain')] = dailyData.ytdGain;
      if (dailyConditions.sixtyDayChangeRange) result[getChineseFieldName('sixtyDayChange')] = dailyData.sixtyDayChange;
      if (dailyConditions.dividendYieldTTMRange) result[getChineseFieldName('dividendYieldTTM')] = dailyData.dividendYieldTTM;
      
      // 布尔条件对应的原始数据字段（不返回计算后的布尔值）
      if (dailyConditions.isLimitUp !== undefined) {
        result[getChineseFieldName('closePrice')] = dailyData.closePrice;
        result[getChineseFieldName('limitUpPrice')] = dailyData.limitUpPrice;
      }
      if (dailyConditions.isLimitDown !== undefined) {
        result[getChineseFieldName('closePrice')] = dailyData.closePrice;
        result[getChineseFieldName('limitDownPrice')] = dailyData.limitDownPrice;
      }
      if (dailyConditions.touchedLimitUp !== undefined) {
        result[getChineseFieldName('highPrice')] = dailyData.highPrice;
        result[getChineseFieldName('limitUpPrice')] = dailyData.limitUpPrice;
      }
      if (dailyConditions.touchedLimitDown !== undefined) {
        result[getChineseFieldName('lowPrice')] = dailyData.lowPrice;
        result[getChineseFieldName('limitDownPrice')] = dailyData.limitDownPrice;
      }
    }

    // 添加财务数据（只返回数据表中存在的字段）
    if (financialDataMap[stockCode]) {
      const financialData = financialDataMap[stockCode];
      const financialConditions = filterConditions.financialConditions || {};
      
      if (financialConditions.netProfitGrowthRateRange) result[getChineseFieldName('netProfitGrowthRate')] = financialData.data?.keyMetrics?.netProfitGrowthRate;
      if (financialConditions.nonRecurringNetProfitGrowthRateRange) result[getChineseFieldName('nonRecurringNetProfitGrowthRate')] = financialData.data?.keyMetrics?.nonRecurringNetProfitGrowthRate;
      if (financialConditions.totalRevenueGrowthRateRange) result[getChineseFieldName('totalRevenueGrowthRate')] = financialData.data?.keyMetrics?.totalRevenueGrowthRate;
      if (financialConditions.netProfitMarginRange) result[getChineseFieldName('netProfitMargin')] = financialData.data?.keyMetrics?.netProfitMargin;
      if (financialConditions.grossProfitMarginRange) result[getChineseFieldName('grossProfitMargin')] = financialData.data?.keyMetrics?.grossProfitMargin;
      if (financialConditions.returnOnEquityRange) result[getChineseFieldName('returnOnEquity')] = financialData.data?.keyMetrics?.returnOnEquity;
      if (financialConditions.dilutedReturnOnEquityRange) result[getChineseFieldName('dilutedReturnOnEquity')] = financialData.data?.keyMetrics?.dilutedReturnOnEquity;
      if (financialConditions.currentRatioRange) result[getChineseFieldName('currentRatio')] = financialData.data?.keyMetrics?.currentRatio;
      if (financialConditions.quickRatioRange) result[getChineseFieldName('quickRatio')] = financialData.data?.keyMetrics?.quickRatio;
      if (financialConditions.equityRatioRange) result[getChineseFieldName('equityRatio')] = financialData.data?.keyMetrics?.equityRatio;
      if (financialConditions.debtToAssetRatioRange) result[getChineseFieldName('debtToAssetRatio')] = financialData.data?.keyMetrics?.debtToAssetRatio;
      if (financialConditions.inventoryTurnoverRatioRange) result[getChineseFieldName('inventoryTurnoverRatio')] = financialData.data?.keyMetrics?.inventoryTurnoverRatio;
      if (financialConditions.inventoryTurnoverDaysRange) result[getChineseFieldName('inventoryTurnoverDays')] = financialData.data?.keyMetrics?.inventoryTurnoverDays;
      if (financialConditions.accountsReceivableTurnoverDaysRange) result[getChineseFieldName('accountsReceivableTurnoverDays')] = financialData.data?.keyMetrics?.accountsReceivableTurnoverDays;
    }

    return result;
  });

  return results;
};

/**
 * 获取指定股票的当日数据
 * @param {Array} stockCodes 股票代码列表
 * @param {string} targetDate 目标日期
 * @returns {Promise<Array>} 当日数据列表
 */
const getDailyDataForStocks = async (stockCodes, targetDate) => {
  const query = { stockCode: { $in: stockCodes } };
  
  // if (targetDate) {
  //   const date = new Date(targetDate);
  //   const nextDate = new Date(date);
  //   nextDate.setDate(nextDate.getDate() + 1);
  //   query.timestamp = { $gte: date, $lt: nextDate };
  // }

  return await DailyStockData.find(query);
};

/**
 * 获取指定股票的财务数据
 * @param {Array} stockCodes 股票代码列表
 * @returns {Promise<Array>} 财务数据列表
 */
const getFinancialDataForStocks = async (stockCodes) => {
  const latestYear = await getLatestFinancialYear();
  
  return await FinancialData.find({
    stockCode: { $in: stockCodes },
    reportType: '按年度',
    fiscalYear: latestYear
  });
};


module.exports = { baseFilterStocks };
