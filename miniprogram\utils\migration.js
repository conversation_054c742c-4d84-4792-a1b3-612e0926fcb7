/**
 * 数据迁移工具
 * 用于清理旧版本的重复存储字段
 */

const storage = require('./storage');

/**
 * 迁移用户数据
 * 将旧的双重字段数据迁移到新的统一字段
 */
function migrateUserData() {
  try {
    console.log('开始数据迁移...');
    
    // 迁移用户信息
    const oldUserInfo = wx.getStorageSync('userInfo'); // 旧字段
    const newUserInfo = storage.getUserInfo(); // 新字段
    
    if (oldUserInfo && !newUserInfo) {
      // 如果旧字段有数据但新字段没有，则迁移
      storage.setUserInfo(oldUserInfo);
      console.log('用户信息迁移完成');
    }
    
    // 迁移token
    const oldToken = wx.getStorageSync('token'); // 旧字段(对象格式)
    const currentToken = storage.getToken(); // 新字段
    
    if (oldToken && !currentToken) {
      // 处理token格式兼容性
      const tokenValue = typeof oldToken === 'string' ? oldToken : oldToken.accessToken;
      if (tokenValue) {
        storage.setToken(tokenValue);
        console.log('Token迁移完成');
      }
    }
    
    // 清理旧字段
    cleanupOldFields();
    
    console.log('数据迁移完成');
  } catch (error) {
    console.error('数据迁移失败:', error);
  }
}

/**
 * 清理旧的存储字段
 */
function cleanupOldFields() {
  try {
    // 清理旧的用户信息字段
    wx.removeStorageSync('userInfo');
    
    // 清理旧的token字段
    wx.removeStorageSync('token');
    
    console.log('旧字段清理完成');
  } catch (error) {
    console.error('清理旧字段失败:', error);
  }
}

/**
 * 检查是否需要迁移
 * @returns {boolean} 是否需要迁移
 */
function needMigration() {
  const hasOldUserInfo = !!wx.getStorageSync('userInfo');
  const hasOldToken = !!wx.getStorageSync('token');
  
  return hasOldUserInfo || hasOldToken;
}

/**
 * 获取存储信息统计
 */
function getStorageStats() {
  try {
    const info = wx.getStorageInfoSync();
    const stats = {
      total: info.keys.length,
      userRelated: 0,
      cache: 0,
      other: 0
    };
    
    info.keys.forEach(key => {
      if (key.includes('user') || key.includes('token') || key.includes('access')) {
        stats.userRelated++;
      } else if (key.startsWith('cache_')) {
        stats.cache++;
      } else {
        stats.other++;
      }
    });
    
    console.log('存储统计:', stats);
    return stats;
  } catch (error) {
    console.error('获取存储统计失败:', error);
    return null;
  }
}

module.exports = {
  migrateUserData,
  cleanupOldFields,
  needMigration,
  getStorageStats
};
