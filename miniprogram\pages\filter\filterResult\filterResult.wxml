<!-- 筛选结果页面 -->
<view class="result-page">
  <!-- 页面标题栏 -->
  <view class="page-header">
    <text class="page-title">筛选结果</text>
    <text class="page-subtitle">共找到 {{stockList.length}} 只股票</text>
  </view>

  <!-- 筛选条件回顾 -->
  <view class="conditions-summary" wx:if="{{conditionsList.length > 0}}">
    <view class="summary-header">
      <text class="summary-title">筛选条件</text>
      <view class="summary-toggle {{showConditions ? 'expanded' : ''}}" bindtap="toggleConditionsShow">
        {{showConditions ? '收起' : '展开'}}
      </view>
    </view>
    <view class="conditions-content" wx:if="{{showConditions}}">
      <view class="condition-item" wx:for="{{conditionsList}}" wx:key="label">
        <text class="condition-label">{{item.label}}:</text>
        <text class="condition-value">{{item.value}}</text>
      </view>
    </view>
  </view>

  <!-- 结果统计信息 -->
  <view class="result-stats">
    <view class="stats-item">
      <text class="stats-number">{{stockList.length}}</text>
      <text class="stats-label">符合条件</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{tableColumns.length}}</text>
      <text class="stats-label">数据字段</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{targetDate}}</text>
      <text class="stats-label">筛选日期</text>
    </view>
  </view>

  <!-- 结果表格 -->
  <view class="result-container">
    <view class="table-header" wx:if="{{stockList.length > 0}}">
      <view class="header-actions">
        <text class="result-title">股票列表</text>
        <text class="page-info">第{{currentPage}}页，共{{totalPages}}页</text>
      </view>
    </view>

    <!-- 数据表格 -->
    <scroll-view class="table-scroll" scroll-x="{{true}}" wx:if="{{stockList.length > 0}}">
      <view class="data-table">
        <!-- 表头 -->
        <view class="table-header-row">
          <view class="table-cell header-cell" wx:for="{{tableColumns}}" wx:key="*this">
            {{item}}
          </view>
        </view>
        
        <!-- 数据行 -->
        <view class="table-row" wx:for="{{displayStockList}}" wx:key="index" wx:for-item="stock" wx:for-index="rowIndex">
          <view class="table-cell data-cell" wx:for="{{tableColumns}}" wx:key="*this" wx:for-item="column">
            <text class="cell-content {{column === '证券代码' ? 'stock-code-link' : ''}}" 
                  bindtap="{{column === '证券代码' ? 'onStockCodeTap' : ''}}" 
                  data-stock-code="{{stock[column]}}">{{stock[column] || '--'}}</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 数据状态提示 -->
    <view class="load-more" wx:if="{{stockList.length > 0}}">
      <view class="load-more-content" wx:if="{{hasMore}}">
        <view class="loading-indicator" wx:if="{{loading}}">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载中...</text>
        </view>
        <view class="load-more-hint" wx:else>
          <text>当前显示第 {{currentPage}} 页数据，使用下方分页按钮查看更多</text>
        </view>
      </view>
      <view class="load-more-end" wx:else>
        <text>已显示全部 {{stockList.length}} 条数据</text>
      </view>
    </view>

    <!-- 分页控制器 -->
    <view class="pagination-controls" wx:if="{{totalPages > 1}}">
      <button class="page-btn {{currentPage === 1 ? 'disabled' : ''}}" 
              bindtap="prevPage" 
              disabled="{{currentPage === 1}}">
        上一页
      </button>
      
      <view class="page-numbers" bindtap="showPageSelector">
        <text class="page-current">{{currentPage}}</text>
        <text class="page-separator">/</text>
        <text class="page-total">{{totalPages}}</text>
      </view>
      
      <button class="page-btn {{currentPage === totalPages ? 'disabled' : ''}}" 
              bindtap="nextPage" 
              disabled="{{currentPage === totalPages}}">
        下一页
      </button>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{stockList.length === 0 && !loading}}">
      <view class="empty-icon">📊</view>
      <text class="empty-title">暂无符合条件的股票</text>
      <text class="empty-desc">请调整筛选条件后重新查询</text>
      <button class="empty-btn" bindtap="goBackToFilter">重新筛选</button>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions" wx:if="{{stockList.length > 0}}">
    <button class="action-button secondary" bindtap="goBackToFilter">
      重新筛选
    </button>
    <button class="action-button primary" bindtap="saveResults">
      保存结果
    </button>
  </view>
</view>
