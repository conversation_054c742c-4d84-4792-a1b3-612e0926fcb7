const axios = require('axios');

/**
 * 专门测试基本信息筛选功能的测试脚本
 */

// 配置
const baseURL = 'http://localhost:3000';
const apiPrefix = '/api/v1/filter';

async function testBasicInfoFilter() {
  console.log('🧪 开始测试基本信息筛选功能');
  console.log('📍 服务器地址:', baseURL);
  console.log('📅 测试时间:', new Date().toLocaleString('zh-CN'));
  console.log('='.repeat(60));

  // 测试1：板块筛选
  await runTest('🏢 测试板块筛选', {
    basicInfoConditions: {
      board: ['沪市主板', '深市主板']
    }
  });

  // 测试2：行业筛选
  await runTest('🏭 测试行业筛选', {
    basicInfoConditions: {
      industry: ['银行', '证券', '保险']
    }
  });

  // 测试3：已上市年份筛选 - 新股 (上市1-3年)
  await runTest('🆕 测试新股筛选 (上市1-3年)', {
    basicInfoConditions: {
      listedYearsRange: { min: 1, max: 3 }
    }
  });

  // 测试4：已上市年份筛选 - 老股 (上市15年以上)
  await runTest('👴 测试老股筛选 (上市15年以上)', {
    basicInfoConditions: {
      listedYearsRange: { min: 15, max: null }
    }
  });

  // 测试5：组合筛选 - 特定板块+行业+上市年份
  await runTest('🎯 测试组合筛选 (沪市主板+银行+上市10年以上)', {
    basicInfoConditions: {
      board: ['沪市主板'],
      industry: ['银行'],
      listedYearsRange: { min: 10, max: null }
    }
  });

  // 测试6：结合当日数据的综合筛选
  await runTest('🔥 测试综合筛选 (基本信息+当日数据)', {
    basicInfoConditions: {
      board: ['沪市主板', '深市主板'],
      listedYearsRange: { min: 5, max: null }
    },
    dailyConditions: {
      peRatioTTMRange: { min: 5, max: 50 },
      totalMarketCapRange: { min: 1000000000, max: null }
    },
    targetDate: '2025-09-12'
  });

  // 测试7：三维度综合筛选
  await runTest('💎 测试三维度综合筛选 (基本信息+当日数据+财务数据)', {
    basicInfoConditions: {
      board: ['沪市主板'],
      industry: ['银行', '证券'],
      listedYearsRange: { min: 8, max: null }
    },
    dailyConditions: {
      peRatioTTMRange: { min: 3, max: 20 },
      pbRatioRange: { min: 0.5, max: 2 }
    },
    financialConditions: {
      returnOnEquityRange: { min: 0.1, max: null },
      debtToAssetRatioRange: { min: null, max: 0.8 }
    },
    targetDate: '2025-09-12'
  });

  console.log('\n🎉 基本信息筛选功能测试完成！');
}

async function runTest(testName, filterConditions) {
  console.log(`\n${testName}`);
  console.log('-'.repeat(50));
  
  try {
    const url = `${baseURL}${apiPrefix}/stocks`;
    console.log('📤 筛选条件:', JSON.stringify(filterConditions, null, 2));
    
    const startTime = Date.now();
    const response = await axios.post(url, filterConditions);
    const endTime = Date.now();
    
    const duration = endTime - startTime;
    
    console.log('📥 响应状态:', response.status);
    console.log('⏱️  响应时间:', `${duration}ms`);
    
    if (response.status === 200) {
      const { stockCodes, count } = response.data.data;
      console.log(`📊 筛选结果: 找到 ${count} 只符合条件的股票`);
      
      if (count > 0) {
        if (count <= 20) {
          console.log('🏷️  股票代码:', stockCodes.join(', '));
        } else {
          console.log('🏷️  前20只股票代码:', stockCodes.slice(0, 20).join(', '));
          console.log(`     ... 还有${count - 20}只股票`);
        }
      }
      
      console.log('✅ 测试通过');
    } else {
      console.log('⚠️  状态码不符合预期');
    }
    
  } catch (error) {
    if (error.response) {
      console.log('📥 响应状态:', error.response.status);
      console.log('📥 错误信息:', error.response.data.message || error.response.data.error);
      console.log('❌ 测试失败');
    } else {
      console.log('❌ 网络错误:', error.message);
      console.log('💡 请确保后端服务在', baseURL, '上运行');
    }
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testBasicInfoFilter().catch(error => {
    console.error('💥 测试运行失败:', error.message);
  });
}

module.exports = { testBasicInfoFilter };
