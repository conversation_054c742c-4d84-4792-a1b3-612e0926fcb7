{"testCase": "testCase1", "description": "连续三年净利润大于0的企业", "timestamp": "2025-09-22T07:57:12.450Z", "result": {"success": true, "userInput": "连续三年净利润大于0的企业", "logicAnalysis": {"needSplit": false, "reason": "这是一个简单的多期数据筛选需求，可以通过直接查询连续三年的净利润数据实现", "singleLogic": {"description": "筛选连续三年净利润都大于零的企业", "type": "simple_filter", "complexity": "low"}}, "executableScripts": [{"logicIndex": 1, "logicDescription": "筛选连续三年净利润都大于零的企业", "logicType": "simple_filter", "complexity": "low", "scriptCode": "const executeLogic = async () => {\n  try {\n    const currentYear = new Date().getFullYear();\n    const years = [currentYear - 2, currentYear - 1, currentYear];\n    \n    const result = await FinancialData.aggregate([\n      {\n        $match: {\n          fiscalYear: { $in: years },\n          \"data.keyMetrics.netProfit\": { $gt: 0 }\n        }\n      },\n      {\n        $group: {\n          _id: \"$stockCode\",\n          yearCount: { $sum: 1 },\n          years: { $push: \"$fiscalYear\" }\n        }\n      },\n      {\n        $match: {\n          yearCount: 3\n        }\n      },\n      {\n        $lookup: {\n          from: \"stockbasicinfos\",\n          localField: \"_id\",\n          foreignField: \"stockCode\",\n          as: \"stockInfo\"\n        }\n      },\n      {\n        $unwind: \"$stockInfo\"\n      },\n      {\n        $project: {\n          stockCode: \"$_id\",\n          stockName: \"$stockInfo.stockName\",\n          board: \"$stockInfo.board\",\n          industry: \"$stockInfo.industry\",\n          years: 1,\n          _id: 0\n        }\n      }\n    ]);\n\n    return result;\n  } catch (error) {\n    throw new Error(`查询失败: ${error.message}`);\n  }\n};", "generatedAt": "2025-09-22T07:57:12.448Z"}], "timestamp": "2025-09-22T07:57:12.449Z"}}