const fs = require('fs');
const path = require('path');

/**
 * 清理CSV文件的BOM字符
 */
async function cleanCSVFile() {
  const originalPath = path.join(__dirname, '../../data_gp/企业关键指标年度.csv');
  const cleanedPath = path.join(__dirname, '../../data_gp/企业关键指标年度_cleaned.csv');
  const backupPath = path.join(__dirname, '../../data_gp/企业关键指标年度_backup.csv');

  console.log('开始清理CSV文件的BOM字符...');
  console.log('原文件:', originalPath);
  console.log('清理后文件:', cleanedPath);

  try {
    // 检查原文件是否存在
    if (!fs.existsSync(originalPath)) {
      throw new Error(`原文件不存在: ${originalPath}`);
    }

    // 读取原文件
    console.log('读取原文件...');
    const originalBuffer = fs.readFileSync(originalPath);
    
    // 检查是否有BOM
    let cleanedBuffer = originalBuffer;
    if (originalBuffer.length >= 3 && 
        originalBuffer[0] === 0xEF && 
        originalBuffer[1] === 0xBB && 
        originalBuffer[2] === 0xBF) {
      console.log('发现BOM字符，正在移除...');
      cleanedBuffer = originalBuffer.slice(3); // 移除前3个字节的BOM
    } else {
      console.log('未发现BOM字符');
    }

    // 创建备份
    console.log('创建备份文件...');
    fs.copyFileSync(originalPath, backupPath);

    // 写入清理后的文件
    console.log('写入清理后的文件...');
    fs.writeFileSync(cleanedPath, cleanedBuffer);

    // 验证清理结果
    const cleanedContent = fs.readFileSync(cleanedPath, 'utf8');
    const firstLine = cleanedContent.split('\n')[0];
    const firstChar = firstLine.charAt(0);
    
    console.log('清理结果验证:');
    console.log(`  第一行: ${firstLine.substring(0, 50)}...`);
    console.log(`  第一个字符: "${firstChar}" (Unicode: ${firstChar.charCodeAt(0)})`);
    console.log(`  是否为中文: ${/[\u4e00-\u9fff]/.test(firstChar)}`);

    console.log('\n✅ CSV文件清理完成！');
    console.log(`备份文件: ${backupPath}`);
    console.log(`清理后文件: ${cleanedPath}`);
    console.log('\n建议：');
    console.log('1. 使用清理后的文件进行导入');
    console.log('2. 或者将清理后的文件重命名为原文件名');

    return cleanedPath;

  } catch (error) {
    console.error('清理过程中发生错误:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  cleanCSVFile()
    .then((cleanedPath) => {
      console.log('\n🎉 清理完成');
      console.log('请考虑使用以下命令替换原文件:');
      console.log(`copy "${cleanedPath}" "${path.join(__dirname, '../../data_gp/企业关键指标年度.csv')}"`);
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 清理失败:', error);
      process.exit(1);
    });
}

module.exports = { cleanCSVFile };
