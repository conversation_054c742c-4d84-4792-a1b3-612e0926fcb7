const { success, error } = require('../../utils/response');
const logoutService = require('../../services/auth/logoutService');

/**
 * 退出登录
 * @route POST /api/v1/auth/logout
 * @access Private
 */
const logout = async (req, res, next) => {
  try {
    // 退出登录
    await logoutService.logout(req.user.id);

    // 返回成功响应
    return success(res, 200, '已成功退出登录');
  } catch (err) {
    next(err);
  }
};

exports.logout = logout;