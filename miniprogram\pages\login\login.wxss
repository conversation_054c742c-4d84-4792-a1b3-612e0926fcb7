/* pages/login/login.wxss */
@import "../../styles/base.wxss";
@import "../../styles/mixins.wxss";

.login-page {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: var(--bg-primary);
  overflow: hidden;
}

/* 背景装饰 */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.bg-decoration {
  position: absolute;
  border-radius: 50%;
  opacity: 0.05;
  background: var(--primary-gradient);
}

.bg-decoration-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
  animation: float1 6s ease-in-out infinite;
}

.bg-decoration-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: -30rpx;
  animation: float2 8s ease-in-out infinite;
}

.bg-decoration-3 {
  width: 120rpx;
  height: 120rpx;
  top: 80%;
  right: 50rpx;
  animation: float3 7s ease-in-out infinite;
}

/* 浮动动画 */
@keyframes float1 {
  0%, 100% { transform: translateY(0rpx) rotate(0deg); }
  50% { transform: translateY(-20rpx) rotate(180deg); }
}

@keyframes float2 {
  0%, 100% { transform: translateX(0rpx) rotate(0deg); }
  50% { transform: translateX(15rpx) rotate(-180deg); }
}

@keyframes float3 {
  0%, 100% { transform: translateY(0rpx) translateX(0rpx) rotate(0deg); }
  33% { transform: translateY(-10rpx) translateX(10rpx) rotate(120deg); }
  66% { transform: translateY(10rpx) translateX(-10rpx) rotate(240deg); }
}

/* 登录内容 */
.login-content {
  position: relative;
  z-index: 1;
  padding: 120rpx 60rpx 80rpx;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Logo和标题区域 */
.login-header {
  animation: fadeInUp 0.8s ease-out;
}

.logo-container {
  width: 160rpx;
  height: 160rpx;
  margin: 0 auto;
  border-radius: 30rpx;
  background: var(--primary-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20rpx 40rpx rgba(27, 79, 114, 0.2);
}

.app-logo {
  width: 100rpx;
  height: 100rpx;
}

.app-title {
  background: var(--primary-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 4rpx;
}

/* 间距区域 */
.login-spacer {
  height: 120rpx;
}

/* 功能特色展示 */
.features-section {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.feature-item {
  background: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: 30rpx;
  border: 2rpx solid var(--border-color);
  transition: all 0.3s ease;
}

.feature-item:hover {
  border-color: var(--primary-light);
  box-shadow: 0 10rpx 30rpx rgba(27, 79, 114, 0.1);
  transform: translateY(-4rpx);
}

.feature-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-lighter), var(--primary-light));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: var(--text-inverse);
}

.feature-title {
  color: var(--text-primary);
}

.feature-desc {
  margin-top: 8rpx;
}

/* 登录按钮区域 */
.login-action {
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.login-button {
  height: 96rpx;
  border-radius: var(--border-radius-lg);
  background: var(--primary-gradient);
  border: none;
  color: var(--text-inverse);
  font-size: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20rpx 40rpx rgba(27, 79, 114, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.6s ease;
}

.login-button:active::before {
  left: 100%;
}

.login-button:active {
  transform: scale(0.98);
  box-shadow: 0 10rpx 20rpx rgba(27, 79, 114, 0.3);
}

.login-button[disabled] {
  opacity: 0.7;
  transform: none;
}

.login-tips {
  animation: fadeIn 0.8s ease-out 0.6s both;
}

.tips-text {
  line-height: 1.6;
}

/* 其他登录方式 */
.other-login {
  animation: fadeInUp 0.8s ease-out 0.8s both;
}

.divider {
  margin: 40rpx 0;
}

.divider-line {
  flex: 1;
  height: 2rpx;
  background: var(--border-color);
}

.divider-text {
  background: var(--bg-primary);
}

.visitor-text {
  color: var(--text-secondary);
  text-decoration: underline;
  padding: 20rpx;
  border-radius: var(--border-radius-base);
  transition: all 0.3s ease;
}

.visitor-text:active {
  background: var(--bg-secondary);
  color: var(--primary-color);
}

/* 动画定义 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 响应式适配 */
@media (max-height: 1200rpx) {
  .login-content {
    padding: 80rpx 60rpx 60rpx;
  }
  
  .logo-container {
    width: 120rpx;
    height: 120rpx;
  }
  
  .app-logo {
    width: 80rpx;
    height: 80rpx;
  }
}

/* 加载状态 */
.login-button[loading] {
  pointer-events: none;
}

.login-button[loading]::after {
  content: '';
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(255,255,255,0.3);
  border-top: 4rpx solid var(--text-inverse);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
