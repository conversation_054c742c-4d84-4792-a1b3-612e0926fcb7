const express = require('express');
const router = express.Router();
const queryStockInfoController = require('../controllers/query/queryStockInfoController');
const { validateQueryStockInfoRequest } = require('../validators/query/queryStockInfoValidator');

/**
 * @route POST /api/query/stock-info
 * @description 根据证券代码查询股票详细信息
 * @access Public
 * @body {string} stockCode - 证券代码（6位数字）
 * @returns {Object} 股票详细信息，包含中文字段名
 *
 * @example
 * POST /api/query/stock-info
 * {
 *   "stockCode": "000001"
 * }
 */
router.post('/stock-info', validateQueryStockInfoRequest, queryStockInfoController.queryStockInfo);

module.exports = router;