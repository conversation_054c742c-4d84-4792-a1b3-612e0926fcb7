# 脚本作用：
# 1. 读取沪深上市公司列表.csv文件，获取所有证券代码
# 2. 读取企业历史日级别价格数据_end20250904.csv文件，获取所有证券代码
# 3. 检测上市公司列表中的证券代码是否都在历史价格数据中存在
# 4. 输出检测结果，包括缺失的证券代码（如果有的话）

import pandas as pd

def check_stock_codes():
    try:
        # 读取沪深上市公司列表，证券代码作为字符串读取，避免000001变成1
        company_list = pd.read_csv('沪深上市公司列表.csv', dtype={'证券代码': str})
        print(f"上市公司列表文件读取成功，共有 {len(company_list)} 家公司")
        
        # 读取企业历史价格数据，证券代码作为字符串读取
        price_data = pd.read_csv('企业历史日级别价格数据_end20250904.csv', dtype={'证券代码': str})
        print(f"历史价格数据文件读取成功，共有 {len(price_data)} 条记录")
        
        # 获取上市公司列表中的所有证券代码
        company_codes = set(company_list['证券代码'].unique())
        print(f"上市公司列表中共有 {len(company_codes)} 个不同的证券代码")
        
        # 获取历史价格数据中的所有证券代码
        price_codes = set(price_data['证券代码'].unique())
        print(f"历史价格数据中共有 {len(price_codes)} 个不同的证券代码")
        
        # 检查上市公司列表中的证券代码是否都在历史价格数据中
        missing_codes = company_codes - price_codes
        
        print("\n" + "="*50)
        print("检测结果：")
        print("="*50)
        
        if len(missing_codes) == 0:
            print("✅ 所有上市公司的证券代码都在历史价格数据中存在！")
        else:
            print(f"❌ 发现 {len(missing_codes)} 个证券代码在历史价格数据中缺失：")
            print("\n缺失的证券代码列表：")
            for code in sorted(missing_codes):
                # 找到对应的公司名称
                company_name = company_list[company_list['证券代码'] == code]['证券简称'].iloc[0]
                board = company_list[company_list['证券代码'] == code]['板块'].iloc[0]
                print(f"  {code} - {company_name} ({board})")
        
        # 统计覆盖率
        coverage_rate = (len(company_codes) - len(missing_codes)) / len(company_codes) * 100
        print(f"\n数据覆盖率: {coverage_rate:.2f}% ({len(company_codes) - len(missing_codes)}/{len(company_codes)})")
        
        # 额外检查：历史价格数据中是否有不在上市公司列表中的代码
        extra_codes = price_codes - company_codes
        if len(extra_codes) > 0:
            print(f"\n📊 历史价格数据中有 {len(extra_codes)} 个证券代码不在当前上市公司列表中")
            print("（这可能是已退市或其他原因的公司）")
        
    except FileNotFoundError as e:
        print(f"❌ 文件未找到: {e}")
    except Exception as e:
        print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    check_stock_codes()
