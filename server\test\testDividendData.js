const mongoose = require('mongoose');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });
const DividendData = require('../models/DividendData');

// 连接数据库
const connectDB = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI;
    if (!mongoUri) {
      throw new Error('MONGODB_URI 环境变量未设置');
    }
    
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ 成功连接到MongoDB数据库');
    console.log(`📊 数据库地址: ${mongoUri}`);
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  }
};

// 测试分红数据查询
const testDividendData = async () => {
  try {
    await connectDB();
    
    console.log('=== 测试分红数据查询 ===\n');
    
    // 1. 获取总数据量
    const totalCount = await DividendData.countDocuments();
    console.log(`1. 数据库中总共有 ${totalCount} 条分红记录\n`);
    
    // 2. 查询平安银行(000001)的分红记录
    console.log('2. 查询平安银行(000001)的分红记录:');
    const paRecords = await DividendData.findByStockCode('000001').limit(5);
    if (paRecords.length > 0) {
      paRecords.forEach((record, index) => {
        console.log(`   ${index + 1}. ${record.reportPeriod.getFullYear()}年 - ${record.getFormattedDividendDescription()}`);
      });
    } else {
      console.log('   未找到平安银行的分红记录');
    }
    
    // 3. 查询招商银行(600036)的分红记录
    console.log('\n3. 查询招商银行(600036)的分红记录:');
    const cmbRecords = await DividendData.findByStockCode('600036').limit(5);
    if (cmbRecords.length > 0) {
      cmbRecords.forEach((record, index) => {
        console.log(`   ${index + 1}. ${record.reportPeriod.getFullYear()}年 - ${record.getFormattedDividendDescription()}`);
      });
    } else {
      console.log('   未找到招商银行的分红记录');
    }
    
    // 4. 查询浦发银行(600000)的分红记录
    console.log('\n4. 查询浦发银行(600000)的分红记录:');
    const pudongRecords = await DividendData.findByStockCode('600000').limit(5);
    if (pudongRecords.length > 0) {
      pudongRecords.forEach((record, index) => {
        const summary = record.getDividendSummary();
        console.log(`   ${index + 1}. ${record.reportPeriod.getFullYear()}年 - ${summary.description}, 股息率: ${summary.dividendYield}`);
      });
    } else {
      console.log('   未找到浦发银行的分红记录');
    }
    
    // 5. 查询2023年高股息率股票（前10名）
    console.log('\n5. 查询2023年高股息率股票（前10名）:');
    const topDividendYield = await DividendData.findTopDividendYield(2023, 10);
    if (topDividendYield.length > 0) {
      topDividendYield.forEach((record, index) => {
        console.log(`   ${index + 1}. ${record.stockCode} - 股息率: ${record.getFormattedDividendYield()}, 现金分红: ${record.cashDividendRatio}`);
      });
    } else {
      console.log('   未找到2023年的高股息率数据');
    }
    
    // 6. 查询2023年高送转股票（前10名）
    console.log('\n6. 查询2023年高送转股票（前10名）:');
    const topStockTransfer = await DividendData.findTopStockTransfer(2023, 10);
    if (topStockTransfer.length > 0) {
      topStockTransfer.forEach((record, index) => {
        console.log(`   ${index + 1}. ${record.stockCode} - 送转比例: ${record.totalStockTransferRatio}`);
      });
    } else {
      console.log('   未找到2023年的高送转数据');
    }
    
    // 7. 按年份统计分红记录数量
    console.log('\n7. 按年份统计分红记录数量:');
    const yearlyStats = await DividendData.aggregate([
      {
        $group: {
          _id: { $year: '$reportPeriod' },
          count: { $sum: 1 },
          avgDividendYield: { $avg: '$dividendYield' },
          avgCashDividend: { $avg: '$cashDividendRatio' },
          stockCount: { $addToSet: '$stockCode' }
        }
      },
      {
        $addFields: {
          uniqueStockCount: { $size: '$stockCount' }
        }
      },
      { $sort: { _id: -1 } },
      { $limit: 10 }
    ]);
    
    yearlyStats.forEach(stat => {
      const avgYield = stat.avgDividendYield ? (stat.avgDividendYield * 100).toFixed(2) : '0.00';
      const avgCash = stat.avgCashDividend ? stat.avgCashDividend.toFixed(2) : '0.00';
      console.log(`   ${stat._id}年: ${stat.count}条记录, ${stat.uniqueStockCount}只股票, 平均股息率: ${avgYield}%, 平均现金分红: ${avgCash}`);
    });
    
    // 8. 字段映射测试
    console.log('\n8. 字段映射测试:');
    const fieldMapping = DividendData.getFieldMapping();
    console.log('   中英文字段映射示例:');
    console.log(`   证券代码 -> ${fieldMapping['证券代码']}`);
    console.log(`   现金分红-股息率 -> ${fieldMapping['现金分红-股息率']}`);
    console.log(`   每股收益 -> ${fieldMapping['每股收益']}`);
    
    console.log('\n=== 测试完成 ===');
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  } finally {
    await mongoose.connection.close();
    console.log('数据库连接已关闭');
  }
};

// 如果直接运行此脚本
if (require.main === module) {
  testDividendData()
    .then(() => {
      console.log('测试执行完毕');
      process.exit(0);
    })
    .catch((error) => {
      console.error('测试失败:', error);
      process.exit(1);
    });
}

module.exports = { testDividendData };
