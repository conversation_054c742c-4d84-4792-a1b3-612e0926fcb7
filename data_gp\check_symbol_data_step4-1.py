import pandas as pd
import os

def analyze_and_update_stock_data():
    """
    分析并更新股票数据的脚本
    """
    
    # 读取CSV文件
    try:
        listed_companies = pd.read_csv('沪深上市公司列表.csv')
        daily_stock_data = pd.read_csv('当日股票数据.csv')
        print("成功读取CSV文件")
    except FileNotFoundError as e:
        print(f"文件未找到: {e}")
        return
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return
    
    # 确保证券代码列为字符串类型（避免前导零丢失）
    listed_companies['证券代码'] = listed_companies['证券代码'].astype(str).str.zfill(6)
    daily_stock_data['证券代码'] = daily_stock_data['证券代码'].astype(str).str.zfill(6)
    
    # 获取证券代码集合
    listed_codes = set(listed_companies['证券代码'])
    daily_codes = set(daily_stock_data['证券代码'])
    
    print("="*60)
    print("数据分析报告")
    print("="*60)
    
    # 1. 检查沪深上市公司列表中的代码是否都在当日股票数据中
    print("\n1. 沪深上市公司列表中但不在当日股票数据中的证券代码:")
    missing_in_daily = listed_codes - daily_codes
    if missing_in_daily:
        print(f"   共 {len(missing_in_daily)} 个:")
        for code in sorted(missing_in_daily):
            company_name = listed_companies[listed_companies['证券代码'] == code]['证券简称'].iloc[0]
            print(f"   {code} - {company_name}")
    else:
        print("   无缺失")
    
    # 2. 检查当日股票数据中的代码是否都在沪深上市公司列表中
    print("\n2. 当日股票数据中但不在沪深上市公司列表中的证券代码:")
    missing_in_listed = daily_codes - listed_codes
    if missing_in_listed:
        print(f"   共 {len(missing_in_listed)} 个:")
        for code in sorted(missing_in_listed):
            company_name = daily_stock_data[daily_stock_data['证券代码'] == code]['证券简称'].iloc[0]
            print(f"   {code} - {company_name}")
    else:
        print("   无缺失")
    
    # 3. 检查两个文件中都有的证券代码对应的证券简称是否一致
    print("\n3. 证券简称不一致的记录:")
    common_codes = listed_codes & daily_codes
    name_mismatch = []
    
    for code in common_codes:
        listed_name = listed_companies[listed_companies['证券代码'] == code]['证券简称'].iloc[0]
        daily_name = daily_stock_data[daily_stock_data['证券代码'] == code]['证券简称'].iloc[0]
        
        if listed_name != daily_name:
            name_mismatch.append({
                '证券代码': code,
                '上市公司列表中的简称': listed_name,
                '当日数据中的简称': daily_name
            })
    
    if name_mismatch:
        print(f"   共 {len(name_mismatch)} 个不一致:")
        for item in name_mismatch:
            print(f"   {item['证券代码']}: 上市公司列表='{item['上市公司列表中的简称']}', 当日数据='{item['当日数据中的简称']}'")
    else:
        print("   所有证券简称都一致")
    
    # 4. 更新板块信息并生成清洗后的数据
    print("\n4. 更新板块信息并生成清洗后数据:")
    
    # 创建证券代码到板块的映射字典
    code_to_sector = dict(zip(listed_companies['证券代码'], listed_companies['板块']))
    
    # 备份原始数据并更新所有记录的板块信息
    daily_stock_data_updated = daily_stock_data.copy()
    
    # 统计更新情况
    updated_count = 0
    unchanged_count = 0
    not_found_count = 0
    
    for index, row in daily_stock_data_updated.iterrows():
        code = row['证券代码']
        if code in code_to_sector:
            old_sector = row['板块']
            new_sector = code_to_sector[code]
            
            if old_sector != new_sector:
                daily_stock_data_updated.at[index, '板块'] = new_sector
                updated_count += 1
            else:
                unchanged_count += 1
        else:
            not_found_count += 1
    
    print(f"   板块信息更新统计:")
    print(f"   - 更新了 {updated_count} 条记录的板块信息")
    print(f"   - {unchanged_count} 条记录的板块信息无需更新")
    print(f"   - {not_found_count} 条记录因不在上市公司列表中而无法更新板块信息")
    
    # 保存更新后的完整文件
    output_filename_all = '当日股票数据_已更新板块.csv'
    daily_stock_data_updated.to_csv(output_filename_all, index=False, encoding='utf-8-sig')
    print(f"\n   已将更新后的完整数据保存为: {output_filename_all}")
    
    # 生成清洗后的数据（只包含在沪深上市公司列表中存在的股票）
    print(f"\n   生成清洗后数据:")
    
    # 筛选出在沪深上市公司列表中存在的股票数据
    cleaned_data = daily_stock_data_updated[daily_stock_data_updated['证券代码'].isin(listed_codes)].copy()
    
    # 创建排序映射：根据沪深上市公司列表中的顺序
    listed_companies_reset = listed_companies.reset_index()
    code_to_order = dict(zip(listed_companies_reset['证券代码'], listed_companies_reset.index))
    
    # 为清洗后的数据添加排序列
    cleaned_data['_sort_order'] = cleaned_data['证券代码'].map(code_to_order)
    
    # 按照沪深上市公司列表的顺序排序
    cleaned_data_sorted = cleaned_data.sort_values('_sort_order').drop('_sort_order', axis=1)
    
    # 保存清洗后的数据
    output_filename_cleaned = '当日股票数据_清洗后.csv'
    cleaned_data_sorted.to_csv(output_filename_cleaned, index=False, encoding='utf-8-sig')
    
    print(f"   - 原始当日股票数据总数: {len(daily_stock_data)}")
    print(f"   - 清洗后数据总数: {len(cleaned_data_sorted)}")
    print(f"   - 剔除的数据总数: {len(daily_stock_data) - len(cleaned_data_sorted)}")
    print(f"   - 已将清洗后的数据保存为: {output_filename_cleaned}")
    print(f"   - 清洗后数据已按沪深上市公司列表的证券代码顺序排序")
    
    # 显示清洗后数据的前几行作为预览
    if len(cleaned_data_sorted) > 0:
        print(f"\n   清洗后数据预览（前5行）:")
        print("   " + "-" * 50)
        preview_columns = ['证券代码', '证券简称', '板块']
        # 确保预览列存在
        available_columns = [col for col in preview_columns if col in cleaned_data_sorted.columns]
        if available_columns:
            for i, (_, row) in enumerate(cleaned_data_sorted[available_columns].head().iterrows()):
                row_str = "   "
                for col in available_columns:
                    row_str += f"{col}: {row[col]}  "
                print(row_str)
        
        # 显示排序验证信息
        first_codes = cleaned_data_sorted['证券代码'].head(3).tolist()
        last_codes = cleaned_data_sorted['证券代码'].tail(3).tolist()
        print(f"\n   排序验证:")
        print(f"   - 前3个证券代码: {first_codes}")
        print(f"   - 后3个证券代码: {last_codes}")
    
    # 生成清洗统计报告
    generate_cleaning_report(daily_stock_data, cleaned_data_sorted, missing_in_listed, listed_companies)
    
    # 生成详细报告
    print("\n" + "="*60)
    print("统计摘要")
    print("="*60)
    print(f"沪深上市公司列表总数: {len(listed_companies)}")
    print(f"当日股票数据总数: {len(daily_stock_data)}")
    print(f"两个文件共同包含的证券代码数: {len(common_codes)}")
    print(f"仅在上市公司列表中的代码数: {len(missing_in_daily)}")
    print(f"仅在当日数据中的代码数: {len(missing_in_listed)}")
    print(f"证券简称不一致的数量: {len(name_mismatch)}")
    print(f"板块信息已更新的记录数: {updated_count}")
    print(f"清洗后数据记录数: {len(cleaned_data_sorted)}")
    print(f"数据清洗率: {len(cleaned_data_sorted)/len(daily_stock_data)*100:.2f}%")
    
    # 可选：生成详细的差异报告文件
    if missing_in_daily or missing_in_listed or name_mismatch:
        generate_detailed_report(missing_in_daily, missing_in_listed, name_mismatch, 
                               listed_companies, daily_stock_data)

def generate_cleaning_report(original_data, cleaned_data, removed_codes, listed_companies):
    """
    生成数据清洗报告
    """
    with open('数据清洗报告.txt', 'w', encoding='utf-8') as f:
        f.write("股票数据清洗报告\n")
        f.write("="*60 + "\n\n")
        
        f.write(f"清洗前数据总数: {len(original_data)}\n")
        f.write(f"清洗后数据总数: {len(cleaned_data)}\n")
        f.write(f"剔除数据总数: {len(removed_codes)}\n")
        f.write(f"数据保留率: {len(cleaned_data)/len(original_data)*100:.2f}%\n\n")
        
        f.write("被剔除的证券代码详情:\n")
        f.write("-"*40 + "\n")
        f.write("证券代码\t证券简称\t原板块信息\n")
        
        if removed_codes:
            for code in sorted(removed_codes):
                row = original_data[original_data['证券代码'] == code].iloc[0]
                f.write(f"{code}\t{row['证券简称']}\t{row.get('板块', 'N/A')}\n")
        else:
            f.write("无被剔除的数据\n")
        
        f.write(f"\n清洗后数据排序说明:\n")
        f.write("-"*40 + "\n")
        f.write("数据已按照'沪深上市公司列表.csv'中证券代码的原始顺序进行排序\n")
        
        if len(cleaned_data) > 0:
            f.write(f"排序后首个证券代码: {cleaned_data.iloc[0]['证券代码']}\n")
            f.write(f"排序后末个证券代码: {cleaned_data.iloc[-1]['证券代码']}\n")
    
    print("   数据清洗报告已保存为: 数据清洗报告.txt")

def generate_detailed_report(missing_in_daily, missing_in_listed, name_mismatch, 
                           listed_companies, daily_stock_data):
    """
    生成详细的差异报告文件
    """
    with open('股票数据差异报告.txt', 'w', encoding='utf-8') as f:
        f.write("股票数据差异详细报告\n")
        f.write("="*60 + "\n\n")
        
        f.write("1. 仅在沪深上市公司列表中存在的证券代码:\n")
        f.write("-"*40 + "\n")
        if missing_in_daily:
            for code in sorted(missing_in_daily):
                company_name = listed_companies[listed_companies['证券代码'] == code]['证券简称'].iloc[0]
                sector = listed_companies[listed_companies['证券代码'] == code]['板块'].iloc[0]
                f.write(f"{code}\t{company_name}\t{sector}\n")
        else:
            f.write("无\n")
        
        f.write("\n2. 仅在当日股票数据中存在的证券代码:\n")
        f.write("-"*40 + "\n")
        if missing_in_listed:
            for code in sorted(missing_in_listed):
                company_name = daily_stock_data[daily_stock_data['证券代码'] == code]['证券简称'].iloc[0]
                sector = daily_stock_data[daily_stock_data['证券代码'] == code]['板块'].iloc[0]
                f.write(f"{code}\t{company_name}\t{sector}\n")
        else:
            f.write("无\n")
        
        f.write("\n3. 证券简称不一致的记录:\n")
        f.write("-"*40 + "\n")
        if name_mismatch:
            for item in name_mismatch:
                f.write(f"{item['证券代码']}\t上市公司列表: {item['上市公司列表中的简称']}\t当日数据: {item['当日数据中的简称']}\n")
        else:
            f.write("无\n")
    
    print("详细差异报告已保存为: 股票数据差异报告.txt")

if __name__ == "__main__":
    analyze_and_update_stock_data()
