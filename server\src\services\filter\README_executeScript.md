# 脚本执行服务

## 功能概述

这个服务可以安全地执行AI生成的JavaScript代码字符串，并返回执行结果。主要用于执行股票筛选脚本。

## 核心特性

- ✅ 安全的沙箱执行环境
- ✅ 完整的安全性检查
- ✅ 支持异步代码执行
- ✅ 30秒执行超时保护
- ✅ 提供完整的数据模型访问

## 使用方法

### 基本用法

```javascript
const { executeScript } = require('./executeScriptService');

// 执行脚本代码
const scriptCode = `const executeLogic = async () => {
  try {
    const result = await DailyStockData.find({
      peRatioTTM: { $gte: 10, $lte: 20 }
    }).limit(10);
    return result;
  } catch (error) {
    throw new Error(\`查询失败: \${error.message}\`);
  }
};`;

const result = await executeScript(scriptCode);
console.log(result);
```

### 返回结果结构

```javascript
{
  "success": true,
  "result": [...], // 脚本执行的实际结果
  "executedAt": "2024-12-20T10:30:00.000Z",
  "resultCount": 10 // 如果结果是数组，返回数组长度
}
```

## 安全特性

### 禁止的操作

- ❌ `require()` - 禁止引入其他模块
- ❌ `import` - 禁止ES6导入
- ❌ `process.*` - 禁止访问进程对象
- ❌ `global.*` - 禁止访问全局对象
- ❌ `eval()` - 禁止eval函数
- ❌ `Function()` - 禁止Function构造器
- ❌ 文件系统操作
- ❌ 子进程操作
- ❌ 无限循环

### 允许的操作

- ✅ 访问数据模型：`DailyStockData`, `FinancialData`, `StockBasicInfo`
- ✅ 使用`console.log`等调试功能
- ✅ 使用`Date`对象
- ✅ 使用`Promise`和`async/await`
- ✅ 使用`setTimeout`（最大30秒）

## 脚本要求

### 必须包含的函数

脚本必须定义一个名为`executeLogic`的异步函数：

```javascript
const executeLogic = async () => {
  // 您的查询逻辑
  const result = await SomeModel.find({...});
  return result;
};
```

### 示例脚本

#### 1. 简单查询

```javascript
const executeLogic = async () => {
  try {
    const result = await DailyStockData.find({
      peRatioTTM: { $gte: 10, $lte: 20 },
      totalMarketCap: { $gt: 100 }
    }).limit(10);
    return result;
  } catch (error) {
    throw new Error(`查询失败: ${error.message}`);
  }
};
```

#### 2. 聚合查询

```javascript
const executeLogic = async () => {
  try {
    const result = await FinancialData.aggregate([
      {
        $match: {
          fiscalYear: 2023,
          "data.keyMetrics.netProfit": { $gt: 0 }
        }
      },
      {
        $lookup: {
          from: "stock_basic_info",
          localField: "stockCode",
          foreignField: "stockCode",
          as: "stockInfo"
        }
      }
    ]);
    return result;
  } catch (error) {
    throw new Error(`查询失败: ${error.message}`);
  }
};
```

## 限制说明

1. **执行时间限制**：最大30秒执行时间
2. **代码长度限制**：最大10,000字符
3. **内存限制**：受Node.js VM模块限制
4. **网络访问**：禁止网络请求
5. **文件访问**：禁止文件系统操作

## 错误处理

服务会捕获并处理以下类型的错误：

- 语法错误
- 运行时错误
- 超时错误
- 安全违规错误
- 数据库连接错误

## 测试方法

运行测试文件：

```javascript
const { testExecuteScript } = require('./testExecuteScript');
await testExecuteScript();
```

## 注意事项

1. **安全性**：虽然有多重安全检查，但仍建议在隔离环境中使用
2. **性能**：复杂查询可能需要较长时间，注意超时设置
3. **数据库连接**：确保MongoDB连接正常
4. **错误处理**：脚本内部应包含适当的错误处理
5. **资源管理**：避免创建过多的数据库连接或大量数据查询

## 技术实现

- 使用Node.js `vm`模块创建安全沙箱
- 通过正则表达式进行安全性检查
- 使用Promise.race实现超时控制
- 提供受限的执行上下文
