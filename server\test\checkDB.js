const mongoose = require('mongoose');
require('dotenv').config();

async function checkDB() {
  try {
    console.log('连接数据库:', process.env.MONGODB_URI);
    
    // 设置连接超时
    await mongoose.connect(process.env.MONGODB_URI, {
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      maxPoolSize: 10,
      minPoolSize: 5,
      maxIdleTimeMS: 30000,
      waitQueueTimeoutMS: 5000
    });
    
    console.log('数据库连接成功');
    
    // 检查集合
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('可用集合:', collections.map(c => c.name));
    
    // 检查daily_stock_data集合的数据量
    const DailyStockData = require('../src/models/DailyStockData');
    const count = await DailyStockData.countDocuments();
    console.log('daily_stock_data 集合数据量:', count);
    
    if (count > 0) {
      // 获取一个样本数据
      const sample = await DailyStockData.findOne();
      console.log('样本数据:', JSON.stringify(sample, null, 2));
    }
    
    await mongoose.disconnect();
    console.log('检查完成');
  } catch (error) {
    console.error('数据库检查失败:', error.message);
    process.exit(1);
  }
}

checkDB();
