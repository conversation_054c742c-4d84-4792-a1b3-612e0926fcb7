// 筛选结果页面逻辑
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 股票列表数据（全部数据）
    stockList: [],
    
    // 当前显示的股票数据（分页后的数据）
    displayStockList: [],
    
    // 表格列名（动态获取）
    tableColumns: [],
    
    // 筛选条件（用于回显）
    filterConditions: {},
    
    // 筛选条件列表（用于显示）
    conditionsList: [],
    
    // 目标日期
    targetDate: '',
    
    // 是否显示筛选条件
    showConditions: false,
    
    // 加载状态
    loading: false,
    
    // 分页相关
    currentPage: 1,        // 当前页码
    pageSize: 10,          // 每页显示数量
    totalPages: 0,         // 总页数
    hasMore: true          // 是否还有更多数据
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    
    // 解析传递的参数
    if (options.results) {
      try {
        const stockList = JSON.parse(decodeURIComponent(options.results));
        
        // 设置股票列表数据
        this.setData({
          stockList: stockList
        });
        
        // 初始化分页数据
        this.initPagination(stockList);
        
        // 动态获取表格列名
        this.generateTableColumns(stockList);
        
      } catch (error) {
        console.error('解析股票列表数据失败:', error);
        wx.showToast({
          title: '数据解析失败',
          icon: 'none',
          duration: 2000
        });
      }
    }
    
    // 声明变量来存储解析的数据
    let conditions = null;
    let targetDate = '';
    
    // 解析筛选条件
    if (options.conditions) {
      try {
        conditions = JSON.parse(decodeURIComponent(options.conditions));
      } catch (error) {
        console.error('解析筛选条件失败:', error);
      }
    }
    
    // 解析目标日期
    if (options.targetDate) {
      try {
        targetDate = decodeURIComponent(options.targetDate);
      } catch (error) {
        console.error('解析目标日期失败:', error);
      }
    }
    
    // 设置页面数据
    this.setData({
      filterConditions: conditions || {},
      targetDate: targetDate
    });
    
    // 生成筛选条件显示列表
    if (conditions) {
      this.generateConditionsList(conditions);
    }
  },

  /**
   * 初始化分页数据
   */
  initPagination(stockList) {
    const totalPages = Math.ceil(stockList.length / this.data.pageSize);
    const displayStockList = stockList.slice(0, this.data.pageSize);
    
    this.setData({
      totalPages: totalPages,
      displayStockList: displayStockList,
      currentPage: 1,
      hasMore: stockList.length > this.data.pageSize
    });
  },

  /**
   * 加载更多数据
   */
  loadMoreData() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }
    
    this.setData({
      loading: true
    });
    
    // 模拟网络延迟
    setTimeout(() => {
      const nextPage = this.data.currentPage + 1;
      const startIndex = (nextPage - 1) * this.data.pageSize;
      const endIndex = startIndex + this.data.pageSize;
      const newData = this.data.stockList.slice(startIndex, endIndex);
      
      if (newData.length > 0) {
        const updatedDisplayList = [...this.data.displayStockList, ...newData];
        
        this.setData({
          displayStockList: updatedDisplayList,
          currentPage: nextPage,
          hasMore: endIndex < this.data.stockList.length,
          loading: false
        });
      } else {
        this.setData({
          hasMore: false,
          loading: false
        });
      }
    }, 300);
  },

  /**
   * 跳转到指定页面
   */
  goToPage(targetPage) {
    if (targetPage < 1 || targetPage > this.data.totalPages) {
      return;
    }
    
    this.setData({
      loading: true
    });
    
    setTimeout(() => {
      const startIndex = (targetPage - 1) * this.data.pageSize;
      const endIndex = startIndex + this.data.pageSize;
      const pageData = this.data.stockList.slice(startIndex, endIndex);
      
      this.setData({
        displayStockList: pageData,
        currentPage: targetPage,
        hasMore: endIndex < this.data.stockList.length,
        loading: false
      });
    }, 200);
  },

  /**
   * 上一页
   */
  prevPage() {
    if (this.data.currentPage > 1) {
      this.goToPage(this.data.currentPage - 1);
    }
  },

  /**
   * 下一页
   */
  nextPage() {
    if (this.data.currentPage < this.data.totalPages) {
      this.goToPage(this.data.currentPage + 1);
    }
  },

  /**
   * 动态生成表格列名
   */
  generateTableColumns(stockList) {
    if (!stockList || stockList.length === 0) {
      this.setData({
        tableColumns: []
      });
      return;
    }
    
    // 获取第一条数据的所有字段作为列名
    const firstStock = stockList[0];
    const columns = Object.keys(firstStock);
    
    this.setData({
      tableColumns: columns
    });
  },

  /**
   * 生成筛选条件显示列表
   */
  generateConditionsList(conditions) {
    const conditionsList = [];
    
    // 引入筛选配置
    const filterConfig = require('../../../config/filterCondition');
    
    // 遍历各个条件组
    for (let groupKey in conditions) {
      if (groupKey === 'targetDate') continue; // 跳过日期字段
      
      const groupConditions = conditions[groupKey];
      const groupConfig = filterConfig[groupKey];
      
      if (!groupConditions || !groupConfig) continue;
      
      // 获取组标签
      const groupInfo = filterConfig.conditionGroups.find(g => g.key === groupKey);
      const groupLabel = groupInfo ? groupInfo.label : groupKey;
      
      // 遍历组内条件
      for (let fieldKey in groupConditions) {
        const fieldValue = groupConditions[fieldKey];
        const fieldConfig = groupConfig[fieldKey];
        
        if (!fieldConfig) continue;
        
        const label = `${groupLabel} - ${fieldConfig.label}`;
        const value = this.formatConditionValue(fieldValue, fieldConfig);
        
        conditionsList.push({
          group: groupKey,
          field: fieldKey,
          label: label,
          value: value
        });
      }
    }
    
    // 添加目标日期
    if (this.data.targetDate) {
      conditionsList.push({
        group: 'common',
        field: 'targetDate',
        label: '筛选日期',
        value: this.data.targetDate
      });
    }
    
    this.setData({
      conditionsList: conditionsList
    });
  },

  /**
   * 格式化筛选条件值显示
   */
  formatConditionValue(value, config) {
    if (!value) return '';
    
    switch (config.type) {
      case 'range':
        const { min, max } = value;
        if (min !== null && max !== null) {
          return `${min} - ${max}`;
        } else if (min !== null) {
          return `≥ ${min}`;
        } else if (max !== null) {
          return `≤ ${max}`;
        }
        break;
        
      case 'multiSelect':
        if (Array.isArray(value)) {
          return value.join(', ');
        }
        break;
        
      case 'boolean':
        return value ? '是' : '否';
        
      default:
        return String(value);
    }
    
    return '';
  },

  /**
   * 切换筛选条件显示状态
   */
  toggleConditionsShow() {
    this.setData({
      showConditions: !this.data.showConditions
    });
  },

  /**
   * 返回筛选页面
   */
  goBackToFilter() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 保存结果（预留功能）
   */
  saveResults() {
    wx.showToast({
      title: '保存功能开发中',
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 点击证券代码跳转到股票详情页面
   */
  onStockCodeTap(e) {
    const stockCode = e.currentTarget.dataset.stockCode;
    if (!stockCode) {
      wx.showToast({
        title: '证券代码无效',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    wx.navigateTo({
      url: `/pages/query/stockInfo/stockInfo?stockCode=${stockCode}`
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时的逻辑
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 页面隐藏时的逻辑
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 页面卸载时的逻辑
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 下拉刷新 - 暂时禁用
    wx.showToast({
      title: '功能开发中',
      icon: 'none',
      duration: 1500
    });
    
    // 完成下拉刷新
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 已禁用自动加载更多功能，用户需要手动点击分页按钮
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '言策AI量化 - 股票筛选结果',
      path: '/pages/index/index',
      imageUrl: ''
    };
  },

  /**
   * 显示页码选择器
   */
  showPageSelector() {
    const pages = [];
    for (let i = 1; i <= this.data.totalPages; i++) {
      pages.push(i.toString());
    }
    
    wx.showActionSheet({
      itemList: pages.map(page => `第${page}页`),
      success: (res) => {
        if (!res.cancel) {
          const targetPage = parseInt(pages[res.tapIndex]);
          this.goToPage(targetPage);
        }
      }
    });
  },
});
