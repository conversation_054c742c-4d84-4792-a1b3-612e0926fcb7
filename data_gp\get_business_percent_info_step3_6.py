#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
沪深上市公司主营业务构成数据获取脚本

功能描述:
1. 读取"沪深上市公司列表.csv"文件中的股票信息
2. 根据板块信息为证券代码添加前缀(沪市主板/科创板加SH，深市主板/创业板加SZ)
3. 调用akshare API获取每只股票的主营业务构成数据
4. 将数据按批次(每500只股票)保存到"企业主营业务构成"文件夹中
5. 支持断点续传，避免重复获取已处理的数据
6. 对失败的请求进行重试机制

作者: [您的姓名]
创建时间: [当前日期]
"""

import pandas as pd
import akshare as ak
import os
import time
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stock_data_fetch.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class StockDataFetcher:
    """股票主营业务构成数据获取器"""
    
    def __init__(self, csv_file_path="沪深上市公司列表.csv", output_dir="企业主营业务构成", batch_size=500):
        self.csv_file_path = csv_file_path
        self.output_dir = output_dir
        self.batch_size = batch_size
        self.failed_stocks = []  # 存储失败的股票代码
        self.processed_stocks = set()  # 存储已处理的股票代码
        
        # 创建输出目录
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            logger.info(f"创建输出目录: {self.output_dir}")
    
    def load_stock_list(self):
        """加载股票列表"""
        try:
            # 使用dtype参数确保证券代码作为字符串读取，避免000001被读成1
            df = pd.read_csv(self.csv_file_path, dtype={'证券代码': str})
            logger.info(f"成功加载股票列表，共{len(df)}只股票")
            return df
        except Exception as e:
            logger.error(f"加载股票列表失败: {e}")
            return None
    
    def get_symbol_with_prefix(self, sector, code):
        """根据板块为证券代码添加前缀"""
        if sector in ["沪市主板", "科创板"]:
            return f"SH{code}"
        elif sector in ["深市主板", "创业板"]:
            return f"SZ{code}"
        else:
            logger.warning(f"未知板块类型: {sector}, 股票代码: {code}")
            return f"SH{code}"  # 默认使用SH前缀
    
    def check_processed_stocks(self):
        """检查已经处理过的股票代码"""
        processed = set()
        if os.path.exists(self.output_dir):
            for filename in os.listdir(self.output_dir):
                if filename.endswith('.csv'):
                    try:
                        filepath = os.path.join(self.output_dir, filename)
                        df = pd.read_csv(filepath, dtype={'原始证券代码': str})
                        if '原始证券代码' in df.columns:
                            processed.update(df['原始证券代码'].unique())
                    except Exception as e:
                        logger.warning(f"读取已处理文件{filename}时出错: {e}")
        
        self.processed_stocks = processed
        logger.info(f"发现{len(processed)}只已处理的股票")
        return processed
    
    def fetch_stock_data(self, symbol, original_code, max_retries=3):
        """获取单只股票的主营业务构成数据"""
        for attempt in range(max_retries):
            try:
                logger.info(f"正在获取股票{original_code}({symbol})的数据... (尝试{attempt + 1}/{max_retries})")
                
                # 调用akshare API
                stock_data = ak.stock_zygc_em(symbol=symbol)
                
                if stock_data is not None and not stock_data.empty:
                    # 添加原始证券代码列
                    stock_data['原始证券代码'] = original_code
                    logger.info(f"成功获取股票{original_code}的数据，共{len(stock_data)}条记录")
                    return stock_data
                else:
                    logger.warning(f"股票{original_code}返回空数据")
                    
            except Exception as e:
                logger.error(f"获取股票{original_code}数据失败 (尝试{attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # 重试前等待2秒
        
        return None
    
    def save_batch_data(self, batch_data, batch_num):
        """保存批次数据"""
        if not batch_data:
            return
        
        # 合并所有数据
        combined_df = pd.concat(batch_data, ignore_index=True)
        
        # 生成文件名
        filename = f"企业主营业务构成_批次{batch_num:03d}_{len(batch_data)}只股票.csv"
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            combined_df.to_csv(filepath, index=False, encoding='utf-8-sig')
            logger.info(f"成功保存批次{batch_num}数据到: {filename}")
        except Exception as e:
            logger.error(f"保存批次{batch_num}数据失败: {e}")
    
    def process_stocks(self):
        """处理所有股票"""
        # 加载股票列表
        stock_df = self.load_stock_list()
        if stock_df is None:
            return
        
        # 检查已处理的股票
        self.check_processed_stocks()
        
        # 过滤出未处理的股票
        unprocessed_stocks = stock_df[~stock_df['证券代码'].isin(self.processed_stocks)]
        logger.info(f"需要处理{len(unprocessed_stocks)}只股票")
        
        batch_data = []
        batch_num = len([f for f in os.listdir(self.output_dir) if f.endswith('.csv')]) + 1
        
        for idx, row in unprocessed_stocks.iterrows():
            sector = row['板块']
            original_code = str(row['证券代码']).zfill(6)  # 确保是6位数字
            symbol = self.get_symbol_with_prefix(sector, original_code)
            
            # 获取股票数据
            stock_data = self.fetch_stock_data(symbol, original_code)
            
            if stock_data is not None:
                batch_data.append(stock_data)
            else:
                self.failed_stocks.append((original_code, symbol))
            
            # 每处理完一只股票后暂停一下，避免请求过于频繁
            time.sleep(1)
            
            # 达到批次大小或处理完所有股票时保存
            if len(batch_data) >= self.batch_size or idx == len(unprocessed_stocks) - 1:
                if batch_data:
                    self.save_batch_data(batch_data, batch_num)
                    batch_data = []
                    batch_num += 1
        
        # 处理失败的股票
        self.retry_failed_stocks()
    
    def retry_failed_stocks(self):
        """重试失败的股票"""
        if not self.failed_stocks:
            logger.info("没有需要重试的股票")
            return
        
        logger.info(f"开始重试{len(self.failed_stocks)}只失败的股票")
        
        retry_count = 0
        max_retries = 3
        
        while self.failed_stocks and retry_count < max_retries:
            retry_count += 1
            logger.info(f"第{retry_count}次重试，共{len(self.failed_stocks)}只股票")
            
            if retry_count > 1:
                logger.info("等待1分钟后开始重试...")
                time.sleep(60)  # 等待1分钟
            
            failed_this_round = []
            batch_data = []
            batch_num = len([f for f in os.listdir(self.output_dir) if f.endswith('.csv')]) + 1
            
            for original_code, symbol in self.failed_stocks:
                stock_data = self.fetch_stock_data(symbol, original_code)
                
                if stock_data is not None:
                    batch_data.append(stock_data)
                else:
                    failed_this_round.append((original_code, symbol))
                
                time.sleep(1)  # 避免请求过于频繁
                
                # 达到批次大小时保存
                if len(batch_data) >= self.batch_size:
                    self.save_batch_data(batch_data, batch_num)
                    batch_data = []
                    batch_num += 1
            
            # 保存剩余数据
            if batch_data:
                self.save_batch_data(batch_data, batch_num)
            
            self.failed_stocks = failed_this_round
            logger.info(f"第{retry_count}次重试完成，还有{len(self.failed_stocks)}只股票失败")
        
        if self.failed_stocks:
            logger.warning(f"最终仍有{len(self.failed_stocks)}只股票获取失败:")
            for original_code, symbol in self.failed_stocks:
                logger.warning(f"  - {original_code} ({symbol})")
    
    def run(self):
        """运行主程序"""
        start_time = datetime.now()
        logger.info("="*50)
        logger.info("开始获取股票主营业务构成数据")
        logger.info(f"开始时间: {start_time}")
        logger.info("="*50)
        
        try:
            self.process_stocks()
        except KeyboardInterrupt:
            logger.info("程序被用户中断")
        except Exception as e:
            logger.error(f"程序执行出错: {e}")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info("="*50)
        logger.info("数据获取完成")
        logger.info(f"结束时间: {end_time}")
        logger.info(f"总耗时: {duration}")
        logger.info(f"输出目录: {self.output_dir}")
        logger.info("="*50)


def main():
    """主函数"""
    fetcher = StockDataFetcher()
    fetcher.run()


if __name__ == "__main__":
    main()
