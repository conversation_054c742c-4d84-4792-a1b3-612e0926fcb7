const jwt = require('jsonwebtoken');

/**
 * 生成JWT令牌
 * @param {string} userId 用户ID
 * @returns {object} 包含访问令牌和刷新令牌的对象
 */
exports.generateToken = (userId) => {
  try {
    // 获取环境变量并处理空格问题
    const {
      JWT_SECRET = 'ae6226acc0a48fd4bcad83a2a4ab0779bf8ede49232d20ecd0d47834e129078cddd1febdc09da96dbe13e1e0e626160409df6e2d13983594684ae8c9da4aff2f',
      JWT_EXPIRES_IN = '1d',
      JWT_REFRESH_SECRET = '771f87e7205b91ae0d22c1e5d9be7b2c161acb0d6926abab04bce73059b94cd99718d82df27071b1c030f21c7813f219a8709af4b600ae346645bb708685274a',
      JWT_REFRESH_EXPIRES_IN = '7d'
    } = process.env;

    // 关键修复：去除环境变量中的空格
    const accessTokenExpiry = JWT_EXPIRES_IN?.trim() || '1d';
    const refreshTokenExpiry = JWT_REFRESH_EXPIRES_IN?.trim() || '7d';
    
    console.log('JWT配置:', {
      accessTokenExpiry,
      refreshTokenExpiry,
      hasSecret: !!JWT_SECRET,
      hasRefreshSecret: !!JWT_REFRESH_SECRET
    });

    // 验证userId
    if (!userId) {
      throw new Error('用户ID不能为空');
    }

    // 生成访问令牌
    const accessToken = jwt.sign(
      { id: userId },
      JWT_SECRET,
      { expiresIn: accessTokenExpiry }
    );

    // 生成刷新令牌
    const refreshToken = jwt.sign(
      { id: userId },
      JWT_REFRESH_SECRET || JWT_SECRET,
      { expiresIn: refreshTokenExpiry }
    );

    // 计算过期时间（秒）
    const expiresIn = parseExpiry(accessTokenExpiry);

    // 返回令牌对象
    return {
      accessToken,
      refreshToken,
      expiresIn
    };
  } catch (error) {
    console.error('JWT生成失败:', error.message);
    throw {
      statusCode: 500,
      message: 'JWT令牌生成失败',
      details: error.message
    };
  }
};

/**
 * 解析过期时间字符串为秒数
 * @param {string} expiry 过期时间字符串 (如 "1d", "24h", "3600")
 * @returns {number} 秒数
 */
function parseExpiry(expiry) {
  if (typeof expiry === 'number') return expiry;
  
  const match = expiry.match(/^(\d+)([dhms]?)$/);
  if (!match) return 86400; // 默认1天
  
  const [, num, unit] = match;
  const value = parseInt(num);
  
  switch (unit) {
    case 'd': return value * 24 * 60 * 60;
    case 'h': return value * 60 * 60;
    case 'm': return value * 60;
    case 's': return value;
    default: return value; // 纯数字认为是秒
  }
}

/**
 * 验证JWT令牌
 * @param {String} token 令牌字符串
 * @returns {Object} 解码后的负载数据
 */
exports.verifyToken = (token) => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET);
  } catch (error) {
    console.error('JWT验证失败:', error.message);
    throw {
      statusCode: 401,
      message: 'JWT令牌无效',
      details: error.message
    };
  }
}; 