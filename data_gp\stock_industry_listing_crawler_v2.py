#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
股票信息批量获取脚本（支持分批写入和断点续传）

脚本作用：
1. 读取包含股票基本信息的CSV文件（包含板块、证券代码、证券简称等字段）
2. 通过akshare库的stock_individual_info_em接口批量获取每只股票的详细信息
3. 从API返回的数据中提取"行业"和"上市时间"两个关键字段
4. 每处理指定数量的股票就保存一次，避免长时间等待
5. 支持断点续传，可以从上次中断的地方继续处理

新增功能特点：
- 分批写入：每处理BATCH_SIZE个股票就保存一次结果
- 断点续传：程序中断后可以从上次停止的地方继续
- 实时保存：不用等到全部完成才能看到结果
- 进度文件：自动记录处理进度，支持恢复

输入文件格式要求：
CSV文件需包含以下列：板块,证券代码,证券简称

输出文件格式：
在原有字段基础上增加：行业,上市时间

使用前请确保已安装依赖：pip install akshare pandas

作者：AI助手
版本：2.0（支持分批写入和断点续传）
创建时间：2024
"""

import pandas as pd
import akshare as ak
import time
import logging
import json
import os
from datetime import datetime

# 配置参数
API_REQUEST_INTERVAL = 2  # 每次API调用间隔2秒
BATCH_SIZE = 50  # 每处理多少个股票保存一次（可根据需要调整）
PROGRESS_FILE = "processing_progress.json"  # 进度记录文件

# 设置日志格式
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stock_processing.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def save_progress(current_index, total_count, success_count, fail_count, start_time):
    """
    保存处理进度到文件
    
    参数:
        current_index (int): 当前处理的索引
        total_count (int): 总股票数量
        success_count (int): 成功处理数量
        fail_count (int): 失败处理数量
        start_time (datetime): 开始处理时间
    """
    progress_data = {
        'current_index': current_index,
        'total_count': total_count,
        'success_count': success_count,
        'fail_count': fail_count,
        'start_time': start_time.isoformat(),
        'last_update': datetime.now().isoformat()
    }
    
    try:
        with open(PROGRESS_FILE, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, ensure_ascii=False, indent=2)
        logging.debug(f"进度已保存: {current_index}/{total_count}")
    except Exception as e:
        logging.error(f"保存进度失败: {str(e)}")

def load_progress():
    """
    从文件加载处理进度
    
    返回:
        dict: 进度信息，如果文件不存在返回None
    """
    if not os.path.exists(PROGRESS_FILE):
        return None
    
    try:
        with open(PROGRESS_FILE, 'r', encoding='utf-8') as f:
            progress_data = json.load(f)
        
        # 转换时间字符串回datetime对象
        progress_data['start_time'] = datetime.fromisoformat(progress_data['start_time'])
        progress_data['last_update'] = datetime.fromisoformat(progress_data['last_update'])
        
        logging.info(f"找到进度文件，上次处理到第 {progress_data['current_index']} 个股票")
        return progress_data
    except Exception as e:
        logging.error(f"加载进度文件失败: {str(e)}")
        return None

def get_stock_info(stock_code):
    """
    获取股票的行业和上市时间信息
    
    参数:
        stock_code (str): 股票代码
    
    返回:
        tuple: (行业, 上市时间) 如果获取失败则返回 (None, None)
    """
    try:
        # 确保股票代码格式正确（6位数字，不足6位前面补0）
        stock_code = str(stock_code).zfill(6)
        
        logging.info(f"正在调用API获取股票 {stock_code} 的信息...")
        
        # 调用API获取股票信息
        stock_info = ak.stock_individual_info_em(symbol=stock_code)
        
        # 初始化返回值
        industry = None
        listing_date = None
        
        # 遍历返回的数据，查找行业和上市时间
        for index, row in stock_info.iterrows():
            if row['item'] == '行业':
                industry = row['value']
                logging.debug(f"股票 {stock_code} 行业: {industry}")
            elif row['item'] == '上市时间':
                listing_date = row['value']
                logging.debug(f"股票 {stock_code} 上市时间: {listing_date}")
        
        logging.info(f"股票 {stock_code} 信息获取成功")
        return industry, listing_date
        
    except Exception as e:
        logging.error(f"获取股票 {stock_code} 信息失败: {str(e)}")
        return None, None

def save_batch_results(df, output_file, batch_num, current_index, total_count):
    """
    保存批次处理结果
    
    参数:
        df (pandas.DataFrame): 要保存的数据框
        output_file (str): 输出文件路径
        batch_num (int): 批次号
        current_index (int): 当前处理索引
        total_count (int): 总数量
    """
    try:
        # 保存完整结果
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        # 同时保存带时间戳的备份文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"backup_{timestamp}_batch{batch_num}_{output_file}"
        df.to_csv(backup_file, index=False, encoding='utf-8-sig')
        
        logging.info(f"批次 {batch_num} 结果已保存到 {output_file}")
        logging.info(f"备份文件: {backup_file}")
        logging.info(f"当前进度: {current_index}/{total_count} ({current_index/total_count*100:.1f}%)")
        
    except Exception as e:
        logging.error(f"保存批次结果失败: {str(e)}")

def process_stock_csv(input_file, output_file, resume=True):
    """
    处理CSV文件，获取股票信息并分批保存到新文件
    
    参数:
        input_file (str): 输入CSV文件路径
        output_file (str): 输出CSV文件路径
        resume (bool): 是否尝试从上次中断处继续
    
    返回:
        pandas.DataFrame: 处理后的数据框，如果失败返回None
    """
    try:
        # 读取原始CSV文件，确保证券代码作为字符串读取
        logging.info(f"正在读取输入文件: {input_file}")
        df = pd.read_csv(input_file, dtype={'证券代码': str})
        
        total_stocks = len(df)
        logging.info(f"共需要处理 {total_stocks} 只股票")
        
        # 初始化新列（如果不存在）
        if '行业' not in df.columns:
            df['行业'] = ''
        if '上市时间' not in df.columns:
            df['上市时间'] = ''
        
        # 尝试加载进度（断点续传）
        start_index = 0
        success_count = 0
        fail_count = 0
        start_time = datetime.now()
        batch_num = 1
        
        if resume:
            progress = load_progress()
            if progress:
                # 询问用户是否继续上次的进度
                print(f"\n发现上次未完成的处理任务:")
                print(f"上次处理到: 第 {progress['current_index']} 个股票")
                print(f"总进度: {progress['current_index']}/{progress['total_count']} ({progress['current_index']/progress['total_count']*100:.1f}%)")
                print(f"成功: {progress['success_count']}, 失败: {progress['fail_count']}")
                
                user_input = input("是否从上次中断处继续？(y/n): ").lower().strip()
                if user_input == 'y':
                    start_index = progress['current_index']
                    success_count = progress['success_count']
                    fail_count = progress['fail_count']
                    start_time = progress['start_time']
                    batch_num = (start_index // BATCH_SIZE) + 1
                    
                    # 尝试加载已有的输出文件
                    if os.path.exists(output_file):
                        try:
                            existing_df = pd.read_csv(output_file, dtype={'证券代码': str})
                            # 合并已处理的数据
                            for i in range(min(start_index, len(existing_df))):
                                if i < len(df):
                                    df.iloc[i] = existing_df.iloc[i]
                            logging.info(f"已加载 {min(start_index, len(existing_df))} 条已处理的数据")
                        except Exception as e:
                            logging.warning(f"加载已有输出文件失败，将重新开始: {str(e)}")
                    
                    logging.info(f"从第 {start_index + 1} 个股票继续处理")
                else:
                    logging.info("用户选择重新开始处理")
        
        # 遍历每一行数据（从指定位置开始）
        for index in range(start_index, total_stocks):
            row = df.iloc[index]
            stock_code = str(row['证券代码']).zfill(6)  # 确保股票代码格式正确
            stock_name = row['证券简称']
            
            logging.info(f"正在处理第 {index + 1}/{total_stocks} 只股票: {stock_code} - {stock_name}")
            
            # 获取股票信息
            industry, listing_date = get_stock_info(stock_code)
            
            # 更新DataFrame
            if industry is not None and listing_date is not None:
                df.at[index, '行业'] = industry
                df.at[index, '上市时间'] = listing_date
                success_count += 1
                logging.info(f"股票 {stock_code} 处理成功")
            else:
                df.at[index, '行业'] = '未获取到'
                df.at[index, '上市时间'] = '未获取到'
                fail_count += 1
                logging.warning(f"股票 {stock_code} 处理失败")
            
            # 保存进度
            save_progress(index + 1, total_stocks, success_count, fail_count, start_time)
            
            # 分批保存结果
            if (index + 1) % BATCH_SIZE == 0 or index == total_stocks - 1:
                save_batch_results(df, output_file, batch_num, index + 1, total_stocks)
                batch_num += 1
                
                # 显示批次完成信息
                elapsed_time = datetime.now() - start_time
                avg_time_per_stock = elapsed_time.total_seconds() / (index + 1 - start_index)
                remaining_stocks = total_stocks - (index + 1)
                estimated_remaining_time = remaining_stocks * avg_time_per_stock
                
                print(f"\n{'='*50}")
                print(f"批次 {batch_num-1} 完成!")
                print(f"已处理: {index + 1}/{total_stocks} 只股票")
                print(f"本次成功: {success_count}, 失败: {fail_count}")
                print(f"成功率: {success_count/(index + 1 - start_index)*100:.1f}%")
                print(f"预计剩余时间: {estimated_remaining_time/60:.1f} 分钟")
                print(f"{'='*50}\n")
            
            # API请求间隔，避免被封禁
            if index < total_stocks - 1:  # 最后一个不需要等待
                logging.info(f"等待 {API_REQUEST_INTERVAL} 秒后继续下一个请求...")
                time.sleep(API_REQUEST_INTERVAL)
        
        # 处理完成，删除进度文件
        if os.path.exists(PROGRESS_FILE):
            os.remove(PROGRESS_FILE)
            logging.info("处理完成，已删除进度文件")
        
        # 最终统计
        end_time = datetime.now()
        total_time = end_time - start_time
        
        logging.info("="*50)
        logging.info("处理完成统计:")
        logging.info(f"总处理股票数: {total_stocks}")
        logging.info(f"成功获取信息: {success_count}")
        logging.info(f"获取失败: {fail_count}")
        logging.info(f"成功率: {success_count/total_stocks*100:.1f}%")
        logging.info(f"总耗时: {total_time}")
        logging.info(f"结果已保存到: {output_file}")
        logging.info("="*50)
        
        return df
        
    except KeyboardInterrupt:
        logging.info("用户中断了处理过程")
        logging.info(f"进度已保存，下次运行时可以选择继续处理")
        return None
    except Exception as e:
        logging.error(f"处理文件时发生错误: {str(e)}")
        return None

def main():
    """
    主函数
    """
    print("股票信息批量获取脚本 v2.0")
    print("支持分批写入和断点续传功能")
    print("="*50)
    
    # 获取输入文件路径
    while True:
        input_file = input("请输入包含股票信息的CSV文件路径: ").strip().strip('"')
        if os.path.exists(input_file):
            break
        else:
            print(f"文件 {input_file} 不存在，请重新输入")
    
    # 获取输出文件路径
    default_output = "股票详细信息_" + datetime.now().strftime("%Y%m%d_%H%M%S") + ".csv"
    output_file = input(f"请输入输出文件路径（直接回车使用默认: {default_output}): ").strip().strip('"')
    if not output_file:
        output_file = default_output
    
    # 获取批次大小设置
    batch_input = input(f"请输入批次大小（直接回车使用默认 {BATCH_SIZE}): ").strip()
    if batch_input.isdigit():
        global BATCH_SIZE
        BATCH_SIZE = int(batch_input)
        print(f"批次大小设置为: {BATCH_SIZE}")
    
    # 获取API请求间隔设置
    interval_input = input(f"请输入API请求间隔秒数（直接回车使用默认 {API_REQUEST_INTERVAL}): ").strip()
    if interval_input.replace('.', '').isdigit():
        global API_REQUEST_INTERVAL
        API_REQUEST_INTERVAL = float(interval_input)
        print(f"API请求间隔设置为: {API_REQUEST_INTERVAL} 秒")
    
    print("\n开始处理...")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"批次大小: {BATCH_SIZE}")
    print(f"请求间隔: {API_REQUEST_INTERVAL} 秒")
    print("="*50)
    
    # 处理文件
    result_df = process_stock_csv(input_file, output_file)
    
    if result_df is not None:
        print(f"\n处理完成！结果已保存到: {output_file}")
        print("您可以打开CSV文件查看结果")
        
        # 询问是否显示前几行预览
        preview_input = input("\n是否显示前5行结果预览？(y/n): ").lower().strip()
        if preview_input == 'y':
            print("\n前5行结果预览:")
            print("="*80)
            print(result_df.head().to_string(index=False))
            print("="*80)
    else:
        print("\n处理被中断或失败")
        if os.path.exists(PROGRESS_FILE):
            print("进度已保存，下次运行时可以选择继续处理")

def cleanup_old_backups(days=7):
    """
    清理指定天数前的备份文件
    
    参数:
        days (int): 保留最近几天的备份文件
    """
    try:
        current_time = datetime.now()
        for file in os.listdir('.'):
            if file.startswith('backup_') and file.endswith('.csv'):
                file_time = os.path.getmtime(file)
                file_datetime = datetime.fromtimestamp(file_time)
                if (current_time - file_datetime).days > days:
                    os.remove(file)
                    logging.info(f"已删除旧备份文件: {file}")
    except Exception as e:
        logging.warning(f"清理备份文件时出错: {str(e)}")

def show_help():
    """
    显示帮助信息
    """
    help_text = """
    股票信息批量获取脚本使用说明
    ================================
    
    功能特点：
    1. 分批处理：每处理指定数量的股票就保存一次结果
    2. 断点续传：程序中断后可以从上次停止的地方继续
    3. 实时保存：不用等到全部完成才能看到结果
    4. 自动备份：每个批次都会创建带时间戳的备份文件
    5. 进度跟踪：实时显示处理进度和预计剩余时间
    
    输入文件要求：
    - CSV格式文件
    - 必须包含列：板块,证券代码,证券简称
    - 证券代码应为6位数字（不足6位会自动补零）
    
    输出文件格式：
    - 在原有字段基础上增加：行业,上市时间
    - 自动保存为UTF-8编码，Excel可正常打开
    
    使用建议：
    - 批次大小建议设置为50-100，太大可能导致单次失败损失较多
    - API请求间隔建议2-3秒，避免被服务器限制
    - 处理大量股票时建议在网络稳定的环境下运行
    
    注意事项：
    - 需要网络连接获取股票信息
    - 处理过程中请勿关闭程序，如需中断请使用Ctrl+C
    - 备份文件会占用磁盘空间，可定期清理
    
    故障排除：
    - 如果某只股票获取失败，会标记为"未获取到"并继续处理下一只
    - 如果程序异常退出，重新运行时选择继续上次进度
    - 如果进度文件损坏，删除progress文件重新开始
    """
    print(help_text)

if __name__ == "__main__":
    try:
        # 检查命令行参数
        import sys
        if len(sys.argv) > 1:
            if sys.argv[1] in ['-h', '--help', 'help']:
                show_help()
                sys.exit(0)
            elif sys.argv[1] == 'cleanup':
                cleanup_old_backups()
                print("备份文件清理完成")
                sys.exit(0)
        
        # 清理旧备份文件（可选）
        cleanup_old_backups()
        
        # 运行主程序
        main()
        
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
        print("进度已保存，下次运行时可以选择继续处理")
    except Exception as e:
        logging.error(f"程序运行出错: {str(e)}")
        print(f"程序运行出错: {str(e)}")
    finally:
        print("\n感谢使用股票信息批量获取脚本！")

