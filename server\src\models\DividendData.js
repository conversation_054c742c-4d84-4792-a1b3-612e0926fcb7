const mongoose = require('mongoose');

// 企业分红配股数据模型 - 用于存储企业历史分红配股信息
const dividendDataSchema = new mongoose.Schema({
  // === 基本信息 ===
  // 证券代码
  stockCode: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  
  // 报告期
  reportPeriod: {
    type: Date,
    required: true,
    index: true
  },
  
  // 业绩披露日期
  performanceDisclosureDate: {
    type: Date,
    default: null
  },
  
  
  // === 送转股份信息 ===
  // 送转总比例
  totalStockTransferRatio: {
    type: Number,
    default: null
  },
  
  // 送股比例
  stockBonusRatio: {
    type: Number,
    default: null
  },
  
  // 转股比例
  stockTransferRatio: {
    type: Number,
    default: null
  },
  
  
  // === 现金分红信息 ===
  // 现金分红比例
  cashDividendRatio: {
    type: Number,
    default: null
  },
  
  // 现金分红比例描述
  cashDividendDescription: {
    type: String,
    default: null,
    trim: true
  },
  
  // 股息率
  dividendYield: {
    type: Number,
    default: null
  },
  
  
  // === 每股指标 ===
  // 每股收益
  earningsPerShare: {
    type: Number,
    default: null
  },
  
  // 每股净资产
  bookValuePerShare: {
    type: Number,
    default: null
  },
  
  // 每股公积金
  reserveFundPerShare: {
    type: Number,
    default: null
  },
  
  // 每股未分配利润
  undistributedProfitPerShare: {
    type: Number,
    default: null
  },
  
  
  // === 财务指标 ===
  // 净利润同比增长
  netProfitGrowthYoY: {
    type: Number,
    default: null
  },
  
  // 总股本
  totalShareCapital: {
    type: Number,
    default: null
  },
  
  
  // === 重要日期 ===
  // 预案公告日
  proposalAnnouncementDate: {
    type: Date,
    default: null
  },
  
  // 股权登记日
  equityRegistrationDate: {
    type: Date,
    default: null
  },
  
  // 除权除息日
  exDividendDate: {
    type: Date,
    default: null
  },
  
  
  // === 状态信息 ===
  // 方案进度
  implementationStatus: {
    type: String,
    default: null,
    trim: true
  },
  
  // 最新公告日期
  latestAnnouncementDate: {
    type: Date,
    default: null
  },
  
  
  // 创建时间
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  // 更新时间
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  // 自动添加 createdAt 和 updatedAt
  timestamps: true,
  
  // 设置集合名称
  collection: 'dividend_data'
});

// 复合索引 - 确保同一股票同一报告期的唯一性
dividendDataSchema.index({ 
  stockCode: 1, 
  reportPeriod: 1 
}, { unique: true });

// 普通索引
dividendDataSchema.index({ stockCode: 1, reportPeriod: -1 }); // 按股票代码和报告期排序
dividendDataSchema.index({ reportPeriod: -1 }); // 按报告期排序
dividendDataSchema.index({ exDividendDate: -1 }); // 按除权除息日排序
dividendDataSchema.index({ cashDividendRatio: -1 }); // 按现金分红比例排序
dividendDataSchema.index({ dividendYield: -1 }); // 按股息率排序

// === 字段映射 ===

// 获取中英文字段映射
dividendDataSchema.statics.getFieldMapping = function() {
  return {
    '证券代码': 'stockCode',
    '报告期': 'reportPeriod',
    '业绩披露日期': 'performanceDisclosureDate',
    '送转股份-送转总比例': 'totalStockTransferRatio',
    '送转股份-送股比例': 'stockBonusRatio',
    '送转股份-转股比例': 'stockTransferRatio',
    '现金分红-现金分红比例': 'cashDividendRatio',
    '现金分红-现金分红比例描述': 'cashDividendDescription',
    '现金分红-股息率': 'dividendYield',
    '每股收益': 'earningsPerShare',
    '每股净资产': 'bookValuePerShare',
    '每股公积金': 'reserveFundPerShare',
    '每股未分配利润': 'undistributedProfitPerShare',
    '净利润同比增长': 'netProfitGrowthYoY',
    '总股本': 'totalShareCapital',
    '预案公告日': 'proposalAnnouncementDate',
    '股权登记日': 'equityRegistrationDate',
    '除权除息日': 'exDividendDate',
    '方案进度': 'implementationStatus',
    '最新公告日期': 'latestAnnouncementDate'
  };
};

// 获取字段的中文名称
dividendDataSchema.statics.getChineseFieldName = function(englishField) {
  const mapping = this.getFieldMapping();
  for (const [chinese, english] of Object.entries(mapping)) {
    if (english === englishField) {
      return chinese;
    }
  }
  return englishField;
};

// 获取字段的英文名称
dividendDataSchema.statics.getEnglishFieldName = function(chineseField) {
  const mapping = this.getFieldMapping();
  return mapping[chineseField] || chineseField;
};

// === 静态方法 ===

// 根据股票代码查找分红数据
dividendDataSchema.statics.findByStockCode = function(stockCode, options = {}) {
  const query = { stockCode };
  if (options.startDate && options.endDate) {
    query.reportPeriod = { $gte: new Date(options.startDate), $lte: new Date(options.endDate) };
  }
  
  return this.find(query).sort({ reportPeriod: -1 });
};

// 获取最新的分红数据
dividendDataSchema.statics.findLatestByStockCode = function(stockCode) {
  return this.findOne({ stockCode }).sort({ reportPeriod: -1 });
};

// 根据报告期查找
dividendDataSchema.statics.findByReportPeriod = function(startDate, endDate, options = {}) {
  const query = {
    reportPeriod: { $gte: new Date(startDate), $lte: new Date(endDate) }
  };
  
  if (options.stockCodes && options.stockCodes.length > 0) {
    query.stockCode = { $in: options.stockCodes };
  }
  
  return this.find(query)
    .sort({ reportPeriod: -1, stockCode: 1 })
    .limit(options.limit || 0)
    .skip(options.skip || 0);
};

// 批量查找多个股票的最新分红数据
dividendDataSchema.statics.findLatestByStockCodes = function(stockCodes) {
  return this.aggregate([
    { $match: { stockCode: { $in: stockCodes } } },
    { $sort: { stockCode: 1, reportPeriod: -1 } },
    { $group: {
        _id: '$stockCode',
        latestData: { $first: '$$ROOT' }
      }
    }
  ]);
};

// 查找高股息率排行榜
dividendDataSchema.statics.findTopDividendYield = function(year, limit = 10) {
  const startDate = new Date(`${year}-01-01`);
  const endDate = new Date(`${year}-12-31`);
  
  return this.find({
    reportPeriod: { $gte: startDate, $lte: endDate },
    dividendYield: { $ne: null, $gt: 0 }
  })
  .sort({ dividendYield: -1 })
  .limit(limit);
};

// 查找现金分红比例排行榜
dividendDataSchema.statics.findTopCashDividend = function(year, limit = 10) {
  const startDate = new Date(`${year}-01-01`);
  const endDate = new Date(`${year}-12-31`);
  
  return this.find({
    reportPeriod: { $gte: startDate, $lte: endDate },
    cashDividendRatio: { $ne: null, $gt: 0 }
  })
  .sort({ cashDividendRatio: -1 })
  .limit(limit);
};

// 查找送转股比例排行榜
dividendDataSchema.statics.findTopStockTransfer = function(year, limit = 10) {
  const startDate = new Date(`${year}-01-01`);
  const endDate = new Date(`${year}-12-31`);
  
  return this.find({
    reportPeriod: { $gte: startDate, $lte: endDate },
    totalStockTransferRatio: { $ne: null, $gt: 0 }
  })
  .sort({ totalStockTransferRatio: -1 })
  .limit(limit);
};

// === 实例方法 ===

// 格式化显示
dividendDataSchema.methods.toDisplayString = function() {
  return `${this.stockCode} - ${this.reportPeriod.getFullYear()}年 分红配股`;
};

// 获取分红类型
dividendDataSchema.methods.getDividendType = function() {
  const hasCashDividend = this.cashDividendRatio && this.cashDividendRatio > 0;
  const hasStockTransfer = this.totalStockTransferRatio && this.totalStockTransferRatio > 0;
  
  if (hasCashDividend && hasStockTransfer) return '现金分红+送转股';
  if (hasCashDividend) return '现金分红';
  if (hasStockTransfer) return '送转股';
  return '无分红';
};

// 获取格式化的分红描述
dividendDataSchema.methods.getFormattedDividendDescription = function() {
  const type = this.getDividendType();
  let description = type;
  
  if (this.cashDividendRatio && this.cashDividendRatio > 0) {
    description += ` (每10股派${this.cashDividendRatio}元)`;
  }
  
  if (this.totalStockTransferRatio && this.totalStockTransferRatio > 0) {
    description += ` (每10股送转${this.totalStockTransferRatio}股)`;
  }
  
  return description;
};

// 获取格式化的股息率
dividendDataSchema.methods.getFormattedDividendYield = function() {
  if (this.dividendYield === null || this.dividendYield === 0) return 'N/A';
  return `${(this.dividendYield * 100).toFixed(2)}%`;
};

// 获取格式化的总股本
dividendDataSchema.methods.getFormattedTotalShareCapital = function() {
  if (this.totalShareCapital === null) return 'N/A';
  
  if (this.totalShareCapital >= 100000000) {
    return `${(this.totalShareCapital / 100000000).toFixed(2)}亿股`;
  } else if (this.totalShareCapital >= 10000) {
    return `${(this.totalShareCapital / 10000).toFixed(2)}万股`;
  }
  return `${this.totalShareCapital}股`;
};

// 获取分红摘要
dividendDataSchema.methods.getDividendSummary = function() {
  return {
    type: this.getDividendType(),
    description: this.getFormattedDividendDescription(),
    dividendYield: this.getFormattedDividendYield(),
    totalShareCapital: this.getFormattedTotalShareCapital(),
    eps: this.earningsPerShare ? this.earningsPerShare.toFixed(3) : 'N/A',
    bvps: this.bookValuePerShare ? this.bookValuePerShare.toFixed(3) : 'N/A',
    netProfitGrowth: this.netProfitGrowthYoY ? `${this.netProfitGrowthYoY.toFixed(2)}%` : 'N/A'
  };
};

// 检查是否为有效分红
dividendDataSchema.methods.hasValidDividend = function() {
  return (this.cashDividendRatio && this.cashDividendRatio > 0) || 
         (this.totalStockTransferRatio && this.totalStockTransferRatio > 0);
};

// 中间件：保存前自动更新时间
dividendDataSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// 导出模型
module.exports = mongoose.model('DividendData', dividendDataSchema);