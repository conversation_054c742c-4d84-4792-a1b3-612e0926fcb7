# 基本信息筛选功能API文档

## 概述
在股票筛选API中新增了基于`StockBasicInfo`模型的筛选条件，支持按板块、行业和已上市年份进行筛选。

## API端点
```
POST /api/v1/filter/stocks
```

## 新增的筛选参数

### basicInfoConditions 对象结构

```javascript
{
  "basicInfoConditions": {
    "board": ["板块名称1", "板块名称2"],           // 板块筛选（数组）
    "industry": ["行业名称1", "行业名称2"],        // 行业筛选（数组）
    "listedYearsRange": {                        // 已上市年份范围
      "min": 5,                                  // 最少上市年份
      "max": 15                                  // 最多上市年份
    }
  }
}
```

### 参数说明

#### 1. board (板块筛选)
- **类型**: Array<String>
- **说明**: 筛选指定板块的股票
- **常见值**: 
  - "沪市主板"
  - "深市主板" 
  - "沪市科创板"
  - "深市创业板"
- **示例**: `["沪市主板", "深市主板"]`

#### 2. industry (行业筛选)
- **类型**: Array<String>
- **说明**: 筛选指定行业的股票
- **常见值**: 
  - "银行"
  - "证券"
  - "保险"
  - "软件开发"
  - "计算机应用"
  - "医药制造"
  - 等等
- **示例**: `["银行", "证券", "保险"]`

#### 3. listedYearsRange (已上市年份范围)
- **类型**: Object
- **属性**:
  - `min`: Number | null - 最少上市年份
  - `max`: Number | null - 最多上市年份
- **说明**: 
  - 根据当前年份与上市日期计算已上市年份
  - `min: 5` 表示至少上市5年
  - `max: 15` 表示最多上市15年
  - `null` 表示不限制该边界
- **示例**: 
  - `{"min": 5, "max": 15}` - 上市5-15年的股票
  - `{"min": 10, "max": null}` - 上市10年以上的股票
  - `{"min": null, "max": 3}` - 上市3年以内的股票

## 完整API调用示例

### 1. 仅基本信息筛选
```javascript
{
  "basicInfoConditions": {
    "board": ["沪市主板"],
    "industry": ["银行", "证券"],
    "listedYearsRange": {
      "min": 10,
      "max": null
    }
  }
}
```

### 2. 基本信息 + 当日数据筛选
```javascript
{
  "basicInfoConditions": {
    "board": ["沪市主板", "深市主板"],
    "listedYearsRange": {
      "min": 5,
      "max": null
    }
  },
  "dailyConditions": {
    "peRatioTTMRange": {
      "min": 5,
      "max": 30
    },
    "totalMarketCapRange": {
      "min": 5000000000,
      "max": null
    }
  },
  "targetDate": "2025-09-12"
}
```

### 3. 三维度综合筛选
```javascript
{
  "basicInfoConditions": {
    "board": ["沪市主板"],
    "industry": ["银行"],
    "listedYearsRange": {
      "min": 8,
      "max": null
    }
  },
  "dailyConditions": {
    "peRatioTTMRange": {
      "min": 3,
      "max": 20
    },
    "pbRatioRange": {
      "min": 0.5,
      "max": 2
    }
  },
  "financialConditions": {
    "returnOnEquityRange": {
      "min": 0.1,
      "max": null
    },
    "debtToAssetRatioRange": {
      "min": null,
      "max": 0.8
    }
  },
  "targetDate": "2025-09-12"
}
```

## 响应格式
```javascript
{
  "success": true,
  "message": "股票筛选成功",
  "data": {
    "stockCodes": ["000001", "000002", "600036"],
    "count": 3,
    "timestamp": "2025-09-12T10:00:00.000Z"
  }
}
```

## 筛选逻辑说明

1. **多条件组合**: 所有条件之间为 `AND` 关系，即必须同时满足所有指定条件
2. **数组条件**: `board` 和 `industry` 数组内为 `OR` 关系，即满足数组中任一值即可
3. **交集计算**: 当同时指定多个维度的筛选条件时，最终结果为各维度筛选结果的交集
4. **年份计算**: 已上市年份 = 当前年份 - 上市年份，按自然年计算

## 使用建议

1. **新股筛选**: `listedYearsRange: {min: 1, max: 3}` 筛选上市1-3年的新股
2. **蓝筹股筛选**: 结合板块 `["沪市主板"]` 和上市年份 `{min: 10, max: null}` 筛选老牌蓝筹股
3. **特定行业**: 使用 `industry` 参数筛选特定行业，如 `["银行", "证券", "保险"]` 筛选金融股
4. **综合筛选**: 建议结合多个维度的条件进行筛选，提高选股精准度

## 测试命令

运行专门的基本信息筛选测试：
```bash
node server/test/testBasicInfoFilter.js
```

运行完整的股票筛选测试：
```bash
node server/test/testFilterStock.js
```
