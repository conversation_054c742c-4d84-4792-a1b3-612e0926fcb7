/**
 * 全局错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
  console.error(err.stack);

  // 默认错误状态码为500
  let statusCode = err.statusCode || 500;
  let message = err.message || '服务器内部错误';
  let errors = err.errors || [];

  // 处理MongoDB重复键错误
  if (err.code === 11000) {
    statusCode = 409;
    const field = Object.keys(err.keyValue)[0];
    
    if (field === 'username') {
      message = '该用户名已被使用';
      errors = [{ field, message: '该用户名已被使用' }];
    } else if (field === 'phone') {
      message = '该手机号已被注册';
      errors = [{ field, message: '该手机号已被注册' }];
    } else {
      message = `${field}字段的值已存在`;
      errors = [{ field, message: `${field}字段的值已存在` }];
    }
  }

  // 处理Mongoose验证错误
  if (err.name === 'ValidationError') {
    statusCode = 400;
    message = '请求参数错误';
    errors = Object.values(err.errors).map(val => {
      return {
        field: val.path,
        message: val.message
      };
    });
  }

  // 处理JWT错误
  if (err.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = '无效的认证令牌';
  }

  if (err.name === 'TokenExpiredError') {
    statusCode = 401;
    message = '认证令牌已过期';
  }

  // 错误响应
  res.status(statusCode).json({
    success: false,
    code: statusCode,
    message,
    errors: errors.length > 0 ? errors : undefined,
    timestamp: Date.now()
  });
};

module.exports = errorHandler; 