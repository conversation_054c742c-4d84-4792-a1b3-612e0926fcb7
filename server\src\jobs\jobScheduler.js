const cron = require('node-cron');
const DailyStockDataJob = require('./dailyStockDataJob');

class JobScheduler {
  constructor() {
    this.dailyStockDataJob = new DailyStockDataJob();
    this.scheduledTasks = [];
  }

  // 启动定时任务
  start() {
    console.log('启动定时任务调度器...');
    
    // 每天下午3点10分执行股票数据获取任务
    const dailyTask = cron.schedule('05 15 * * 1-5', async () => {
      console.log('触发每日股票数据任务...');
      try {
        await this.dailyStockDataJob.run();
        console.log('每日股票数据任务执行成功');
      } catch (error) {
        console.error('每日股票数据任务执行失败:', error.message);
      }
    }, {
      scheduled: false,
      timezone: "Asia/Shanghai"
    });

    this.scheduledTasks.push({
      name: 'dailyStockData',
      task: dailyTask,
      schedule: '每工作日下午15:05',
      description: '获取股票数据并导入数据库'
    });

    // 启动所有任务
    this.scheduledTasks.forEach(({ task, name, schedule }) => {
      task.start();
      console.log(`✅ 定时任务 [${name}] 已启动 - 执行时间: ${schedule}`);
    });

    console.log('所有定时任务已启动');
  }

  // 停止定时任务
  stop() {
    console.log('停止定时任务调度器...');
    
    this.scheduledTasks.forEach(({ task, name }) => {
      task.stop();
      console.log(`⏹️ 定时任务 [${name}] 已停止`);
    });

    console.log('所有定时任务已停止');
  }

  // 手动执行任务
  async runDailyStockDataJob() {
    console.log('手动执行每日股票数据任务...');
    try {
      await this.dailyStockDataJob.run();
      console.log('手动执行成功');
      return { success: true, message: '任务执行成功' };
    } catch (error) {
      console.error('手动执行失败:', error.message);
      return { success: false, message: error.message };
    }
  }

  // 获取所有任务状态
  getTasksStatus() {
    return {
      tasks: this.scheduledTasks.map(({ name, schedule, description }) => ({
        name,
        schedule,
        description,
        isRunning: name === 'dailyStockData' ? this.dailyStockDataJob.getStatus().isRunning : false,
        lastRunTime: name === 'dailyStockData' ? this.dailyStockDataJob.getStatus().lastRunTime : null
      }))
    };
  }
}

module.exports = JobScheduler;
